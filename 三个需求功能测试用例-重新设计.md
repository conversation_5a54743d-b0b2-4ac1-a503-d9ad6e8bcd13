# 【您负责的三个需求】功能测试用例

## 需求1：SaaS印章上传前端裁剪改成原图裁剪

### TC-001 小于1M图片勾选前端裁剪触发原图裁剪

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统触发前端原图裁剪逻辑；原图完整保存到服务器；生成对应的截图版本；页面显示"前端裁剪处理成功"提示

### TC-002 小于1M图片未勾选前端裁剪使用原有逻辑

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 不勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

### TC-003 大于1M图片勾选前端裁剪不触发原图裁剪

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备1.2MB的PNG格式印章图片文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择1.2MB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

### TC-004 1MB边界值图片勾选前端裁剪验证

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备正好1MB的PNG格式印章图片文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择正好1MB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

### TC-005 非图片格式文件勾选前端裁剪验证

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PDF格式文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PDF格式文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统提示"文件格式不支持，请上传图片格式文件"；不触发前端裁剪逻辑；上传操作失败

### TC-006 前端裁剪过程异常处理验证

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟前端JavaScript异常环境

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统捕获前端裁剪异常；显示"前端裁剪失败，已自动切换到后端处理"提示；图片按原有逻辑正常处理完成

## 需求2：非标API支持法人授权静默签

### TC-007 原有参数调用接口兼容性验证

**前置条件：** 已配置非标API接口；准备企业授权相关材料；准备原有参数组合

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 传入原有参数组合（不包含authType和legalRepAccountId）
3. 上传企业授权书文件
4. 提交授权申请

**预期结果：** 接口调用成功返回200状态码；系统按企业授权逻辑处理；授权书文件上传成功；授权申请提交成功

### TC-008 authType为ENTERPRISE不传legalRepAccountId验证

**前置条件：** 已配置非标API接口；准备企业授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"ENTERPRISE"
3. 不传入legalRepAccountId参数
4. 上传企业授权书文件
5. 提交授权申请

**预期结果：** 接口调用成功返回200状态码；系统识别为企业授权类型；授权书文件上传成功；按企业授权流程处理

### TC-009 authType为LEGAL_PERSON传入有效legalRepAccountId验证

**前置条件：** 已配置非标API接口；准备法人授权相关材料；准备有效的法人个人账号ID

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 设置legalRepAccountId参数为有效的法人个人账号ID
4. 上传法人线下授权书文件
5. 提交授权申请

**预期结果：** 接口调用成功返回200状态码；系统识别为法人授权类型；法人账号ID正确关联；授权书文件上传成功；进入法人授权审核流程

### TC-010 authType为LEGAL_PERSON不传legalRepAccountId异常验证

**前置条件：** 已配置非标API接口；准备法人授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 不传入legalRepAccountId参数
4. 尝试提交授权申请

**预期结果：** 接口返回400错误状态码；返回错误信息"legalRepAccountId参数为必填项"；授权申请提交失败

### TC-011 authType为无效值异常验证

**前置条件：** 已配置非标API接口；准备授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"INVALID_TYPE"
3. 尝试提交授权申请

**预期结果：** 接口返回400错误状态码；返回错误信息"authType参数值无效，支持ENTERPRISE或LEGAL_PERSON"；授权申请提交失败

### TC-012 legalRepAccountId为不存在账号ID异常验证

**前置条件：** 已配置非标API接口；准备法人授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 设置legalRepAccountId参数为不存在的账号ID
4. 尝试提交授权申请

**预期结果：** 接口返回400错误状态码；返回错误信息"legalRepAccountId对应的账号不存在"；授权申请提交失败

### TC-013 法人授权审核通过后静默签验证

**前置条件：** 法人授权已审核通过；法人个人账号已创建；准备需要签署的合同

**操作步骤：**
1. 使用法人个人账号创建签署流程
2. 设置autosign参数为true
3. 指定法人印章
4. 发起静默签署

**预期结果：** 系统验证法人静默签授权有效；静默签署自动执行完成；合同状态更新为已签署；文档显示法人印章签署效果

### TC-014 企业授权和法人授权静默签区分验证

**前置条件：** 同时存在企业授权和法人授权；准备需要签署的合同

**操作步骤：**
1. 使用企业授权账号创建签署流程
2. 设置autosign参数为true
3. 发起静默签署
4. 使用法人授权账号创建签署流程
5. 设置autosign参数为true
6. 发起静默签署

**预期结果：** 系统正确区分企业静默签授权和法人静默签授权；两种授权类型的静默签署都正常执行；授权类型判断准确无误

## 需求3：法人章创建流程优化

### TC-015 优化后法人章创建流程验证

**前置条件：** 用户已登录SaaS系统；用户具有法人章创建权限；准备法人基本信息和身份证明文件

**操作步骤：**
1. 进入法人章创建页面
2. 填写法人姓名
3. 填写法人身份证号
4. 填写法人联系方式
5. 上传法人身份证明文件
6. 选择法人章样式
7. 点击"提交创建申请"按钮

**预期结果：** 页面布局美观合理；信息填写界面友好直观；文件上传交互流畅；章样式选择直观；申请提交流程简化；整体用户体验提升

### TC-016 PC端授权书预览功能验证

**前置条件：** 用户在PC端Chrome浏览器中访问法人章创建页面；系统中存在授权书文件

**操作步骤：**
1. 进入法人章创建页面
2. 点击"查看授权书"链接

**预期结果：** 授权书预览窗口正常打开；授权书内容清晰显示；预览界面无下载按钮；预览界面无另存为功能

### TC-017 PC端授权书下载限制验证

**前置条件：** 用户在PC端Chrome浏览器中打开授权书预览界面

**操作步骤：**
1. 在授权书预览界面右键点击
2. 查看右键菜单选项

**预期结果：** 右键菜单中无"图片另存为"选项；右键菜单中无"复制图片地址"选项；无法通过右键菜单下载授权书

### TC-018 H5端授权书预览功能验证

**前置条件：** 用户在移动设备H5浏览器中访问法人章创建页面；系统中存在授权书文件

**操作步骤：**
1. 进入法人章创建页面
2. 点击"查看授权书"链接

**预期结果：** 授权书预览界面在移动端正常显示；预览内容在小屏幕上清晰可读；预览界面适配移动端屏幕尺寸；无下载或保存功能

### TC-019 H5端授权书保存限制验证

**前置条件：** 用户在移动设备H5浏览器中打开授权书预览界面

**操作步骤：**
1. 在授权书预览界面长按内容
2. 查看长按后的操作选项

**预期结果：** 长按后无"保存图片"选项；长按后无"复制"选项；无法通过长按操作保存授权书到本地

### TC-020 法人章创建状态转换验证

**前置条件：** 用户已登录SaaS系统；用户具有法人章创建权限

**操作步骤：**
1. 进入法人章创建页面（初始状态）
2. 开始填写法人基本信息（进入信息填写状态）
3. 完成信息填写点击下一步（进入文件上传状态）
4. 完成文件上传点击下一步（进入样式选择状态）
5. 完成样式选择点击提交（进入提交审核状态）

**预期结果：** 每个状态转换顺畅无阻；状态转换时页面UI正确更新；无法跳过必要的状态直接提交；状态回退功能正常工作

### TC-021 跨浏览器兼容性验证

**前置条件：** 准备Chrome、Firefox、Safari浏览器；相同的测试数据

**操作步骤：**
1. 在Chrome浏览器中完成法人章创建流程
2. 在Firefox浏览器中完成法人章创建流程
3. 在Safari浏览器中完成法人章创建流程

**预期结果：** 所有浏览器中页面显示一致；功能操作无异常；用户体验保持一致；无浏览器特有的兼容性问题

## 异常场景测试用例

### TC-022 网络中断时前端裁剪异常处理

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟网络中断环境

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮（此时网络中断）

**预期结果：** 系统检测到网络异常；显示"网络连接异常，请检查网络后重试"提示；提供重试按钮；不会丢失已填写的信息

### TC-023 服务器存储空间不足异常处理

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟服务器存储空间不足

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 勾选"使用前端裁剪"选项
6. 点击"确认上传"按钮

**预期结果：** 系统检测到存储空间不足；显示"服务器存储空间不足，请稍后重试"提示；上传操作失败；系统记录异常日志

### TC-024 AI审核法人授权书失败处理

**前置条件：** 已配置非标API接口；准备模糊不清的法人授权书图片

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 设置legalRepAccountId参数为有效的法人个人账号ID
4. 上传模糊不清的法人授权书图片
5. 提交授权申请

**预期结果：** AI审核识别授权书质量问题；审核结果返回失败状态；返回具体失败原因"授权书图片不清晰，请重新上传"；提供重新上传指引

### TC-025 法人授权过期后静默签失败处理

**前置条件：** 法人授权已过期；准备需要签署的合同

**操作步骤：**
1. 使用过期法人授权的账号创建签署流程
2. 设置autosign参数为true
3. 尝试发起静默签署

**预期结果：** 系统检测到法人授权已过期；静默签署操作失败；返回错误信息"法人授权已过期，请重新进行授权"；签署流程状态保持未签署

### TC-026 授权书文件损坏时预览异常处理

**前置条件：** 用户在PC端访问法人章创建页面；系统中存在损坏的授权书文件

**操作步骤：**
1. 进入法人章创建页面
2. 点击"查看授权书"链接

**预期结果：** 系统检测到授权书文件损坏；显示"授权书文件异常，无法预览"提示；提供联系客服的引导信息；不会导致页面崩溃

## 性能测试用例

### TC-027 前端裁剪并发处理性能验证

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备50个800KB的PNG格式印章图片文件

**操作步骤：**
1. 同时打开50个浏览器标签页
2. 在每个标签页中进入印章管理页面
3. 在每个标签页中上传800KB图片文件
4. 在每个标签页中勾选"使用前端裁剪"选项
5. 同时点击所有标签页的"确认上传"按钮

**预期结果：** 50个并发请求的成功率大于95%；单个前端裁剪处理时间小于10秒；系统CPU和内存使用率在合理范围内；无系统崩溃或异常

### TC-028 静默签批量处理性能验证

**前置条件：** 法人授权已审核通过；准备100个需要签署的合同

**操作步骤：**
1. 使用法人个人账号批量创建100个签署流程
2. 为所有签署流程设置autosign参数为true
3. 同时发起100个静默签署请求

**预期结果：** 100个静默签署请求的成功率大于95%；单个静默签署处理时间小于5秒；系统响应时间稳定；无性能瓶颈或超时异常

### TC-029 法人章创建页面加载性能验证

**前置条件：** 用户已登录SaaS系统；用户具有法人章创建权限；网络环境正常

**操作步骤：**
1. 清空浏览器缓存
2. 访问法人章创建页面
3. 记录页面完全加载时间

**预期结果：** 页面首次加载时间小于3秒；页面资源加载完整无缺失；页面交互响应时间小于1秒；用户体验流畅

## 安全测试用例

### TC-030 授权书URL直接访问安全验证

**前置条件：** 用户已获取授权书的预览URL地址

**操作步骤：**
1. 复制授权书预览界面的URL地址
2. 在新的浏览器标签页中直接访问该URL
3. 尝试在未登录状态下访问该URL

**预期结果：** 直接访问URL返回403权限错误；未登录状态下访问返回401认证错误；无法绕过权限控制直接访问授权书；安全控制机制有效

### TC-031 法人授权信息篡改安全验证

**前置条件：** 已配置非标API接口；准备法人授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 设置legalRepAccountId参数为他人的法人个人账号ID
4. 尝试提交授权申请

**预期结果：** 系统验证法人身份信息不匹配；授权申请被拒绝；返回错误信息"法人身份验证失败"；记录安全异常日志

### TC-032 静默签权限越权验证

**前置条件：** 用户A具有企业授权；用户B具有法人授权

**操作步骤：**
1. 使用用户A的企业授权账号
2. 尝试调用用户B的法人静默签功能
3. 设置autosign参数为true
4. 尝试发起静默签署

**预期结果：** 系统检测到权限不匹配；静默签署操作被拒绝；返回错误信息"无权限执行该操作"；记录越权尝试日志

## 兼容性测试用例

### TC-033 不同操作系统兼容性验证

**前置条件：** 准备Windows、macOS、Linux操作系统环境；相同的测试数据

**操作步骤：**
1. 在Windows系统Chrome浏览器中完成印章前端裁剪功能测试
2. 在macOS系统Safari浏览器中完成印章前端裁剪功能测试
3. 在Linux系统Firefox浏览器中完成印章前端裁剪功能测试

**预期结果：** 所有操作系统中功能表现一致；前端裁剪处理结果相同；无操作系统特有的兼容性问题；用户体验保持统一

### TC-034 移动设备兼容性验证

**前置条件：** 准备iOS和Android移动设备；相同的测试数据

**操作步骤：**
1. 在iOS设备Safari浏览器中测试法人章创建流程
2. 在Android设备Chrome浏览器中测试法人章创建流程
3. 验证授权书预览功能在移动端的表现

**预期结果：** 移动端页面布局适配良好；触摸操作响应正常；授权书预览在移动端清晰显示；功能完整性与PC端一致

### TC-035 不同屏幕分辨率适配验证

**前置条件：** 准备1920x1080、1366x768、1280x720分辨率显示器；相同的测试数据

**操作步骤：**
1. 在1920x1080分辨率下测试法人章创建页面显示
2. 在1366x768分辨率下测试法人章创建页面显示
3. 在1280x720分辨率下测试法人章创建页面显示

**预期结果：** 所有分辨率下页面布局正常；文字和按钮大小适中；功能区域完整显示；用户操作体验良好

## 集成测试用例

### TC-036 印章前端裁剪与法人章创建集成验证

**前置条件：** 用户已登录SaaS系统；用户具有法人章创建权限；准备800KB的PNG格式印章图片文件

**操作步骤：**
1. 进入法人章创建页面
2. 填写法人基本信息
3. 上传法人身份证明文件
4. 上传800KB的法人印章图片
5. 勾选"使用前端裁剪"选项
6. 选择法人章样式
7. 提交法人章创建申请

**预期结果：** 法人章创建流程正常执行；印章图片前端裁剪功能正常工作；前端裁剪后的印章正确保存；法人章创建成功

### TC-037 法人授权与静默签完整流程集成验证

**前置条件：** 已配置非标API接口；准备法人授权相关材料；准备需要签署的合同

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 设置authType参数为"LEGAL_PERSON"
3. 设置legalRepAccountId参数为有效的法人个人账号ID
4. 上传法人线下授权书文件
5. 等待AI审核通过
6. 使用法人个人账号创建签署流程
7. 设置autosign参数为true
8. 发起静默签署

**预期结果：** 法人授权申请成功提交；AI审核正常通过；法人授权状态正确更新；静默签署功能正常执行；合同签署成功完成

### TC-038 三个需求端到端业务流程集成验证

**前置条件：** 用户已登录SaaS系统；具有完整业务权限；准备完整的测试数据

**操作步骤：**
1. 使用优化后的法人章创建流程创建法人章
2. 在创建过程中上传印章图片并使用前端裁剪功能
3. 预览授权书（验证只能查看不能下载）
4. 完成法人章创建
5. 通过非标API配置法人授权静默签
6. 创建需要法人签署的合同
7. 执行静默签署流程

**预期结果：** 整个业务流程端到端执行成功；各功能模块协调工作无冲突；法人章创建、印章裁剪、授权配置、静默签署各环节正常；最终合同签署完成

### TC-039 数据一致性集成验证

**前置条件：** 完成法人章创建和授权配置；准备签署测试

**操作步骤：**
1. 查询法人章创建记录中的印章信息
2. 查询法人授权记录中的账号信息
3. 创建签署流程并执行静默签
4. 查询签署记录中的印章和授权信息
5. 对比各模块中的数据一致性

**预期结果：** 法人章信息在各模块中保持一致；印章数据在创建和签署中一致；法人授权信息准确传递；数据完整性在整个流程中得到保障

## 回归测试用例

### TC-040 现有印章功能回归验证

**前置条件：** 用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件

**操作步骤：**
1. 进入印章管理页面
2. 点击"创建印章"按钮
3. 点击"上传印章图片"按钮
4. 选择800KB的PNG格式印章图片文件
5. 不勾选"使用前端裁剪"选项（使用原有逻辑）
6. 点击"确认上传"按钮

**预期结果：** 原有印章创建功能完全正常；图片按原有逻辑正确处理；印章创建成功；功能表现与改动前一致

### TC-041 现有企业授权功能回归验证

**前置条件：** 已配置非标API接口；准备企业授权相关材料

**操作步骤：**
1. 调用非标API企业线下授权接口
2. 使用原有参数组合（不传入新增参数）
3. 上传企业授权书文件
4. 提交授权申请
5. 使用企业授权进行签署操作

**预期结果：** 原有企业授权功能完全正常；授权流程与改动前一致；签署功能正常工作；现有客户使用无影响

### TC-042 现有签署流程回归验证

**前置条件：** 已配置签署环境；准备需要签署的合同；使用原有签署方式

**操作步骤：**
1. 创建普通签署流程（非静默签）
2. 添加签署人信息
3. 发起签署流程
4. 签署人完成在线签署
5. 验证签署结果

**预期结果：** 原有签署流程功能完全正常；签署人操作体验无变化；签署结果正确生成；功能稳定性保持

### TC-043 系统整体稳定性回归验证

**前置条件：** 系统部署完成；准备完整的回归测试数据

**操作步骤：**
1. 执行核心业务流程测试
2. 执行高频使用功能测试
3. 执行边界条件测试
4. 监控系统性能指标
5. 检查系统日志异常

**预期结果：** 系统整体功能稳定；核心业务流程正常；性能指标在正常范围内；无新增异常日志；系统可用性保持

## 用例执行优先级说明

### P0级用例（冒烟测试）
- TC-001：小于1M图片勾选前端裁剪触发原图裁剪
- TC-007：原有参数调用接口兼容性验证
- TC-009：authType为LEGAL_PERSON传入有效legalRepAccountId验证
- TC-015：优化后法人章创建流程验证
- TC-016：PC端授权书预览功能验证
- TC-040：现有印章功能回归验证
- TC-041：现有企业授权功能回归验证

### P1级用例（核心功能）
- TC-002至TC-006：印章前端裁剪相关功能
- TC-008、TC-010至TC-014：法人授权静默签核心功能
- TC-017至TC-021：法人章创建流程优化核心功能
- TC-036至TC-039：集成测试用例

### P2级用例（重要功能）
- TC-022至TC-026：异常场景测试
- TC-027至TC-029：性能测试
- TC-030至TC-032：安全测试
- TC-042至TC-043：回归测试

### P3级用例（边缘场景）
- TC-033至TC-035：兼容性测试

## 测试数据准备说明

### 印章图片文件
- 100KB PNG格式文件：用于小文件测试
- 800KB PNG格式文件：用于标准测试
- 1MB PNG格式文件：用于边界值测试
- 1.2MB PNG格式文件：用于超限测试
- 800KB PDF格式文件：用于格式异常测试
- 损坏的图片文件：用于异常测试

### 法人账号信息
- 有效法人个人账号ID：用于正常流程测试
- 不存在的账号ID：用于异常测试
- 过期授权的账号ID：用于过期测试

### 授权书文件
- 标准格式法人授权书：用于正常审核测试
- 模糊不清授权书：用于AI审核失败测试
- 损坏的授权书文件：用于异常测试

### 测试环境
- 开发环境：用于功能开发和基础测试
- 测试环境：用于完整功能测试和集成测试
- 预发环境：用于回归测试和性能测试
- 生产环境：用于最终验收测试
