<!DOCTYPE html>
<!-- saved from url=(0253)http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.1.1SaaS%E7%BA%B8%E8%B4%A8%E5%8D%B0%E7%AB%A0%E5%AE%A1%E6%A0%B8%E6%83%85%E5%86%B5%E5%88%86%E6%9E%90(%E5%90%8E%E7%AB%AF%E5%9F%8B%E7%82%B9) -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                            <title>********签署迭代 - 2.技术中心 - 天谷百科</title>
    
        

                        
    
                        
    

                
    
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=IE7">

<meta id="confluence-context-path" name="confluence-context-path" content="">
<meta id="confluence-base-url" name="confluence-base-url" content="http://wiki.timevale.cn:8081">

<meta id="atlassian-token" name="atlassian-token" content="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">


<meta id="confluence-space-key" name="confluence-space-key" content="PRODUCT">
<script type="text/javascript">
        var contextPath = '';
</script>

    

    <meta name="confluence-request-time" content="1753757206435">
        
    
        
            <meta name="ajs-is-space-admin" content="false"> <meta name="ajs-has-space-config" content="false">
            <meta name="ajs-show-space-welcome-dialog" content="true">
            <style>.ia-fixed-sidebar, .ia-splitter-left {width: 285px;}.theme-default .ia-splitter #main {margin-left: 285px;}.ia-fixed-sidebar {visibility: hidden;}</style>
            <meta name="ajs-use-keyboard-shortcuts" content="true">
            <meta name="ajs-discovered-plugin-features" content="{&quot;com.atlassian.confluence.plugins.confluence-page-banner&quot;:[&quot;recently-work-on-contributor-lozenge&quot;],&quot;com.atlassian.confluence.plugins.confluence-dashboard&quot;:[&quot;recently-worked-on-drafts&quot;]}">
            <meta name="ajs-keyboardshortcut-hash" content="5d0be2eb1aace87b32328c0177e65e28">
            <meta name="ajs-is-confluence-admin" content="false">
            <meta name="ajs-connection-timeout" content="10000">
            
    
    
            <meta name="ajs-page-title" content="********签署迭代">
            <meta name="ajs-latest-published-page-title" content="********签署迭代">
            <meta name="ajs-space-name" content="2.技术中心">
            <meta name="ajs-page-id" content="*********">
            <meta name="ajs-latest-page-id" content="*********">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-parent-page-title" content="25年Q3迭代">
            <meta name="ajs-parent-page-id" content="217674785">
            <meta name="ajs-space-key" content="PRODUCT">
            <meta name="ajs-max-number-editors" content="12">
            <meta name="ajs-macro-placeholder-timeout" content="5000">
            <meta name="ajs-jira-metadata-count" content="0">
            <meta name="ajs-from-page-title" content="">
            <meta name="ajs-can-remove-page" content="false">
            <meta name="ajs-can-remove-page-hierarchy" content="false">
            <meta name="ajs-browse-page-tree-mode" content="view">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-user-display-name" content="桃浪">
            <meta name="ajs-context-path" content="">
            <meta name="ajs-base-url" content="http://wiki.timevale.cn:8081">
            <meta name="ajs-version-number" content="6.13.4">
            <meta name="ajs-build-number" content="7901">
            <meta name="ajs-remote-user" content="taolang">
            <meta name="ajs-remote-user-key" content="2c9d835180d15caa0181f5976c180050">
            <meta name="ajs-remote-user-has-licensed-access" content="true">
            <meta name="ajs-remote-user-has-browse-users-permission" content="true">
            <meta name="ajs-current-user-fullname" content="桃浪">
            <meta name="ajs-current-user-avatar-url" content="/download/attachments/153249279/user-avatar">
            <meta name="ajs-current-user-avatar-uri-reference" content="/download/attachments/153249279/user-avatar">
            <meta name="ajs-static-resource-url-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/_">
            <meta name="ajs-global-settings-attachment-max-size" content="209715200">
            <meta name="ajs-user-locale" content="zh_CN">
            <meta name="ajs-enabled-dark-features" content="site-wide.shared-drafts,site-wide.synchrony,confluence.view.edit.transition,confluence-inline-comments-resolved,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareContentEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.mentions.api.ConfluenceMentionEvent,frontend.editor.v4.compatibility,notification.plugin.api.enabled.com.atlassian.confluence.event.events.security.ForgotPasswordEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.tasklist.event.SendTaskEmailEvent,file-annotations,confluence.efi.onboarding.rich.space.content,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessGrantedEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageMoveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.follow.FollowEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentReplyEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostCreateEvent,lucene.caching.filter,confluence.table.resizable,notification.batch,confluence-inline-comments-rich-editor,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostUpdateEvent,site-wide.synchrony.opt-in,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageCreatedEvent,mobile.supported.version,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentMentionUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.hipchat.api.events.HipChatUserMapped,quick-reload-inline-comments-flags,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostMovedEvent,clc.quick.create,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageUpdateEvent,cql.search.screen,nps.survey.inline.dialog,confluence.efi.onboarding.new.templates,pdf-preview,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageMovedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareCustomEvent,previews.sharing,previews.versions,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.attachment.AttachmentBatchUploadCompletedEvent,collaborative-audit-log,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingLessUsersEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentRemoveEvent,confluence.wrap.macro,previews.conversion-service,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentUpdateEvent,editor.ajax.save,graphql,read.only.mode,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageEditedEvent,previews.trigger-all-file-types,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentResolveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.like.LikeCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentCreateEvent,attachment.extracted.text.extractor,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessRequestedEvent,previews.sharing.pushstate,file-annotations.likes,v2.content.name.searcher,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingNoSpaceCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareDraftEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareAttachmentEvent,confluence-inline-comments,confluence-inline-comments-dangling-comment">
            <meta name="ajs-atl-token" content="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">
            <meta name="ajs-confluence-flavour" content="VANILLA">
            <meta name="ajs-user-date-pattern" content="yyyy-M-d">
            <meta name="ajs-access-mode" content="READ_WRITE">
            <meta name="ajs-render-mode" content="READ_WRITE">
            <meta name="ajs-date.format" content="MMM dd, yyyy">
    
    <link rel="shortcut icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">
    <link rel="icon" type="image/x-icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">

<link rel="search" type="application/opensearchdescription+xml" href="http://wiki.timevale.cn:8081/opensearch/osd.action" title="天谷百科">
    
                    
            <meta name="ajs-create-issue-metadata-show-discovery" content="true">
            

    <script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\u0022\u0022";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-hipchat-integration-plugin:discovery-javascript-data.link-active"]="{\u0022linkActive\u0022:false,\u0022conditionsMet\u0022:true,\u0022admin\u0022:false}";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-feature-discovery-plugin:confluence-feature-discovery-plugin-resources.test-mode"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-help-paths"]="{\u0022entries\u0022:{\u0022applinks.docs.root\u0022:\u0022https://confluence.atlassian.com/display/APPLINKS-054/\u0022,\u0022applinks.docs.diagnostics.troubleshoot.sslunmatched\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthsignatureinvalid\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthtimestamprefused\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.delete.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.adding.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administration.guide\u0022:\u0022Application+Links+Documentation\u0022,\u0022applinks.docs.oauth.security\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.troubleshoot.application.links\u0022:\u0022Troubleshoot+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownerror\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.trusted.apps\u0022:\u0022Configuring+Trusted+Applications+authentication+for+an+application+link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelunsupported\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.ssluntrusted\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownhost\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.delete.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.link.applications\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthproblem\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.migration\u0022:\u0022Update+application+links+to+use+OAuth\u0022,\u0022applinks.docs.relocate.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administering.entity.links\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.upgrade.application.link\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.connectionrefused\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.oauth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.insufficient.remote.permission\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.configuring.application.link.auth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics\u0022:\u0022Application+links+diagnostics\u0022,\u0022applinks.docs.configured.authentication.types\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.adding.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unexpectedresponse\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.basic\u0022:\u0022Configuring+Basic+HTTP+Authentication+for+an+Application+Link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelmismatch\u0022:\u0022OAuth+troubleshooting+guide\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-types"]="{\u0022crowd\u0022:\u0022\u4eba\u7fa4\u0022,\u0022confluence\u0022:\u0022Confluence\u0022,\u0022fecru\u0022:\u0022Fisheye / Crucible\u0022,\u0022stash\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u0022,\u0022jira\u0022:\u0022Jira\u0022,\u0022refapp\u0022:\u0022\u76f8\u5173\u5e94\u7528\u7a0b\u5e8f\u0022,\u0022bamboo\u0022:\u0022\u7af9\u0022,\u0022generic\u0022:\u0022\u901a\u7528\u5e94\u7528\u7a0b\u5e8f\u0022}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.entity-types"]="{\u0022singular\u0022:{\u0022refapp.charlie\u0022:\u0022Charlie\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022},\u0022plural\u0022:{\u0022refapp.charlie\u0022:\u0022\u67e5\u7406\u65af\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u96c6\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.authentication-types"]="{\u0022com.atlassian.applinks.api.auth.types.BasicAuthenticationProvider\u0022:\u0022\u57fa\u672c\u8bbf\u95ee\u0022,\u0022com.atlassian.applinks.api.auth.types.TrustedAppsAuthenticationProvider\u0022:\u0022\u4fe1\u4efb\u7684\u5e94\u7528\u65e0\u6548\u0022,\u0022com.atlassian.applinks.api.auth.types.CorsAuthenticationProvider\u0022:\u0022\u6b4c\u73e5\u0022,\u0022com.atlassian.applinks.api.auth.types.OAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthWithImpersonationAuthenticationProvider\u0022:\u0022Oauth\u0022}";
WRM._unparsedData["com.atlassian.confluence.plugins.synchrony-interop:************************loader.synchrony-status"]="false";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-license-banner:confluence-license-banner-resources.license-details"]="{\u0022daysBeforeLicenseExpiry\u0022:0,\u0022daysBeforeMaintenanceExpiry\u0022:0,\u0022showLicenseExpiryBanner\u0022:false,\u0022showMaintenanceExpiryBanner\u0022:false,\u0022renewUrl\u0022:null,\u0022salesEmail\u0022:null}";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lt+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lte+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(1).css" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d29c9770d1d3dafb92b965ac248fc207-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/bcb04377762ea4527933daff33201ece/_/download/contextbatch/css/atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super/batch.css?conditionalComment=lt+IE+9&amp;confluence.table.resizable=true&amp;highlightactions=true&amp;hostenabled=true" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<script type="text/javascript" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch.js" data-wrm-key="_super" data-wrm-batch-type="context"></script>
<script type="text/javascript" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(2).js" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context"></script><style type="text/css">/* Applied to body so that the parent document does not scroll while the full screen IFrame dialog is open. */
.spark-no-scroll {
  overflow: hidden; }

.YvpxiV5x7vy1i-QG5gfAe {
  /*
     * Styles for iframe (full screen) dialog emulating Atlassian Connect
     */
  /*
     * Styling to imitate dialog chrome styling from Atlassian Connect (at least
     * one specific version).
     * If an add-on would like to override these styles, it could be done by
     * prefixing with the predictable 'app-id' that will be added as an id
     * to the main dialog wrapper div.
     */ }
  .YvpxiV5x7vy1i-QG5gfAe.spark-app-iframe {
    border: none; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper {
    position: fixed;
    z-index: 9000;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    margin: 0;
    padding: 0;
    overflow: hidden;
    /*
         * Change background on fullscreen dialogs to grey.
         * There are two types of fullscreen dialogs in Atlassian Connect:
         * Dialogs with width/height set to 100% have a half-transparent black background,
         * dialogs with size set to "fullscreen" have a grey background.
         */ }
    .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper.spark-fullscreen-dialog {
      background-color: #f5f5f5; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper {
    position: static;
    width: 100%;
    height: 100%;
    overflow: auto;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper.spark-fullscreen-haschrome {
    height: calc(100% - 51px); }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome {
    width: 100%;
    background: black;
    height: 50px;
    margin: 0;
    padding: 0;
    border: none;
    border-bottom: 1px solid #cccccc; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap {
    float: right;
    margin: 0;
    padding: 0;
    height: 50px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-icon-small::before {
    margin-left: 16px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-button {
    height: 50px;
    width: 50px;
    border: none;
    border-left: 1px solid #333;
    border-radius: 0;
    float: left;
    margin-left: 0;
    background: black;
    color: white; }
</style>

    

        
    

        
        <meta name="ajs-site-title" content="天谷百科">
            
    

    
                <link rel="canonical" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
        <link rel="shortlink" href="http://wiki.timevale.cn:8081/x/viM0DQ">
    <meta name="wikilink" content="[PRODUCT:********签署迭代]">
    <meta name="page-version" content="43">
    <meta name="ajs-page-version" content="43">

<script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(3).js"></script><link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(4).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(5).js"></script><link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(6).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(7).js"></script><style>
        .inject_highlight_overlay {
            position: fixed;
            background-color: #e6171759;
            border-radius: 3px;
            z-index: 99999999999998;
            pointer-events: none
        }

        .inject_highlight_overlay-sub {
            background-color: #e6171733
        }

        .inject_highlight_overlay_content {
            position: fixed;
            padding: 4px 8px;
            background-color: #fff;
            background-clip: padding-box;
            border-radius: 3px;
            color: #333;
            text-align: center;
            border: 1px solid rgba(65, 184, 131, .5);
            font-size: 11px;
            font-family: monospace;
            z-index: 99999999999999;
            pointer-events: none
        }
    </style><link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/com.atlassian.confluence.ext.newcode-macro-plugin_sh-theme-confluence.css"><script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(8).js"></script>
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.129">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="221520831">
            <meta name="ajs-draft-share-id" content="0b8e5a4a-12c9-43e4-951b-05ff873dd6b7">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTgwZDE1Y2FhMDE4MWY1OTc2YzE4MDA1MCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjIxNTIwODMwLXRpdGxlIjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMjE1MjA4MzAiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE4MGQxNWNhYTAxODFmNTk3NmMxODAwNTAifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTUzMjQ5Mjc5XC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0YW9sYW5nIiwiZnVsbG5hbWUiOiLmoYPmtaoifSwiaXNzIjoiU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZiIsImV4cCI6MTc1Mzg0MzYwNywiaWF0IjoxNzUzNzU3MjA3fQ.12SG2xyB-z-eJZ27VlLEoZCcPExIE8dy1mIra-O0sEA">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="1753842707">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    <link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(9).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(10).js"></script><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/com.atlassian.confluence.plugins.confluence-previews_confluence-previews-css.css"><link rel="stylesheet" type="text/css" href="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(11).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/batch(12).js"></script><style type="text/css">.tooltip {
  text-indent: initial;
  width: auto;
  display: inline-block;
  text-transform: none;
  font-family: Arial, sans-serif;
  font-size: 12px;
  line-height: 20px;
  border-radius: 3px;
  background: rgba(51, 51, 51, 0.9);
  color: #fff;
  left: 50%;
  white-space: nowrap;
  margin-bottom: 15px;
  opacity: 0;
  padding: 5px 10px;
  pointer-events: none;
  position: absolute;
  z-index: 999;
  max-width: 200px;
  -webkit-transition: all 0s 0.3s ease-out;
  -moz-transition: all 0s 0.3s ease-out;
  -ms-transition: all 0s 0.3s ease-out;
  -o-transition: all 0s 0.3s ease-out;
  transition: all 0s 0.3s ease-out;
  /* This bridges the gap so you can mouse into the tooltip without it disappearing */
  /* CSS Triangles */
}
.tooltip:before {
  bottom: -20px;
  content: " ";
  display: block;
  height: 20px;
  left: 0;
  position: absolute;
  width: 100%;
}
.tooltip:after {
  border-left: solid transparent 4px;
  border-right: solid transparent 4px;
  content: " ";
  height: 0;
  left: 50%;
  margin-left: -4px;
  position: absolute;
  width: 0;
}
.tooltip.tooltip-s {
  bottom: 100%;
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
.tooltip.tooltip-s:after {
  border-top: solid rgba(51, 51, 51, 0.9) 4px;
  bottom: -4px;
}
.tooltip.tooltip-n {
  top: 100%;
  -webkit-transform: translateY(0) translateX(-50%);
  -moz-transform: translateY(0) translateX(-50%);
  -ms-transform: translateY(0) translateX(-50%);
  -o-transform: translateY(0) translateX(-50%);
  transform: translateY(0) translateX(-50%);
}
.tooltip.tooltip-n:after {
  border-bottom: solid rgba(51, 51, 51, 0.9) 4px;
  top: -4px;
}
*:hover > .tooltip {
  opacity: 1;
  pointer-events: auto;
}
*:hover > .tooltip.tooltip-s {
  -webkit-transform: translateY(0px) translateX(-50%);
  -moz-transform: translateY(0px) translateX(-50%);
  -ms-transform: translateY(0px) translateX(-50%);
  -o-transform: translateY(0px) translateX(-50%);
  transform: translateY(0px) translateX(-50%);
}
*:hover > .tooltip.tooltip-n {
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
/*
    Header
 */
.cp-header {
  position: relative;
  z-index: 99;
  background-color: #000000;
  padding-left: 10px;
}
.cp-header.cp-header-group {
  display: table;
  box-sizing: border-box;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
}
.cp-header.cp-header-group .cp-header-item {
  display: table-cell;
  vertical-align: top;
}
.cp-header.cp-header-group > .cp-header-item {
  margin: 0;
  box-sizing: border-box;
}
.cp-title-container {
  max-width: 70%;
  display: block;
  float: left;
  height: 50px;
}
.cp-title-container div {
  color: #fff;
  line-height: 50px;
  white-space: nowrap;
  overflow: hidden;
}
.cp-title-container .cp-file-icon {
  width: 45px;
  height: 50px;
  display: inline-block;
  float: left;
  background: no-repeat 10px center;
}
.cp-file-controls {
  padding-left: 0;
  text-align: right;
  float: right;
  max-width: 30%;
  display: block;
  height: 50px;
}
.cp-file-controls > span {
  display: inline-block;
}
/*
    Body
 */
.cp-body {
  height: 100%;
  margin: 0;
}
.cp-error-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  max-width: 640px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.cp-error-message .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-error-message p {
  margin: 10px 0;
  color: #fff;
  line-height: 1.4em;
}
.cp-error-message p.message {
  margin: 10px 0 0 0;
  word-wrap: break-word;
}
.cp-preview-password {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-preview-password .cp-password-lock-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: url("data:image/svg+xml;base64,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") no-repeat center center;
  background-size: contain;
}
.cp-preview-password p {
  margin: 0;
  color: #fff;
}
.cp-preview-password p.message {
  line-height: 1.4em;
  margin-top: 10px;
}
.cp-preview-password .cp-password-fullscreen {
  display: none;
}
.cp-preview-password .cp-password-base .cp-password-input {
  font-size: 14px;
  text-align: left;
  height: 28px;
  border: 1px solid #ccc;
  border-radius: 2px;
  outline: none;
  padding: 0 6px;
  margin: 10px 10px 0 0;
}
.cp-preview-password .cp-password-base .cp-password-button:focus {
  outline: none !important;
}
.cp-file-body .cp-baseline-extension {
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
.cp-file-body.presentation {
  background: #000;
  top: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  left: 0 !important;
  height: 100% !important;
  width: 100% !important;
  z-index: 100;
}
.cp-container {
  font-family: Arial, sans-serif;
  font-size: 16px;
  background-color: rgba(51, 51, 51, 0.95);
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* CONFDEV-26487: z-index is greater than an AUI inline dialog, whose default is 100. */
  z-index: 101;
}
.cp-container .hidden {
  display: none;
}
.cp-container *[role='button'] {
  position: relative;
  -webkit-transform: translateZ(0);
  /* webkit flicker fix */
  -webkit-font-smoothing: antialiased;
  /* webkit text rendering fix */
}
.cp-container .cp-icon {
  padding: 0;
  margin: 0;
  background-color: transparent;
  background-position: center !important;
  background-repeat: no-repeat !important;
  height: 50px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 50px;
  border-radius: 0;
}
.cp-container a.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.cp-container a.cp-icon:hover {
  background-color: #707070;
}
.cp-container a.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
.cp-container button.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: none;
  outline: none;
  cursor: pointer;
}
.cp-container button.cp-icon:hover {
  background-color: #707070;
}
.cp-container button.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
*[id^='cp-container-'][data-embedded='true'],
*[id^='cp-container-'][data-contained='true'] {
  position: relative;
  background: none;
  z-index: auto;
}
*[id^='cp-container-'][data-embedded='true'] .cp-body .cp-file-body,
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  position: relative;
  overflow: auto;
  top: 0;
}
*[id^='cp-container-'][data-embedded='true'] .cp-footer,
*[id^='cp-container-'][data-contained='true'] .cp-footer {
  display: none;
}
*[id^='cp-container-'][data-embedded='true'] .cp-file-controls .fv-close-button,
*[id^='cp-container-'][data-contained='true'] .cp-file-controls .fv-close-button {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-header {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  height: 100%;
}
.cp-small-icon {
  background: no-repeat left center;
  height: 20px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 25px;
}
.cp-file-body {
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50px;
  vertical-align: middle;
  height: calc(100% - 50px);
}
.cp-viewer-layer {
  height: 100%;
}
.cp-file-body.narrow {
  right: 350px;
}
.cp-file-body.short {
  bottom: 160px;
  /* to account for the thumbnail pane */
  height: calc(100% - 210px);
}
.no-scroll {
  overflow: hidden !important;
}
.cp-nav {
  cursor: pointer;
  outline: none;
  border: none;
  background: transparent no-repeat center center;
  color: #fff;
  position: absolute;
  top: 50%;
  margin-top: -50px;
  width: 65px;
  height: 100px;
  text-indent: -999em;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
  -moz-transition: background-color 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s ease-in-out;
}
.cp-nav:after {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}
.cp-nav:hover,
.cp-nav:focus {
  background-color: rgba(0, 0, 0, 0.8);
  transition: background-color 0.1s ease-in-out;
  -moz-transition: background-color 0.1s ease-in-out;
  -webkit-transition: background-color 0.1s ease-in-out;
}
.cp-nav.disabled {
  opacity: 0.5;
  cursor: default;
}
.cp-nav.disabled:hover {
  background-color: transparent;
}
.cp-nav-left {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  left: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvcHJldmlvdXMtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xMCAyNC41MTR2LTEuMDI4TDI1LjMyMyA2Ljc0Yy4zNzQtLjQxLjk4NC0uNDI1IDEuMzczLS4wMjRsMS4xMDggMS4xNGMuMzg0LjM5NS4zOTMgMS4wMzguMDE2IDEuNDRMMTQgMjRsMTMuODIgMTQuNzA1Yy4zNzYuNC4zNzMgMS4wNC0uMDE2IDEuNDRsLTEuMTA4IDEuMTRjLS4zODQuMzk0LTEgLjM4LTEuMzczLS4wMjVMMTAgMjQuNTE0eiIgZmlsbD0iIzk5OSIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-nav-right {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  right: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvbmV4dC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTM4IDI0LjUxNHYtMS4wMjhMMjIuNjc3IDYuNzRjLS4zNzQtLjQxLS45ODQtLjQyNS0xLjM3My0uMDI0bC0xLjEwOCAxLjE0Yy0uMzg0LjM5NS0uMzkzIDEuMDM4LS4wMTYgMS40NEwzNCAyNCAyMC4xOCAzOC43MDVjLS4zNzYuNC0uMzczIDEuMDQuMDE2IDEuNDRsMS4xMDggMS4xNGMuMzg0LjM5NCAxIC4zOCAxLjM3My0uMDI1TDM4IDI0LjUxNHoiIGZpbGw9IiM5OTkiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-error-layer,
.cp-waiting-layer,
.cp-password-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#inline-dialog-sharePreviewPopup {
  z-index: 120;
}
.cp-share-dialog-spinner {
  position: relative;
  top: 12px;
  left: 8px;
}
.cp-waiting-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-waiting-message .file-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-waiting-message p {
  margin: 0;
}
/*
    Sidebar
 */
.cp-sidebar {
  background-color: #fff;
  height: calc(100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  right: 0;
  /* CONFDEV-30274: Delegate rendering of this element to the GPU to avoid painting issues in latest Chrome/Safari */
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
.cp-sidebar.meta-infobar {
  height: calc(100% - 100px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
a.cp-button {
  box-sizing: border-box;
  background: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 3.01px;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: inherit;
  font-size: 14px;
  font-variant: normal;
  font-weight: 400;
  height: 2.14285714em;
  line-height: 1.42857143;
  margin: 0;
  padding: 4px 10px;
  vertical-align: baseline;
  white-space: nowrap;
  text-decoration: none;
  margin: 20px 10px 0 10px;
}
a.cp-button:hover {
  background: #e9e9e9;
  border-color: #999;
}
a.cp-button span.cp-button-icon {
  background-position: 0 0;
  border: none;
  margin: 0;
  padding: 0;
  text-indent: -999em;
  vertical-align: text-bottom;
  display: inline-block;
  text-align: left;
  line-height: 0;
  position: relative;
  vertical-align: text-top;
  height: 16px;
  width: 16px;
}
a.cp-button span.cp-button-icon.icon-download {
  position: relative;
  top: 3px;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=") no-repeat 0 0;
}
/*
    Footer
 */
.cp-footer {
  background-color: #000000;
  position: absolute;
  width: 100%;
  bottom: 0;
}
.cp-footer a {
  color: #fff;
  text-decoration: none;
}
.cp-file-body.meta-infobar {
  bottom: 50px;
  /* when no footer is present */
  height: calc(100% - 100px);
}
.cp-file-body.meta-minimode-toggle {
  bottom: 40px;
  /* when no footer is present */
  height: calc(100% - 90px);
}
.cp-file-body.meta-minimode-toggle.short {
  bottom: 200px;
  height: calc(100% - 250px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
</style><style type="text/css">.cp-image-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDFfZmlsZS1pbWctbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAzIDI0IDI0IDIzLjEwMyAyNCAyMS45OTZWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMjMgMjEuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTZWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTcgMTNsLTMuNzMgMy43M0w4IDlsLTUgNSAuNSAxczMuMDA1LTIuNzggNC41LTQuNWMuNDUzLjU1MyA3IDEwLjUgNyAxMC41aDFsLTIuMTItMy4zNzggMy42Mi0zLjYyIDMgMyAuNS0xLTMtM2gtMXptLTQuMzU3LTQuMDdsLS4yIDIuOCAyLjAyNC0xLjkwNSAyLjAyNCAxLjkwNS0uMi0yLjggMi43MjYtLjQyMy0yLjI3NC0xLjU4NSAxLjM3My0yLjQzNC0yLjYzNi44MjMtMS4wMTMtMi42MS0xLjAxMyAyLjYxLTIuNjM2LS44MjIgMS4zNzQgMi40MzQtMi4yNzUgMS41ODUgMi43MjYuNDI0em0xLjgyNC0zLjM4N2MxLjAzIDAgMS44NjcuODUzIDEuODY3IDEuOTA3IDAgMS4wNTUtLjgzOCAxLjkwOC0xLjg2NyAxLjkwOC0xLjAzIDAtMS44NjctLjg1My0xLjg2Ny0xLjkwOCAwLTEuMDU0LjgzOC0xLjkwNyAxLjg2Ny0xLjkwN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-image-icon.size-48,
.cp-image-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDBfZmlsZS1pbWFnZS14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDM5Ljk4ek0yOS45IDE5LjU2bC0xLjY4IDMgLjQtMy40MTVjLS42NzQtLjMxMy0xLjI3Ny0uNzU0LTEuNzc2LTEuMjkzTDIzLjcyIDE5LjI5bDIuMzMyLTIuNTI2Yy0uMzU0LS42MzMtLjU5LTEuMzQtLjY4LTIuMDlMMjIgMTRsMy4zNzMtLjY3M2MuMDktLjc1Mi4zMjUtMS40NTguNjgtMi4wOUwyMy43MTggOC43MWwzLjEyNCAxLjQzOGMuNS0uNTQgMS4xMDItLjk4IDEuNzc3LTEuMjkzbC0uNC0zLjQxNSAxLjY4IDNjLjM1Ni0uMDcuNzI0LS4xMDcgMS4xLS4xMDcuMzc2IDAgLjc0NC4wMzcgMS4xLjEwN2wxLjY4LTMtLjQgMy40MTVjLjY3NC4zMTMgMS4yNzcuNzU0IDEuNzc2IDEuMjkzTDM4LjI4IDguNzFsLTIuMzMyIDIuNTI2Yy4zNTQuNjMzLjU5IDEuMzQuNjggMi4wOUw0MCAxNGwtMy4zNzMuNjczYy0uMDkuNzUyLS4zMjUgMS40NTgtLjY4IDIuMDlsMi4zMzQgMi41MjctMy4xMjQtMS40MzhjLS41LjU0LTEuMTAyLjk4LTEuNzc3IDEuMjkzbC40IDMuNDE1LTEuNjgtM2MtLjM1Ni4wNy0uNzI0LjEwNy0xLjEuMTA3LS4zNzYgMC0uNzQ0LS4wMzctMS4xLS4xMDd6TTI2LjMzNCAxNGMwLTIuNTc3IDIuMDktNC42NjcgNC42NjctNC42NjdzNC42NjcgMi4wOSA0LjY2NyA0LjY2Ny0yLjA5IDQuNjY3LTQuNjY3IDQuNjY3LTQuNjY3LTIuMDktNC42NjctNC42Njd6bTEuNDg0IDIwLjY4M0wzMyA0MmwtLjUgMUwxNiAxOS41IDUuNSAzMiA1IDMxbDExLTEzIDExLjE5NSAxNS44MDVMMzUgMjZsOCAxMC0uNSAxLTcuNS05LjUtNy4xODMgNy4xODN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-pdf-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDJfZmlsZS1wZGYtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMy44OTYgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMSAyMS45OTVWOC45NkM1LjE3NSA4Ljc1OCA5LjQ0MyA3LjMxNyAxNSA1djE4SDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NXpNMTYgOWgydjE0aC0yVjl6bTcgMTIuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gxOVY4aC0zVjMuNUMxMC40MzQgNS44MiA1LjMwNyA3LjY5IDEgNy45NTJWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTIgMTkuNXYtMWMtMyAxLTYgMi05IDIuNXYxYzMtLjUgNi0xLjUgOS0yLjV6TTMgMThjMy0uNSA2LTEuNSA5LTIuNXYtMWMtMyAxLTYgMi05IDIuNXYxem0wLTV2MWMzLS41IDYtMS41IDktMi41di0xYy0zIDEtNiAyLTkgMi41eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-pdf-icon.size-48,
.cp-pdf-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDFfZmlsZS1wZGYteGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDB6TTQuMDEgNDdDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxNC45ODVjOS4zODQtLjA3NyAxOS4xMzItMi4zMjUgMjktNi43MTJWNDdINC4wMXpNNDAgNDdoLTRWMTZoMy41MDJjLjI3NCAwIC40OTguMjIuNDk4LjQ5M1Y0N3ptLTUgMGgtNFYxMmgzLjUwMmMuMjc0IDAgLjQ5OC4yMjQuNDk4LjVWNDd6bTguOTkgMEg0MVYxNi40OTNDNDEgMTUuNjcgNDAuMzI4IDE1IDM5LjUwMiAxNUgzNnYtMi41YzAtLjgyNy0uNjcyLTEuNS0xLjQ5OC0xLjVIMzFWNi43MjRsLS43MDcuMzJjLTkuOTc4IDQuNTM3LTE5LjgzIDYuODYyLTI5LjI5MyA2Ljk0VjQuMDFDMSAyLjM0NyAyLjM0OCAxIDQuMDEgMWgzOS45OEM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDggMy4wMS0zLjAxIDMuMDF6TTUgMjRjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6bTAgNmM4LjUtLjUgMTItMSAyMC00di0xYy04IDMtMTIgMy41LTIwIDR2MXptMCA2YzguNS0uNSAxMi0xIDIwLTR2LTFjLTggMy0xMiAzLjUtMjAgNHYxem0wIDZjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-text-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDNfZmlsZS10ZXh0LWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0zIDE0djFoMTh2LTFIM3ptMC04djFoMTVWNkgzem0wIDR2MWgxMXYtMUgzem0wIDh2MWgxM3YtMUgzeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-text-icon.size-48,
.cp-text-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDJfZmlsZS10ZXh0LXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTUgMzF2MWgzOHYtMUg1em0wLTE0djFoMzN2LTFINXptMC03djFoMjh2LTFINXptMCAxNHYxaDI0di0xSDV6bTAgMTR2MWgyOHYtMUg1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-document-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDRfZmlsZS1kb2N1bWVudC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5OCAwIDIuMDA1djE5Ljk5QzAgMjMuMTAzLjg5NiAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwNCAwIDIxLjk5NSAwek0yMyAyMS45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTlDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5ek0zIDEwaDd2MUgzdi0xem0wLTRoOXYxSDNWNnptMCA4aDE2djFIM3YtMXptMCA0aDE0djFIM3YtMXpNMTkuOTk3IDVoLTQuOTk0QzE0LjQ1IDUgMTQgNS40NSAxNCA1Ljk5djQuMDJjMCAuNTQ2LjQzNy45ODggMSAuOTloNC45OTdjLjU1NCAwIDEuMDAzLS40NSAxLjAwMy0uOTlWNS45OWMwLS41NDctLjQzOC0uOTktMS4wMDMtLjk5em0tNC4wMDcgNWwyLjQ3Ni0yLjUgMS41MzYuOTFjMCAuODUtLjAwMiAxLjYtLjAwMiAxLjYgMC0uMDEtMi41NTUtLjAxLTQuMDEtLjAxek0xNSA5LjU0M2MtLjAwNC0xLjA0OCAwLTMuNTUyIDAtMy41NTIgMCAuMDEyIDQuOTk3LjAxIDQuOTk3LjAxLjAwNCAwIC4wMDUuNjEyLjAwNSAxLjM1N2wtMS41MzYtLjk5MkwxNSA5LjU0M3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-document-icon.size-48,
.cp-document-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDNfZmlsZS1kb2N1bWVudC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2NC0xLjM0OCAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjU0IDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ4IDEgNC4wMSAxaDM5Ljk4ek01IDM5aDI4di0xSDV2MXptMC03aDMydi0xSDV2MXptMC0yMWgxN3YtMUg1djF6bTAgN2gxNHYtMUg1djF6bTAgN2gxOXYtMUg1djF6bTM2LjAwOC0xNUgyOS45OTJjLTEuMSAwLTEuOTkyLjg5NC0xLjk5MiAxLjk5MnYxMS4wMTZjMCAxLjEuODk0IDEuOTkyIDEuOTkyIDEuOTkyaDExLjAxNmMxLjEgMCAxLjk5Mi0uODkzIDEuOTkyLTEuOTkyVjExLjk5MmMwLTEuMS0uODk0LTEuOTkyLTEuOTkyLTEuOTkyek0yOS45OTIgMjRjLS41NDcgMC0uOTkyLS40NDQtLjk5Mi0uOTkyVjIxbDMtMy41IDUgNi41aC03LjAwOHptMTEuMDE2IDBoLTIuODlsLTEuMS0xLjQzNSAyLjQ2Ni0yLjA2NSAyLjQzMyAyLjljLS4xNTMuMzUzLS41MDIuNi0uOTEuNnpNNDIgMjEuOTJMMzkuNDg0IDE5bC0zLjEzIDIuNjk1TDMyIDE2bC0zIDMuNXYtNy41MDhjMC0uNTQ3LjQ0NS0uOTkyLjk5Mi0uOTkyaDExLjAxNmMuNTQ3IDAgLjk5Mi40NDUuOTkyLjk5MnY5LjkyOHptLTEuNzY0LTkuMTAzbC0xLjk3Ni42MDYtLjc2LTEuOTIzLS43NiAxLjkyMy0xLjk3Ni0uNjA2IDEuMDMgMS43OTQtMS43MDYgMS4xNyAyLjA0NC4zMS0uMTUgMi4wNjRMMzcuNSAxNi43NWwxLjUyIDEuNDA0LS4xNTItMi4wNjMgMi4wNDQtLjMxLTEuNzA2LTEuMTcgMS4wMy0xLjc5M3pNMzcuNSAxNi4zYy0uNzE4IDAtMS4zLS41ODItMS4zLTEuMyAwLS43MTguNTgyLTEuMyAxLjMtMS4zLjcxOCAwIDEuMy41ODIgMS4zIDEuMyAwIC43MTgtLjU4MiAxLjMtMS4zIDEuM3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDVfZmlsZS1zcHJlYWRzaGVldC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTIxLjk5NSAxQzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OUMyMyAyMi41NSAyMi41NSAyMyAyMS45OTUgMjNIMi4wMDVDMS40NSAyMyAxIDIyLjU1IDEgMjEuOTk1VjIuMDA1QzEgMS40NSAxLjQ1IDEgMi4wMDUgMWgxOS45OXpNMTYgMTh2LTRoN3YtMWgtN1Y5aDdWOEg4VjFIN3Y3SDF2MWg2djE0aDFWOWg3djRIOHYxaDd2NEg4djFoN3Y0aDF2LTRoN3YtMWgtN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-48,
.cp-spreadsheet-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDRfZmlsZS1zcHJlYWRzaGVldC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00NyAxNlY0LjAxQzQ3IDIuMzQ3IDQ1LjY1MyAxIDQzLjk5IDFIMTZ2MTVoMzF6bTAgMXYxMEgzMlYxN2gxNXptMCAxMXYxMEgzMlYyOGgxNXptMCAxMXY0Ljk5YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFIMzJ2LThoMTV6bS0xNiA4SDE2di04aDE1djh6bS0xNiAwSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxN2gxNHYzMHpNMSAxNlY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFIMTV2MTVIMXptMTUgMTJoMTV2MTBIMTZWMjh6bTAtMTFoMTV2MTBIMTZWMTd6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDZfZmlsZS1wcmVzZW50YXRpb24tbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgMi4wMDV2MTkuOTlDMCAyMy4xMDIuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5NyAwIDIuMDA1ek0yMS45OTUgMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTE2IDd2MWg1VjdoLTV6bTAgM3YxaDV2LTFoLTV6bS0xIDN2MWg2di0xaC02ek0zIDE3djFoMTh2LTFIM3pNOC41IDQuNWMtMi43NiAwLTUgMi4yMzgtNSA1czIuMjQgNSA1IDUgNS0yLjIzOCA1LTUtMi4yNC01LTUtNXptLTQgNWMwLTIuMDQgMS41MjctMy43MjIgMy41LTMuOTd2My42MjNsLTMuMzkzIDEuMjczYy0uMDctLjI5Ny0uMTA3LS42MDgtLjEwNy0uOTI2em00IDRjLTEuNTM3IDAtMi44Ny0uODY3LTMuNTQtMi4xMzhsMy40MTQtMS4yOCAyLjU4IDIuNTc4Yy0uNjguNTI3LTEuNTMuODQtMi40NTQuODR6bTMuMTYtMS41NDdMOSA5LjI5M1Y1LjUzYzEuOTczLjI0OCAzLjUgMS45MyAzLjUgMy45NyAwIC45MjUtLjMxNCAxLjc3Ni0uODQgMi40NTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-48,
.cp-presentation-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDVfZmlsZS1wcmVzZW50YXRpb24teGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgNC4wMXYzOS45OEMwIDQ2LjIwNSAxLjc5NCA0OCA0LjAxIDQ4aDM5Ljk4YzIuMjE1IDAgNC4wMS0xLjc5NCA0LjAxLTQuMDFWNC4wMUM0OCAxLjc5NSA0Ni4yMDYgMCA0My45OSAwSDQuMDFDMS43OTUgMCAwIDEuNzk0IDAgNC4wMXpNNDMuOTkgMUM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFINC4wMUMyLjM0NyA0NyAxIDQ1LjY1MyAxIDQzLjk5VjQuMDFDMSAyLjM0NyAyLjM0NyAxIDQuMDEgMWgzOS45OHpNMjggMTJ2MWgxNXYtMUgyOHptMCA2djFoMTV2LTFIMjh6bS0yIDZ2MWgxN3YtMUgyNnpNNSAzOHYxaDM4di0xSDV6bTAtN3YxaDM4di0xSDV6bTAtMTQuNWMwIDUuMjQ3IDQuMjUzIDkuNSA5LjUgOS41czkuNS00LjI1MyA5LjUtOS41UzE5Ljc0NyA3IDE0LjUgNyA1IDExLjI1MyA1IDE2LjV6bTEgMGMwLTQuNTI2IDMuNTM4LTguMjI2IDgtOC40ODV2OC4xMzhsLTcuNjIzIDIuODZDNi4xMzIgMTguMjE3IDYgMTcuMzcyIDYgMTYuNXptOC41IDguNWMtMy40NjcgMC02LjQ1LTIuMDc1LTcuNzcyLTUuMDUybDcuNjQ2LTIuODY3IDUuNzczIDUuNzc0QzE4LjY0NSAyNC4xOSAxNi42NjcgMjUgMTQuNSAyNXptNi4zNTMtMi44NTNMMTUgMTYuMjkzVjguMDE1YzQuNDYyLjI2IDggMy45NiA4IDguNDg1IDAgMi4xNjctLjgxIDQuMTQ2LTIuMTQ3IDUuNjQ3eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-code-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDdfZmlsZS1jb2RlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0yLjUgOS41bDUtNSAxIC41TDQgOS41IDguNSAxNGwtMSAuNS01LTV6bTE5IDVsLTUuMDUtNS0uOTUuNSA0LjUgNC41LTQuNSA0LjUuOTUuNSA1LjA1LTV6TTkgMjFoMWw1LTE4aC0xTDkgMjF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-code-icon.size-48,
.cp-code-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwMzlfZmlsZS1jb2RlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTYgMTcuNWwxMC0xMCAuNSAxLTkgOSA5IDktLjUgMS0xMC0xMHptMzcgMTJsLTEwLTEwLS41IDEgOSA5LTkgOSAuNSAxIDEwLTEwek0xOCA0Mi45NjJoMUwzMCA2aC0xTDE4IDQyLjk2MnoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-multimedia-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDhfZmlsZS12aWRlby1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTguMjkzIDFsLTMgM0gxLjcwN2wzLTNoMy41ODZ6TTYuNzA3IDRoMy41ODZsMy0zSDkuNzA3bC0zIDN6bTgtM2wtMyAzaDMuNTg2bDMtM2gtMy41ODZ6bTguMTMuNDZDMjIuNjYgMS4xOCAyMi4zNSAxIDIxLjk5NiAxaC0yLjI4OGwtMyAzaDMuNTg2bDIuNTQ1LTIuNTR6TTMuMjk0IDFIMi4wMDVDMS40NSAxIDEgMS40NSAxIDIuMDA1djEuMjg4TDMuMjkzIDF6TTIzIDRoLTEuMjkzTDIzIDIuNzA3VjR6bTAgMXYxNi45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVY1aDIyek05IDE5LjVsNy01LjUtNy01LjV2MTF6bTUuNS01LjQ3TDEwIDE3LjV2LTdsNC41IDMuNTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-multimedia-icon.size-48,
.cp-multimedia-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDZfZmlsZS12aWRlby14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMHpNNDcgMTF2MzIuOTljMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlMMS4wMyAxMUg0N3ptLTEyLjc5Mi0xaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMGgtNS4zM2w4Ljg1NS05aDUuMzNsLTguODU1IDl6bS03IDBoLTUuMzNsOC44NTUtOWg1LjMzbC04Ljg1NSA5em0tNyAwaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMEgxLjAzMnYtLjI1TDkuNzI2IDFoNS4zMzhsLTguODU2IDl6TTEgNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDMuOTkzTDEgOC4wNDhWNC4wMXpNNDIuOTc0IDEwTDQ3IDUuOTMzVjEwaC00LjAyNnptLTcuMDk3IDBsOC43ODMtOC45MjVDNDYgMS4zOCA0NyAyLjU3NyA0NyA0LjEyNUw0MS4xODYgMTBoLTUuMzF6TTE5IDM5bDEyLTEwLjVMMTkgMTl2MjB6bTEtMThsOS41IDcuNUwyMCAzN1YyMXoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-archive-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTBfZmlsZS1hcmNoaXZlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0xMCAxOGg0di02aC0ydi0xaC0ydjd6bTItN2gydi0xaC0ydjF6bTAtMmgyVjhoLTJ2MXptLTIgMWgyVjloLTJ2MXptMC0yaDJWN2gtMnYxem0yLTFoMlY2aC0ydjF6bS0yLTFoMlY1aC0ydjF6bTItMWgyVjRoLTJ2MXptLTItMWgyVjNoLTJ2MXptMi0xaDJWMmgtMnYxem0tMi0xaDJWMWgtMnYxem0xMS45OTUtMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTAgMi4wMDV2MTkuOTlDMCAyMy4xMDMuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5NyAwIDAgLjg5OCAwIDIuMDA1ek0xMiAxNWMtLjU1MiAwLTEgLjQ0Ny0xIDEgMCAuNTUzLjQ0OCAxIDEgMXMxLS40NDcgMS0xYzAtLjU1My0uNDQ4LTEtMS0xeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-archive-icon.size-48,
.cp-archive-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDhfZmlsZS1hcmNoaXZlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTIwIDMxLjAwMnY4Ljk5QzIwIDQxLjEgMjAuODg3IDQyIDIxLjk5OCA0Mmg0LjAwNEMyNy4xMDUgNDIgMjggNDEuMDk4IDI4IDM5Ljk5di04Ljk4OGMwLS41NTMtLjQ1My0xLjAwMi0uOTk3LTEuMDAyaC02LjAwNmMtLjU1IDAtLjk5Ny40NTYtLjk5NyAxLjAwMnpNMjcuMDAzIDMxYy0uMDA2IDAtLjAwMyA4Ljk5LS4wMDMgOC45OSAwIC41NTgtLjQ1IDEuMDEtLjk5OCAxLjAxaC00LjAwNGMtLjU1NSAwLS45OTgtLjQ0Ny0uOTk4LTEuMDF2LTguOTg4TDI3LjAwMyAzMXpNMjIgMzh2MWMwIC41NTMuNDQzIDEgMS4wMSAxaDEuOThjLjU1OCAwIDEuMDEtLjQ0MyAxLjAxLTF2LTFjMC0uNTUzLS40NDMtMS0xLjAxLTFoLTEuOThjLS41NTggMC0xLjAxLjQ0My0xLjAxIDF6bTIuOTkgMGMuMDEyIDAgLjAxLS4wMDIuMDEgMHYxaC0xLjk5Yy0uMDEyIDAtLjAxLjAwMi0uMDEgMHYtMWgxLjk5ek0yMCA2djFoNVY2aC01em0wLTR2MWg1VjJoLTV6bTMgNnYxaDVWOGgtNXptMC00djFoNVY0aC01em0tMyA2djFoNXYtMWgtNXptMyAydjFoNXYtMWgtNXptLTMgMnYxaDV2LTFoLTV6bTMgMnYxaDV2LTFoLTV6bS0zIDJ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0zLTZ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTFfZmlsZS1nZW5lcmljLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0xMCAxNS40OGMwIDEuNTEgMSAyLjUyIDIuNSAyLjUyczIuNS0xLjAxIDIuNS0yLjUxNlY2Ljk5N0MxNSA0LjUgMTMgMyAxMSAzUzcgNC41IDcgNy4wMDJ2OC40OEM3IDE4LjUyIDkuNDY2IDIxIDEyLjUgMjFjMy4wMzcgMCA1LjUtMi40NjcgNS41LTUuNVY2aC0xdjkuNWMwIDIuNDgtMiA0LjUtNC41IDQuNVM4IDE3Ljk3IDggMTUuNDhWNy4wMDNDOCA1IDkuNSA0IDExIDRzMyAxIDMgMi45OTd2OC40ODdDMTQgMTYuNSAxMy41IDE3IDEyLjUgMTdzLTEuNS0uNS0xLjUtMS41MlYxMGgtMXY1LjQ4eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-48,
.cp-unknown-file-type-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDlfZmlsZS1nZW5lcmljLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5Ljk4QzQ1LjY1My45OCA0NyAyLjMyNiA0NyAzLjk5djQwLjAxNmMwIDEuNjY1LTEuMzQ3IDMuMDEzLTMuMDEgMy4wMTNINC4wMUMyLjM0NyA0Ny4wMiAxIDQ1LjY3NCAxIDQ0LjAxVjMuOTkyQzEgMi4zMjcgMi4zNDcuOTggNC4wMS45OGgzOS45OHpNMTkgMzAuOTk1QzE5IDM0IDIxIDM2IDI0IDM2czUtMiA1LTVWMTMuOTg2QzI5IDEwIDI2IDYuOTkgMjEuOTk2IDYuOTlzLTcuMDA0IDMuMDEtNy4wMDQgN3YxOC4wMDVjMCA1LjAwNSA0IDkgOS4wMDQgOVMzMyAzNyAzMyAzMS45ODJWMTIuOTk0aC0xdjE4Ljk4OGMwIDQuNTE4LTMuNSA4LjAxMy04LjAwNCA4LjAxM3MtOC4wMDQtMy40OTUtOC4wMDQtOFYxMy45OWMwLTMuNDkgMi41LTYgNi4wMDQtNkMyNS41IDcuOTkgMjggMTAuNSAyOCAxMy45ODVWMzFjMCAyLjUtMiA0LTQgNHMtNC0xLjUtNC00LjAwNFYyMGgtMXYxMC45OTZ6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
</style><style type="text/css">.cp-control-panel-close {
  border-left: 1px solid #333;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAwMjdfY2xvc2UtZGlhbG9nPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48cGF0aCBkPSJNOS42NCA4bDQuMDE2IDQuMDE2Yy4yMy4yMy4zNDQuNTAyLjM0NC44MiAwIC4zMTgtLjExNS41OS0uMzQ0LjgyLS4yMy4yMy0uNTAyLjM0NC0uODIuMzQ0LS4zMTggMC0uNTktLjExNS0uODItLjM0NEw4IDkuNjRsLTQuMDE2IDQuMDE2Yy0uMjMuMjMtLjUwMi4zNDQtLjgyLjM0NC0uMzE4IDAtLjU5LS4xMTUtLjgyLS4zNDQtLjIzLS4yMy0uMzQ0LS41MDItLjM0NC0uODIgMC0uMzE4LjExNS0uNTkuMzQ0LS44Mkw2LjM2IDggMi4zNzQgNC4wMTZjLS4yMy0uMjMtLjM0NC0uNTAzLS4zNDQtLjgyIDAtLjMxOC4xMTYtLjU4Ny4zNDUtLjgwNS4yMy0uMjMuNTAzLS4zNC44Mi0uMzQuMzE4IDAgLjU5LjExMy44Mi4zNDNMOCA2LjM3Nmw0LjAzLTQuMDNjLjIzLS4yMy41MDQtLjM0NS44MjItLjM0NS4zMTcgMCAuNTkuMTE3LjgyLjM0Ni4yMi4yMy4zMjguNTAyLjMyOC44MiAwIC4zMTgtLjExLjU4Ni0uMzI4LjgwNUw5LjY0MiA4eiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-control-panel-download,
.cp-waiting-message-download {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvZG93bmxvYWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0zIDE0di0xaDEwdjFIM3ptMTAtNy44NEw4LjIgMTFoLS4zOUwzIDYuMTZsMS4xMi0xLjEzTDcgNy45MlYzLjVjLjA5LTEgLjQ1LTEuNSAxLTEuNXMuODkuMzcgMSAxLjV2NC40MWwyLjkxLTIuODVMMTMgNi4xNnoiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-control-panel-more {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAxMjdfbW9yZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTQgOGMwLS41NTItLjE5NS0xLjAyMy0uNTg2LTEuNDE0QzMuMDI0IDYuMTk2IDIuNTUyIDYgMiA2Yy0uNTUyIDAtMS4wMjMuMTk1LTEuNDE0LjU4NkMuMTk2IDYuOTc2IDAgNy40NDggMCA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4NkMzLjgwNCA5LjAyNCA0IDguNTUyIDQgOHptNiAwYzAtLjU1Mi0uMTk1LTEuMDIzLS41ODYtMS40MTRDOS4wMjQgNi4xOTYgOC41NTIgNiA4IDZjLS41NTIgMC0xLjAyMy4xOTUtMS40MTQuNTg2QzYuMTk2IDYuOTc2IDYgNy40NDggNiA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4Ni4zOS0uMzkuNTg2LS44NjIuNTg2LTEuNDE0em02IDBjMC0uNTUyLS4xOTUtMS4wMjMtLjU4Ni0xLjQxNEMxNS4wMjQgNi4xOTYgMTQuNTUyIDYgMTQgNmMtLjU1MiAwLTEuMDIzLjE5NS0xLjQxNC41ODYtLjM5LjM5LS41ODYuODYyLS41ODYgMS40MTQgMCAuNTUyLjE5NSAxLjAyMy41ODYgMS40MTQuMzkuMzkuODYyLjU4NiAxLjQxNC41ODYuNTUyIDAgMS4wMjMtLjE5NSAxLjQxNC0uNTg2LjM5LS4zOS41ODYtLjg2Mi41ODYtMS40MTR6IiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  z-index: 201;
}
.cp-control-panel-more.cp-dropdown {
  position: relative;
  display: inline-block;
  text-indent: 0;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content {
  pointer-events: auto;
  display: block;
  margin-top: 0;
  padding: 0;
  white-space: nowrap;
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s ease, visibility 0s ease 0.5s;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li {
  font-size: 14px;
  list-style: none;
  padding: 0;
  background-color: #333;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li:hover {
  background-color: #707070;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li a {
  display: block;
  line-height: 2.25rem;
  padding: 0 1.5rem 0 1rem;
  color: #fff;
  text-decoration: none;
}
.cp-control-panel-more.cp-dropdown:focus {
  pointer-events: none;
}
.cp-control-panel-more.cp-dropdown:focus .cp-dropdown-content {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.25s ease, visibility 0s ease 0s;
}
.cp-control-panel-more.cp-dropdown:focus .tooltip {
  display: none !important;
}
</style><style type="text/css">.cp-toolbar {
  display: block;
  opacity: 0;
  position: absolute;
  left: 50%;
  bottom: 20px;
  background-color: #333333;
  border-radius: 5px;
  margin-left: -75px;
  /* to center the div */
}
.cp-toolbar > * {
  position: relative;
}
.cp-toolbar > *:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.cp-toolbar > *:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.cp-toolbar > *::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  opacity: 1;
}
.cp-toolbar-prev-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctdXA8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTMgMTFsNS4wMS01TDEzIDExbC41LTEtNS40OS01LjVMMi41IDEwbC41IDF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-toolbar-next-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctZG93bjwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMyA1bDUuMDEgNUwxMyA1bC41IDEtNS40OSA1LjVMMi41IDYgMyA1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-toolbar-fit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1maXQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaCBCZXRhLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJXaGl0ZSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImNwLXRvb2xiYXItZml0IiBmaWxsPSIjRkZGRkZGIj4KICAgICAgICAgICAgPGcgaWQ9IlBhZ2UtMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMi41MDAwMDAsIDIuNTAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iRmlsbC0xIiBwb2ludHM9IjAuOTk5OSAxMi4wMDAyIDAuOTk5OSA4Ljk5OTIgLTAuMDAwMSA4Ljk5OTIgLTAuMDAwMSAxNC4wMDAyIDUuMDAwOSAxNC4wMDAyIDUuMDAwOSAxMy4wMDAyIDEuOTk5OSAxMy4wMDAyIDUuMDAwOSAxMC4wMDAyIDMuOTk5OSA4Ljk5OTIiPjwvcG9seWdvbj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTIiIHBvaW50cz0iMCAtMC4wMDA0IDAgNC45OTk2IDEgNC45OTk2IDEgMi4wMDA2IDQgNC45OTk2IDUgNC4wMDA2IDIgMS4wMDA2IDUgMS4wMDA2IDUgLTAuMDAwNCI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTksLTAuMDAwNCBMOSwxLjAwMDYgTDEyLDEuMDAwNiBMOSw0LjAwMDYgTDEwLDQuOTk5NiBMMTMsMi4wMDA2IEwxMyw0Ljk5OTYgTDE0LDQuOTk5NiBMMTQsLTAuMDAwNCBMOSwtMC4wMDA0IFogTTEzLDguOTk5NiBMMTMsMTIuMDAwNiBMMTAsOC45OTk2IEw5LDEwLjAwMDYgTDEyLDEzLjAwMDYgTDksMTMuMDAwNiBMOSwxMy45OTk2IEwxNCwxMy45OTk2IEwxNCw4Ljk5OTYgTDEzLDguOTk5NiBaIiBpZD0iRmlsbC0zIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-minus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1taW51czwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1taW51cyIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxnIGlkPSJQYWdlLTEiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEuMDAwMDAwLCAxLjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHBvbHlnb24gaWQ9IkZpbGwtMSIgcG9pbnRzPSI0LjEyNSA3LjgyNTUgMTAuMTI1IDcuODI1NSAxMC4xMjUgNi43OTU1IDQuMTI1IDYuNzk1NSI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTcuMTI1LDEzLjM3MiBDMy44ODYsMTMuMzcyIDEuMjUsMTAuNjUzIDEuMjUsNy4zMTEgQzEuMjUsMy45NjkgMy44ODYsMS4yNSA3LjEyNSwxLjI1IEMxMC4zNjQsMS4yNSAxMywzLjk2OSAxMyw3LjMxMSBDMTMsMTAuNjUzIDEwLjM2NCwxMy4zNzIgNy4xMjUsMTMuMzcyIE0xNi43MTYsMTUuMTQ3IEwxMy4xOCwxMS42MTIgQzEzLjExOSwxMS41NTIgMTMuMDQ1LDExLjUxOCAxMi45NzQsMTEuNDc2IEMxMy43NzYsMTAuMjkzIDE0LjI1LDguODU4IDE0LjI1LDcuMzExIEMxNC4yNSwzLjI4IDExLjA1NCwwIDcuMTI1LDAgQzMuMTk2LDAgMCwzLjI4IDAsNy4zMTEgQzAsMTEuMzQzIDMuMTk2LDE0LjYyMiA3LjEyNSwxNC42MjIgQzguODQyLDE0LjYyMiAxMC40MTksMTMuOTk1IDExLjY1LDEyLjk1MyBDMTEuNjc5LDEyLjk5IDExLjY5MiwxMy4wMzMgMTEuNzI2LDEzLjA2NiBMMTUuMjYyLDE2LjYwMSBDMTUuNjUyLDE2Ljk5MiAxNi4yODUsMTYuOTkyIDE2LjY3NiwxNi42MDEgTDE2LjcxNiwxNi41NjIgQzE3LjEwNiwxNi4xNzIgMTcuMTA2LDE1LjUzOCAxNi43MTYsMTUuMTQ3IiBpZD0iRmlsbC0yIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-plus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wbHVzPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2ggQmV0YS48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iV2hpdGUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJjcC10b29sYmFyLXBsdXMiIGZpbGw9IiNGRkZGRkYiPgogICAgICAgICAgICA8ZyBpZD0idG9vbGJhci1wbHVzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxLjAwMDAwMCwgMS4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTEiIHBvaW50cz0iNi42MjUgNC4yMjUxIDcuNjI1IDQuMjI1MSA3LjYyNSA2Ljc5NTEgMTAuMTI1IDYuNzk1MSAxMC4xMjUgNy44MjUxIDcuNjI1IDcuODI1MSA3LjYyNSAxMC4zOTQxIDYuNjI1IDEwLjM5NDEgNi42MjUgNy44MjUxIDQuMTI1IDcuODI1MSA0LjEyNSA2Ljc5NTEgNi42MjUgNi43OTUxIj48L3BvbHlnb24+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNNy4xMjUsMTMuMzcyIEMzLjg4NiwxMy4zNzIgMS4yNSwxMC42NTMgMS4yNSw3LjMxMSBDMS4yNSwzLjk2OSAzLjg4NiwxLjI1IDcuMTI1LDEuMjUgQzEwLjM2NCwxLjI1IDEzLDMuOTY5IDEzLDcuMzExIEMxMywxMC42NTMgMTAuMzY0LDEzLjM3MiA3LjEyNSwxMy4zNzIgTTE2LjcxNiwxNS4xNDcgTDEzLjE4LDExLjYxMiBDMTMuMTE5LDExLjU1MiAxMy4wNDUsMTEuNTE4IDEyLjk3NCwxMS40NzYgQzEzLjc3NiwxMC4yOTMgMTQuMjUsOC44NTggMTQuMjUsNy4zMTEgQzE0LjI1LDMuMjggMTEuMDU0LDAgNy4xMjUsMCBDMy4xOTYsMCAwLDMuMjggMCw3LjMxMSBDMCwxMS4zNDMgMy4xOTYsMTQuNjIyIDcuMTI1LDE0LjYyMiBDOC44NDIsMTQuNjIyIDEwLjQxOSwxMy45OTUgMTEuNjUsMTIuOTUzIEMxMS42NzksMTIuOTkgMTEuNjkyLDEzLjAzMyAxMS43MjYsMTMuMDY2IEwxNS4yNjIsMTYuNjAxIEMxNS42NTIsMTYuOTkyIDE2LjI4NSwxNi45OTIgMTYuNjc2LDE2LjYwMSBMMTYuNzE2LDE2LjU2MiBDMTcuMTA2LDE2LjE3MiAxNy4xMDYsMTUuNTM4IDE2LjcxNiwxNS4xNDciIGlkPSJGaWxsLTIiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
}
.cp-toolbar-presentation::before {
  background-image: url("data:image/svg+xml;base64,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");
}
.cp-toolbar-presentation-exit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdCIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJQYWdlLTEiIHBvaW50cz0iMTQuNSAzLjUgOS41MDEgOC40OTkgNC41IDMuNSAzLjUgNC41IDguNTAxIDkuNDk5IDMuNSAxNC41IDQuNSAxNS41IDkuNTAxIDEwLjQ5OSAxNC41IDE1LjUgMTUuNSAxNC41IDEwLjUgOS40OTkgMTUuNSA0LjUiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar button,
.cp-toolbar a {
  height: 40px !important;
  width: 40px !important;
  background-color: transparent;
  cursor: pointer;
}
.cp-toolbar button.inactive,
.cp-toolbar a.inactive {
  pointer-events: none;
  cursor: default;
  opacity: 0.5;
}
.cp-waiting-message-spinner {
  position: relative;
}
.cp-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: 0;
}
.cp-spinner .cp-spinner-blades {
  position: absolute;
  top: -2px;
  opacity: 0.25;
}
.cp-spinner .cp-spinner-blades div {
  position: absolute;
  width: 12px;
  height: 4px;
  box-shadow: rgba(0, 0, 0, 0.0980392) 0px 0px 1px;
  border-radius: 2px;
  background: #ffffff;
  -webkit-transform-origin: left center 0px;
          transform-origin: left center 0px;
}
.cp-spinner .cp-spinner-blade-1 {
  -webkit-animation: cp-spinner-anim-1-12 0.666667s linear infinite;
          animation: cp-spinner-anim-1-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-1 div {
  -webkit-transform: rotate(0deg) translate(10px, 0px);
          transform: rotate(0deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-2 {
  -webkit-animation: cp-spinner-anim-2-12 0.666667s linear infinite;
          animation: cp-spinner-anim-2-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-2 div {
  -webkit-transform: rotate(30deg) translate(10px, 0px);
          transform: rotate(30deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-3 {
  -webkit-animation: cp-spinner-anim-3-12 0.666667s linear infinite;
          animation: cp-spinner-anim-3-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-3 div {
  -webkit-transform: rotate(60deg) translate(10px, 0px);
          transform: rotate(60deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-4 {
  -webkit-animation: cp-spinner-anim-4-12 0.666667s linear infinite;
          animation: cp-spinner-anim-4-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-4 div {
  -webkit-transform: rotate(90deg) translate(10px, 0px);
          transform: rotate(90deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-5 {
  -webkit-animation: cp-spinner-anim-5-12 0.666667s linear infinite;
          animation: cp-spinner-anim-5-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-5 div {
  -webkit-transform: rotate(120deg) translate(10px, 0px);
          transform: rotate(120deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-6 {
  -webkit-animation: cp-spinner-anim-6-12 0.666667s linear infinite;
          animation: cp-spinner-anim-6-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-6 div {
  -webkit-transform: rotate(150deg) translate(10px, 0px);
          transform: rotate(150deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-7 {
  -webkit-animation: cp-spinner-anim-7-12 0.666667s linear infinite;
          animation: cp-spinner-anim-7-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-7 div {
  -webkit-transform: rotate(180deg) translate(10px, 0px);
          transform: rotate(180deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-8 {
  -webkit-animation: cp-spinner-anim-8-12 0.666667s linear infinite;
          animation: cp-spinner-anim-8-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-8 div {
  -webkit-transform: rotate(210deg) translate(10px, 0px);
          transform: rotate(210deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-9 {
  -webkit-animation: cp-spinner-anim-9-12 0.666667s linear infinite;
          animation: cp-spinner-anim-9-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-9 div {
  -webkit-transform: rotate(240deg) translate(10px, 0px);
          transform: rotate(240deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-10 {
  -webkit-animation: cp-spinner-anim-10-12 0.666667s linear infinite;
          animation: cp-spinner-anim-10-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-10 div {
  -webkit-transform: rotate(270deg) translate(10px, 0px);
          transform: rotate(270deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-11 {
  -webkit-animation: cp-spinner-anim-11-12 0.666667s linear infinite;
          animation: cp-spinner-anim-11-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-11 div {
  -webkit-transform: rotate(300deg) translate(10px, 0px);
          transform: rotate(300deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-12 {
  -webkit-animation: cp-spinner-anim-12-12 0.666667s linear infinite;
          animation: cp-spinner-anim-12-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-12 div {
  -webkit-transform: rotate(330deg) translate(10px, 0px);
          transform: rotate(330deg) translate(10px, 0px);
}
@-webkit-keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@-webkit-keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@-webkit-keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@-webkit-keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@-webkit-keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@-webkit-keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@-webkit-keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
@keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
</style><style type="text/css">.cp-unknown-file-type-view-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.cp-unknown-file-type-view {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-unknown-file-type-view .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
.cp-unknown-file-type-view p {
  margin: 0;
  color: #fff;
}
.cp-unknown-file-type-view a.download-button {
  margin: 20px 0 0 0;
}
.cp-unknown-file-type-view a.download-button span.icon-download {
  position: relative;
  top: 3px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  background-repeat: none;
  background-position: 0 0;
}
</style><style type="text/css">.cp-thumbnail-img-container.has-thumbnail {
  background-image: none !important;
}
.cp-sink.expanded .cp-thumbnails {
  display: block;
}
.cp-info {
  color: #fff;
  display: inline-block;
  width: 100%;
  font-size: 0.85em;
  height: 40px;
  line-height: 40px;
  text-decoration: none;
  vertical-align: middle;
}
.cp-info .cp-files-label {
  display: block;
  padding-left: 20px;
}
.cp-info .cp-files-label:focus {
  outline: none;
  background-color: #707070;
}
.cp-info .cp-files-label:active {
  background-color: #000;
}
.cp-files-collapser {
  margin-left: 5px;
}
.cp-files-collapser:after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background-size: 12px;
  background-repeat: no-repeat;
  background-position: 0 0;
  position: relative;
  top: 2px;
  left: 5px;
  background-image: url("data:image/svg+xml;base64,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");
}
.cp-files-collapser.down:after {
  transform: rotate(180deg);
}
.cp-thumbnails {
  background-color: #333;
  display: none;
  height: 130px;
  margin-top: 0;
  padding-bottom: 20px;
  padding-left: 0;
  padding-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;
}
.cp-thumbnail {
  color: #fff;
  display: inline-block;
  text-align: center;
  padding-left: 10px;
  max-width: 145px;
}
.cp-thumbnail.selected .cp-thumbnail-img {
  border: 2px solid #3572B0;
  border-radius: 3px;
}
.cp-thumbnail-group {
  margin: 0;
}
.cp-thumbnail-img-container {
  display: block;
  height: 60px;
  overflow: hidden;
  background-size: contain;
  background: no-repeat center center;
  background-size: 48px 48px;
}
.cp-thumbnail-img {
  background-color: #444;
  border: 2px solid #333;
  width: 100px;
  margin: 10px auto 0 auto;
  padding: 10px 20px;
}
.cp-thumbnail-img:hover {
  background-color: #555;
}
.cp-thumbnail-title {
  color: #f5f5f5;
  font-size: 12px;
  margin-top: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cp-thumbnail-img img {
  height: 60px;
}
</style><script type="text/javascript" charset="utf-8" async="" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/mediaviewer-1.js"></script><style type="text/css">.cp-image-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  white-space: nowrap;
  box-sizing: border-box;
}
.cp-image-preview .cp-scale-info {
  position: absolute;
  width: 100px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin-left: -50px;
  margin-top: -20px;
  border-radius: 5px;
  background: #333333;
  background: rgba(38, 38, 38, 0.5);
  line-height: 40px;
  color: #fff;
  z-index: 1;
  display: none;
}
.cp-image-preview .cp-image-container.cp-fit-width {
  width: 100%;
  height: auto;
}
.cp-image-preview .cp-image-container.cp-fit-height {
  width: auto;
  height: 100%;
}
.cp-image-preview img {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKAQMAAAC3/F3+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAABlBMVEXf39////8zI3BgAAAAEUlEQVQIW2Nk38mIjH5wICMAez4Iyz2C/F8AAAAASUVORK5CYII=") repeat;
  /* vertical-align prevents extra space from being added between
           the top of the image and it's container */
  vertical-align: middle;
}
.cp-image-preview img.pixelate {
  /* Prevent images from being smoothed when scaled up */
  image-rendering: optimizeSpeed;
  /* Legal fallback */
  image-rendering: -moz-crisp-edges;
  /* Firefox        */
  image-rendering: -o-crisp-edges;
  /* Opera          */
  image-rendering: -webkit-optimize-contrast;
  /* Safari         */
  image-rendering: optimize-contrast;
  /* CSS3 Proposed  */
  image-rendering: crisp-edges;
  /* CSS4 Proposed  */
  image-rendering: pixelated;
  /* CSS4 Proposed  */
  -ms-interpolation-mode: nearest-neighbor;
  /* IE8+           */
}
.cp-img.pannable {
  cursor: pointer;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  cursor: grab;
}
.cp-img.pannable.panning {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.cp-image-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
</style></head>

    
<body id="com-atlassian-confluence" class="theme-default  aui-layout aui-theme-default synchrony-active" data-aui-version="7.9.7">

        
            <div id="stp-licenseStatus-banner"></div>
    <ul id="assistive-skip-links" class="assistive">
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#title-heading">转至内容</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#breadcrumbs">转至导航栏</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#header-menu-bar">转至主菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#navigation">转至动作菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#quick-search-query">转至快速搜索</a></li>
</ul>
<!-- Render the dialog --><section role="dialog" id="page-view-tracker-dialog" class="aui-layer aui-dialog2" style="width:820px;" data-aui-modal="true" aria-hidden="true"><!-- Dialog header --><header class="aui-dialog2-header"><!-- The dialogs title --><h2 class="aui-dialog2-header-main">Page View Statistics</h2></header><!-- Main dialog content --><div class="aui-dialog2-content" id="page-view-tracker-content" style="height:400px; overflow-y: scroll;"></div><!-- Dialog footer --><footer class="aui-dialog2-footer"><!-- Actions to render on the right of the footer --><div class="aui-dialog2-footer-actions"><button id="page-view-tracker-help" class="aui-button aui-button-link">Help</button><button id="page-view-tracker-close" class="aui-button aui-button-link">Close</button></div></footer></section><div id="page">
<div id="full-height-container"><ul id="messageContainer"></ul>
    <div id="header-precursor">
        <div class="cell">
            
                            </div>
    </div>
        





<header id="header" role="banner" class="">
    <nav class="aui-header aui-dropdown2-trigger-group" role="navigation" data-aui-responsive="true"><div class="aui-header-inner"><div class="aui-header-primary"><h1 id="logo" class="aui-header-logo aui-header-logo-custom"><a href="http://wiki.timevale.cn:8081/"><img src="./********签署迭代 - 2.技术中心 - 天谷百科_files/atl.site.logo" alt="天谷百科" data-aui-responsive-header-index="0"><span class="aui-header-logo-text">天谷百科</span></a></h1><ul class="aui-nav" resolved="" style="width: auto;">
                            <li>
            
        
        
<a id="space-menu-link" class=" aui-dropdown2-trigger aui-nav-link" aria-controls="space-menu-link-content" aria-haspopup="true" role="button" title="空间" tabindex="0" data-aui-trigger="" resolved="" aria-expanded="false" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#space-menu-link-content">空间<span class="icon aui-icon-dropdown"></span></a><div id="space-menu-link-content" class="aui-dropdown2 aui-style-default aui-dropdown2-in-header aui-layer" role="menu" aria-hidden="true" resolved=""><div role="presentation"></div></div>
        </li>
                    <li>
            
    
        
<a id="people-directory-link" href="http://wiki.timevale.cn:8081/browsepeople.action" class=" aui-nav-imagelink" title="人员">
            <span>人员</span>
    </a>
        </li>
                                        <li class="aui-buttons">
                                    
                                        <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=PRODUCT&amp;fromPageId=*********&amp;src=quick-create" id="quick-create-page-button" class="aui-button aui-button-primary" title="创建空白页 (c)" tabindex="0">创建</a>
                                <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=PRODUCT&amp;fromPageId=*********" id="create-page-button" class="aui-button aui-button-primary clc-create-dialog-btn" title="从模板创建" tabindex="0"><span class="aui-icon aui-icon-small aui-iconfont-more">创建 </span></a>
            </li>
    </ul>
</div><div class="aui-header-secondary"><ul class="aui-nav" resolved="">
                        <li>
        <form id="quick-search" class="aui-quicksearch dont-default-focus header-quicksearch" action="http://wiki.timevale.cn:8081/dosearchsite.action" method="get"><fieldset><label for="quick-search-query" class="assistive">快速搜索</label><input id="quick-search-query" class="text app-search search quick-search-query" type="text" accesskey="q" autocomplete="off" name="queryString" title="快速搜索 (g ，g或 /)" placeholder="搜索"><input id="quick-search-submit" class="quick-search-submit" type="submit" value="搜索"><div class="aui-dd-parent quick-nav-drop-down"></div></fieldset></form>
    </li>
        <li>
            
        <a id="help-menu-link" class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" title="帮助" resolved="" aria-controls="help-menu-link-content" aria-expanded="false">
        <span class="aui-icon aui-icon-small aui-iconfont-question-filled">帮助</span>
    </a>
    <nav id="help-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                    <div class="aui-dropdown2-section">
                                <ul id="help-menu-link-leading" class="aui-list-truncate section-leading first">
                                            <li>
        
            
<a id="confluence-help-link" href="https://docs.atlassian.com/confluence/docs-613/" class="    " title="访问Confluence文档首页" target="_blank">
        在线帮助
</a>
</li>
                                            <li>
    
                
<a id="keyboard-shortcuts-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="    " title="查看可用的键盘快捷方式 (?)">
        快捷键
</a>
</li>
                                            <li>
    
            
<a id="feed-builder-link" href="http://wiki.timevale.cn:8081/dashboard/configurerssfeed.action" class="    " title="创建个性化的 RSS源。">
        RSS源建立器
</a>
</li>
                                            <li>
    
            
<a id="whats-new-menu-link" href="https://confluence.atlassian.com/display/DOC/Confluence+6.13+Release+Notes?a=false" class="    " title="">
        新功能
</a>
</li>
                                            <li>
    
                
<a id="gadget-directory-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="   user-item administration-link " title="浏览小工具">
        可用的小工具
</a>
</li>
                                            <li>
    
            
<a id="confluence-about-link" href="http://wiki.timevale.cn:8081/aboutconfluencepage.action" class="    " title="获取关于Confluence的更多信息">
        关于Confluence
</a>
</li>
                                            <li>
    
            
<a id="index.section.link" href="https://dragonsoft.atlassian.net/wiki/spaces/DAS/pages/7078120" class="    " title="">
        水印插件 - 用户手册
</a>
</li>
                                    </ul>
            </div>
            </nav>
    
    </li>
        <li>
                
    
    </li>
        <li>
                    
        
            
<a id="notifications-anchor" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="mw-anchor read aui-nav-imagelink" title="打开通知 (g ，n)"><div class="badge-i aui-icon aui-icon-small aui-iconfont-notification"></div><span class="badge-w"><span class="badge">0</span></span></a>
    
    </li>
        <li>
                                            
        <a id="user-menu-link" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless " aria-haspopup="true" data-username="taolang" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" title="桃浪" resolved="" aria-controls="user-menu-link-content" aria-expanded="false">
                    <div class="aui-avatar aui-avatar-small">
                <div class="aui-avatar-inner">
                                                                                                    <img src="./********签署迭代 - 2.技术中心 - 天谷百科_files/user-avatar">
                </div>
            </div>
            <span class="aui-icon-dropdown"></span>
        </a>
        <nav id="user-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-preferences" class="aui-list-truncate section-user-preferences first">
                                                    <li>
        
            
<a id="view-personal-space-link" href="http://wiki.timevale.cn:8081/spaces/viewspace.action?key=~taolang" class="   user-item personal-space " title="">
        个人空间
</a>
</li>
                                                    <li>
    
            
<a id="view-user-history-link" href="http://wiki.timevale.cn:8081/users/viewuserhistory.action" class="   user-item user-history popup-link " title=" (g ，r)">
        最近浏览
</a>
</li>
                                                    <li>
    
            
<a id="user-recently-worked-on" href="http://wiki.timevale.cn:8081/dashboard.action#recently-worked" class="   user-item " title="">
        最近的工作
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-content" class="aui-list-truncate section-user-content">
                                                    <li>
    
            
<a id="view-user-profile-link" href="http://wiki.timevale.cn:8081/users/viewmyprofile.action" class="   user-item user-profile " title="">
        用户信息
</a>
</li>
                                                    <li>
    
            
<a id="view-mytasks-link" href="http://wiki.timevale.cn:8081/plugins/inlinetasks/mytasks.action" class="   user-item list-user-status " title="">
        任务
</a>
</li>
                                                    <li>
    
            
<a id="user-favourites-link" href="http://wiki.timevale.cn:8081/users/viewmyfavourites.action" class="   user-item " title="">
        收藏夹
</a>
</li>
                                                    <li>
    
            
<a id="user-watches-link" href="http://wiki.timevale.cn:8081/users/viewnotifications.action" class="   user-item " title="">
        关注
</a>
</li>
                                                    <li>
    
            
<a id="user-drafts-link" href="http://wiki.timevale.cn:8081/users/viewmydrafts.action" class="   user-item " title="">
        草稿
</a>
</li>
                                                    <li>
    
            
<a id="user-network-link" href="http://wiki.timevale.cn:8081/users/viewfollow.action?username=taolang" class="   follow-link " title="">
        网络
</a>
</li>
                                                    <li>
    
            
<a id="user-settings-link" href="http://wiki.timevale.cn:8081/users/viewmysettings.action" class="   user-item " title="">
        设置
</a>
</li>
                                                    <li>
    
            
<a id="upm-requests-link" href="http://wiki.timevale.cn:8081/plugins/servlet/upm/requests?source=header_user" class="    " title="">
        Atlassian Marketplace
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-operations" class="aui-list-truncate section-user-operations">
                                                    <li>
    
            
<a id="logout-link" href="http://wiki.timevale.cn:8081/logout.action" class="   user-item logout-link " title="">
        注销
</a>
</li>
                                            </ul>
                </div>
                    </nav>
                    
    </li>
    </ul>
</div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    <br class="clear">
</header>
    

    
    	<div class="ia-splitter">
    		<div class="ia-splitter-left">
    			<div class="ia-fixed-sidebar" style="width: 393px; visibility: visible; top: 40px; left: 0px;">
                                            
                            <div class="acs-side-bar ia-scrollable-section"><div class="acs-side-bar-space-info tipsy-enabled" data-configure-tooltip="编辑空间详情"><div class="avatar"><div class="space-logo" data-key="PRODUCT" data-name="2.技术中心" data-entity-type="confluence.space"><div class="avatar-img-container"><div class="avatar-img-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=sidebar" title="2.技术中心"><img class="avatar-img" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/PRODUCT" alt="2.技术中心"></a></div></div></div></div><div class="space-information-container"><div class="name"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=sidebar" title="2.技术中心">2.技术中心</a></div><div class="flyout-handle icon aui-icon aui-icon-small aui-iconfont-edit"></div><div class="favourite-space-icon "><button title="收藏空间" id="space-favourite-add" class="space-favourite aui-icon aui-icon-small aui-iconfont-unstar"></button><button class=" space-favourite aui-icon aui-icon-small aui-iconfont-star" id="space-favourite-remove" title="取消收藏"></button></div></div></div><div class="acs-side-bar-content"><div class="acs-nav-wrapper"><div class="acs-nav" data-has-create-permission="true" data-quick-links-state="hide" data-page-tree-state="null" data-nav-type="page-tree"><div class="acs-nav-sections"></div></div></div><div class="ia-secondary-container tipsy-enabled" data-tree-type="page-tree"><div class="ia-secondary-header"><h5 class="ia-secondary-header-title page-tree"><span class="icon"></span><span class="label">页面树结构</span></h5></div><div class="ia-secondary-content">


<div class="plugin_pagetree conf-macro output-inline" data-hasbody="false" data-macro-name="pagetree">

        
        
    <ul class="plugin_pagetree_children_list plugin_pagetree_children_list_noleftspace">
        <div class="plugin_pagetree_children" id="children983056-0">
<ul class="plugin_pagetree_children_list" id="child_ul983056-0">
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus8782025-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan8782025-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8782025&amp;src=contextnavpagetreemode">产品发布</a>
        </span>
            </div>

        <div id="children8782025-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28610843-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28610843-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/UED?src=contextnavpagetreemode">UED</a>
        </span>
            </div>

        <div id="children28610843-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607659-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607659-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607659&amp;src=contextnavpagetreemode">业务域</a>
        </span>
            </div>

        <div id="children28607659-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul28607659-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus983109-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan983109-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/CRM?src=contextnavpagetreemode">CRM</a>
        </span>
            </div>

        <div id="children983109-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus2883606-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2883606-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=2883606&amp;src=contextnavpagetreemode">SAAS产品</a>
        </span>
            </div>

        <div id="children2883606-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus5377146-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan5377146-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=5377146&amp;src=contextnavpagetreemode">中台服务</a>
        </span>
            </div>

        <div id="children5377146-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607727-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607727-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607727&amp;src=contextnavpagetreemode">混合云</a>
        </span>
            </div>

        <div id="children28607727-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607725-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607725-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607725&amp;src=contextnavpagetreemode">行业云PBU</a>
        </span>
            </div>

        <div id="children28607725-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28608056-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28608056-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28608056&amp;src=contextnavpagetreemode">数据分析</a>
        </span>
            </div>

        <div id="children28608056-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607703-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607703-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607703&amp;src=contextnavpagetreemode">用户域</a>
        </span>
            </div>

        <div id="children28607703-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607729-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607729-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607729&amp;src=contextnavpagetreemode">阿里生态PO</a>
        </span>
            </div>

        <div id="children28607729-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus2883675-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2883675-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=2883675&amp;src=contextnavpagetreemode">基础业务</a>
        </span>
            </div>

        <div id="children2883675-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus8782015-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan8782015-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8782015&amp;src=contextnavpagetreemode">开放服务</a>
        </span>
            </div>

        <div id="children8782015-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus74449109-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan74449109-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=74449109&amp;src=contextnavpagetreemode">蚂蚁PO</a>
        </span>
            </div>

        <div id="children74449109-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus74449566-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan74449566-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=74449566&amp;src=contextnavpagetreemode">产品计费域</a>
        </span>
            </div>

        <div id="children74449566-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus108885011-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan108885011-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=108885011&amp;src=contextnavpagetreemode">SAAS被集成</a>
        </span>
            </div>

        <div id="children108885011-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113888966-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113888966-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113888966&amp;src=contextnavpagetreemode">业务中台</a>
        </span>
            </div>

        <div id="children113888966-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118580613-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118580613-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118580613&amp;src=contextnavpagetreemode">客户成功</a>
        </span>
            </div>

        <div id="children118580613-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126322467-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126322467-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322467&amp;src=contextnavpagetreemode">低代码</a>
        </span>
            </div>

        <div id="children126322467-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus128354362-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan128354362-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=128354362&amp;src=contextnavpagetreemode">公有云基线业务</a>
        </span>
            </div>

        <div id="children128354362-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus128370369-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan128370369-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=128370369&amp;src=contextnavpagetreemode">信息中心域</a>
        </span>
            </div>

        <div id="children128370369-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132754530-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132754530-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132754530&amp;src=contextnavpagetreemode">计费&amp;低代码</a>
        </span>
            </div>

        <div id="children132754530-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145132141-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145132141-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145132141&amp;src=contextnavpagetreemode">客户端&amp;招签宝</a>
        </span>
            </div>

        <div id="children145132141-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus147435006-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan147435006-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=147435006&amp;src=contextnavpagetreemode">公有云-SaaS</a>
        </span>
            </div>

        <div id="children147435006-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus147435016-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan147435016-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=147435016&amp;src=contextnavpagetreemode">公有云-电子签名</a>
        </span>
            </div>

        <div id="children147435016-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul147435016-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus147444704-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan147444704-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=147444704&amp;src=contextnavpagetreemode">0.公共内容</a>
        </span>
            </div>

        <div id="children147444704-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607707-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607707-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607707&amp;src=contextnavpagetreemode">1.签署服务组</a>
        </span>
            </div>

        <div id="children28607707-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul28607707-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus49546153-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan49546153-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=49546153&amp;src=contextnavpagetreemode">1.1 指引</a>
        </span>
            </div>

        <div id="children49546153-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113887833-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113887833-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113887833&amp;src=contextnavpagetreemode">业务逻辑梳理</a>
        </span>
            </div>

        <div id="children113887833-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118566009-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118566009-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118566009&amp;src=contextnavpagetreemode">签署沉淀</a>
        </span>
            </div>

        <div id="children118566009-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113886334-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113886334-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113886334&amp;src=contextnavpagetreemode">功能交接文档</a>
        </span>
            </div>

        <div id="children113886334-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113879452-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113879452-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113879452&amp;src=contextnavpagetreemode">电子合同SOP</a>
        </span>
            </div>

        <div id="children113879452-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus108882391-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan108882391-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=108882391&amp;src=contextnavpagetreemode">线上问题排查</a>
        </span>
            </div>

        <div id="children108882391-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus108862363-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan108862363-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=108862363&amp;src=contextnavpagetreemode">日常</a>
        </span>
            </div>

        <div id="children108862363-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28608459-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28608459-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28608459&amp;src=contextnavpagetreemode">设计</a>
        </span>
            </div>

        <div id="children28608459-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28608453-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28608453-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28608453&amp;src=contextnavpagetreemode">项目&amp;迭代</a>
        </span>
            </div>

        <div id="children28608453-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul28608453-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118576544-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118576544-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118576544&amp;src=contextnavpagetreemode">重点项目</a>
        </span>
            </div>

        <div id="children118576544-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113868937-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113868937-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113868937&amp;src=contextnavpagetreemode">秋风行动</a>
        </span>
            </div>

        <div id="children113868937-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus44958107-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan44958107-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=44958107&amp;src=contextnavpagetreemode">签署发布计划</a>
        </span>
            </div>

        <div id="children44958107-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118565572-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118565572-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118565572&amp;src=contextnavpagetreemode">签署项目复盘</a>
        </span>
            </div>

        <div id="children118565572-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus108868157-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan108868157-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=108868157&amp;src=contextnavpagetreemode">迭代复盘-</a>
        </span>
            </div>

        <div id="children108868157-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126321123-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126321123-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126321123&amp;src=contextnavpagetreemode">快捷签发布接口文档</a>
        </span>
            </div>

        <div id="children126321123-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126322665-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126322665-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322665&amp;src=contextnavpagetreemode">迭代详细设计</a>
        </span>
            </div>

        <div id="children126322665-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul126322665-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126328573-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126328573-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126328573&amp;src=contextnavpagetreemode">2021年迭代</a>
        </span>
            </div>

        <div id="children126328573-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus140130400-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan140130400-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140130400&amp;src=contextnavpagetreemode">2022年迭代</a>
        </span>
            </div>

        <div id="children140130400-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus166312275-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan166312275-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=166312275&amp;src=contextnavpagetreemode">2023年迭代</a>
        </span>
            </div>

        <div id="children166312275-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus192567368-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan192567368-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=192567368&amp;src=contextnavpagetreemode">2024年迭代</a>
        </span>
            </div>

        <div id="children192567368-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus210155734-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210155734-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210155734&amp;src=contextnavpagetreemode">2025年迭代</a>
        </span>
            </div>

        <div id="children210155734-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul210155734-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus210155738-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210155738-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210155738&amp;src=contextnavpagetreemode">25年Q1迭代</a>
        </span>
            </div>

        <div id="children210155738-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus217647776-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217647776-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217647776&amp;src=contextnavpagetreemode">25年Q2迭代</a>
        </span>
            </div>

        <div id="children217647776-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus217674785-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217674785-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674785&amp;src=contextnavpagetreemode">25年Q3迭代</a>
        </span>
            </div>

        <div id="children217674785-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul217674785-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217674789-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674789&amp;src=contextnavpagetreemode">20250710签署侧迭代</a>
        </span>
            </div>

        <div id="children217674789-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221514388-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221514388&amp;src=contextnavpagetreemode">20250724签署迭代</a>
        </span>
            </div>

        <div id="children221514388-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span plugin_pagetree_current" id="childrenspan*********-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;src=contextnavpagetreemode">********签署迭代</a>
        </span>
            </div>

        <div id="children*********-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221518504-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221518504&amp;src=contextnavpagetreemode">支持FDA电子签名 - 签署测详细设计</a>
        </span>
            </div>

        <div id="children221518504-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126322672-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322672&amp;src=contextnavpagetreemode">20200422迭代</a>
        </span>
            </div>

        <div id="children126322672-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126328749-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126328749&amp;src=contextnavpagetreemode">20210513迭代</a>
        </span>
            </div>

        <div id="children126328749-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan142709473-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=142709473&amp;src=contextnavpagetreemode">迭代详设模板</a>
        </span>
            </div>

        <div id="children142709473-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126322667-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126322667-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322667&amp;src=contextnavpagetreemode">项目详细设计</a>
        </span>
            </div>

        <div id="children126322667-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113872080-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113872080-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113872080&amp;src=contextnavpagetreemode">签署迭代</a>
        </span>
            </div>

        <div id="children113872080-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132770571-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132770571-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132770571&amp;src=contextnavpagetreemode">项目预研</a>
        </span>
            </div>

        <div id="children132770571-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus196200650-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan196200650-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=196200650&amp;src=contextnavpagetreemode">PAAS API发布</a>
        </span>
            </div>

        <div id="children196200650-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan196207957-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=196207957&amp;src=contextnavpagetreemode">快捷签发布流程</a>
        </span>
            </div>

        <div id="children196207957-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan196207962-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=196207962&amp;src=contextnavpagetreemode">快捷签发布计划模版</a>
        </span>
            </div>

        <div id="children196207962-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210170467-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210170467&amp;src=contextnavpagetreemode">快捷签0213发布内容</a>
        </span>
            </div>

        <div id="children210170467-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214551297-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214551297&amp;src=contextnavpagetreemode">快捷签0320发布计划</a>
        </span>
            </div>

        <div id="children214551297-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus214561176-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214561176-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214561176&amp;src=contextnavpagetreemode">快捷签0410发布计划</a>
        </span>
            </div>

        <div id="children214561176-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217648799-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217648799&amp;src=contextnavpagetreemode">快捷签0424发布过程对齐</a>
        </span>
            </div>

        <div id="children217648799-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217654449-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217654449&amp;src=contextnavpagetreemode">专家签服务拆分发布计划（0515）</a>
        </span>
            </div>

        <div id="children217654449-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217659086-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217659086&amp;src=contextnavpagetreemode">快捷签大小客发布规范</a>
        </span>
            </div>

        <div id="children217659086-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217660057-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217660057&amp;src=contextnavpagetreemode">快捷签SDK支持高版本JDK</a>
        </span>
            </div>

        <div id="children217660057-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217660864-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217660864&amp;src=contextnavpagetreemode">快捷签账号迁移发布计划【0612】</a>
        </span>
            </div>

        <div id="children217660864-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221513719-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221513719&amp;src=contextnavpagetreemode">快捷签雪花ID生成冲突优化方案</a>
        </span>
            </div>

        <div id="children221513719-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus122529673-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan122529673-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=122529673&amp;src=contextnavpagetreemode">语雀迁移-签署</a>
        </span>
            </div>

        <div id="children122529673-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus136996424-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan136996424-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=136996424&amp;src=contextnavpagetreemode">签署域技术分享</a>
        </span>
            </div>

        <div id="children136996424-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus136997514-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan136997514-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=136997514&amp;src=contextnavpagetreemode">技术回收站</a>
        </span>
            </div>

        <div id="children136997514-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus140116291-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan140116291-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140116291&amp;src=contextnavpagetreemode">工具&amp;规范</a>
        </span>
            </div>

        <div id="children140116291-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus142718240-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan142718240-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=142718240&amp;src=contextnavpagetreemode">稳定性保障</a>
        </span>
            </div>

        <div id="children142718240-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus153229662-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan153229662-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=153229662&amp;src=contextnavpagetreemode">总结</a>
        </span>
            </div>

        <div id="children153229662-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus166325544-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan166325544-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=166325544&amp;src=contextnavpagetreemode">存出证业务</a>
        </span>
            </div>

        <div id="children166325544-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus186833998-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan186833998-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=186833998&amp;src=contextnavpagetreemode">新人培养指引</a>
        </span>
            </div>

        <div id="children186833998-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28610192-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28610192-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28610192&amp;src=contextnavpagetreemode">2.认证服务组</a>
        </span>
            </div>

        <div id="children28610192-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145140809-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145140809-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145140809&amp;src=contextnavpagetreemode">3.印章服务组</a>
        </span>
            </div>

        <div id="children145140809-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus192557082-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan192557082-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=192557082&amp;src=contextnavpagetreemode">4.电签迭代文档汇总</a>
        </span>
            </div>

        <div id="children192557082-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus150342154-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan150342154-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=150342154&amp;src=contextnavpagetreemode">5.成长记录</a>
        </span>
            </div>

        <div id="children150342154-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155577982-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155577982-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155577982&amp;src=contextnavpagetreemode">公有云域</a>
        </span>
            </div>

        <div id="children155577982-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155578057-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155578057-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155578057&amp;src=contextnavpagetreemode">开放平台</a>
        </span>
            </div>

        <div id="children155578057-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118575673-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118575673-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118575673&amp;src=contextnavpagetreemode">医疗线</a>
        </span>
            </div>

        <div id="children118575673-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157753547-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157753547-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157753547&amp;src=contextnavpagetreemode">南昌第二研发中心</a>
        </span>
            </div>

        <div id="children157753547-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157774479-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157774479&amp;src=contextnavpagetreemode">中海油需求收集</a>
        </span>
            </div>

        <div id="children157774479-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159953356-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159953356&amp;src=contextnavpagetreemode">中智（北京）经济技术合作有限公司需求收集</a>
        </span>
            </div>

        <div id="children159953356-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus166319605-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan166319605-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=166319605&amp;src=contextnavpagetreemode">流版签PBU</a>
        </span>
            </div>

        <div id="children166319605-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus190228788-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan190228788-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=190228788&amp;src=contextnavpagetreemode">公有云-生态融合</a>
        </span>
            </div>

        <div id="children190228788-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199121880-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199121880-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199121880&amp;src=contextnavpagetreemode">e签宝CA</a>
        </span>
            </div>

        <div id="children199121880-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132752553-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132752553-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752553&amp;src=contextnavpagetreemode">大前端</a>
        </span>
            </div>

        <div id="children132752553-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan31916596-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=31916596&amp;src=contextnavpagetreemode">回顾</a>
        </span>
            </div>

        <div id="children31916596-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607662-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607662-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607662&amp;src=contextnavpagetreemode">架构</a>
        </span>
            </div>

        <div id="children28607662-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607665-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607665-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607665&amp;src=contextnavpagetreemode">测试</a>
        </span>
            </div>

        <div id="children28607665-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607650-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607650-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607650&amp;src=contextnavpagetreemode">规范</a>
        </span>
            </div>

        <div id="children28607650-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus49545565-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan49545565-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=49545565&amp;src=contextnavpagetreemode">安全</a>
        </span>
            </div>

        <div id="children49545565-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan76448355-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=76448355&amp;src=contextnavpagetreemode">会议纪要</a>
        </span>
            </div>

        <div id="children76448355-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus78839942-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan78839942-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=78839942&amp;src=contextnavpagetreemode">流程规范</a>
        </span>
            </div>

        <div id="children78839942-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus95584345-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan95584345-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=95584345&amp;src=contextnavpagetreemode">技术保障</a>
        </span>
            </div>

        <div id="children95584345-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113880344-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113880344-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113880344&amp;src=contextnavpagetreemode">技术知识库</a>
        </span>
            </div>

        <div id="children113880344-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan136984739-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=136984739&amp;src=contextnavpagetreemode">1迭代记录</a>
        </span>
            </div>

        <div id="children136984739-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145142028-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145142028-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/PMO?src=contextnavpagetreemode">PMO</a>
        </span>
            </div>

        <div id="children145142028-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155561693-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155561693-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155561693&amp;src=contextnavpagetreemode">技术委员会</a>
        </span>
            </div>

        <div id="children155561693-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155563455-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155563455-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155563455&amp;src=contextnavpagetreemode">管理机制沉淀</a>
        </span>
            </div>

        <div id="children155563455-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155564343-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155564343&amp;src=contextnavpagetreemode">研发中心&amp;技术委员会双周会</a>
        </span>
            </div>

        <div id="children155564343-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus169195421-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan169195421-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=169195421&amp;src=contextnavpagetreemode">PBG项目积分制</a>
        </span>
            </div>

        <div id="children169195421-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan186842522-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=186842522&amp;src=contextnavpagetreemode">编译器</a>
        </span>
            </div>

        <div id="children186842522-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan190241004-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=190241004&amp;src=contextnavpagetreemode">签署页接口安全</a>
        </span>
            </div>

        <div id="children190241004-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus206909778-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan206909778-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=206909778&amp;src=contextnavpagetreemode">CMMI文档</a>
        </span>
            </div>

        <div id="children206909778-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
    </ul>
</div>
    </ul>

    <fieldset class="hidden">
        <input type="hidden" name="treeId" value="">
        <input type="hidden" name="treeRequestId" value="/plugins/pagetree/naturalchildren.action?decorator=none&amp;excerpt=false&amp;sort=position&amp;reverse=false&amp;disableLinks=false&amp;expandCurrent=true">
        <input type="hidden" name="treePageId" value="*********">

        <input type="hidden" name="noRoot" value="false">
        <input type="hidden" name="rootPageId" value="983056">

        <input type="hidden" name="rootPage" value="">
        <input type="hidden" name="startDepth" value="0">
        <input type="hidden" name="spaceKey" value="PRODUCT">

        <input type="hidden" name="i18n-pagetree.loading" value="载入中...">
        <input type="hidden" name="i18n-pagetree.error.permission" value="无法载入页面树。看来你没有权限查看根页面。">
        <input type="hidden" name="i18n-pagetree.eeror.general" value="检索页面树时发生问题。有关详细信息，请检查服务器的日志文件。">
        <input type="hidden" name="loginUrl" value="/login.action?os_destination=%2Fpages%2Fviewpage.action%3FpageId%3D*********&amp;permissionViolation=true">
        <input type="hidden" name="mobile" value="false">

                <fieldset class="hidden">
                                                <input type="hidden" name="ancestorId" value="217674785">
                                    <input type="hidden" name="ancestorId" value="210155734">
                                    <input type="hidden" name="ancestorId" value="126322665">
                                    <input type="hidden" name="ancestorId" value="28608453">
                                    <input type="hidden" name="ancestorId" value="28607707">
                                    <input type="hidden" name="ancestorId" value="147435016">
                                    <input type="hidden" name="ancestorId" value="28607659">
                                    <input type="hidden" name="ancestorId" value="983056">
                                    </fieldset>
    </fieldset>
</div>
</div></div></div><div class="hidden"><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT&amp;src=sidebar" id="space-pages-link" title=" (g ，s)"></a><script type="text/x-template" title="logo-config-content"><h2>空间详情</h2><div class="personal-space-logo-hint">您的个人头像被作为您的个人空间的logo使用。<a href="/users/profile/editmyprofilepicture.action" target="_blank">更改您的个人头像</a>.</div></script></div></div><div class="space-tools-section"><div id="space-tools-menu-additional-items" class="hidden"><div data-label="重排页面" data-class="" data-href="/pages/reorderpages.action?key=PRODUCT">重排页面</div></div><button id="space-tools-menu-trigger" class=" aui-dropdown2-trigger aui-button aui-button-subtle tipsy-enabled aui-dropdown2-trigger-arrowless " aria-controls="space-tools-menu" aria-haspopup="true" role="button" data-aui-trigger="" resolved="" aria-expanded="false" data-collapsed-tooltip="空间管理"><span class="aui-icon aui-icon-small aui-iconfont-configure">设置</span><span class="aui-button-label">空间管理</span><span class="icon aui-icon-dropdown"></span></button><div id="space-tools-menu" class="aui-dropdown2 aui-style-default space-tools-dropdown aui-layer" role="menu" aria-hidden="true" data-aui-alignment="top left" resolved=""><div role="presentation" class="aui-dropdown2-section space-tools-navigation"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/spaces/viewspacesummary.action?key=PRODUCT&amp;src=spacetools">概览</a></li><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;src=spacetools">内容工具</a></li></ul></div></div><div role="presentation" class="aui-dropdown2-section space-operations"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;src=spacetools">重排页面</a></li></ul></div></div></div><a class="expand-collapse-trigger aui-icon aui-icon-small aui-iconfont-chevron-double-left" data-tooltip="收起侧边栏 ( [ )"></a></div>
                    
                        			<div class="ia-splitter-handle tipsy-enabled" data-tooltip="收起侧边栏 ( [ )" title=" ([)"><div class="ia-splitter-handle-highlight confluence-icon-grab-handle"></div></div></div>
    		</div>
        <!-- \#header -->

            
    
        <div id="main" class=" aui-page-panel" style="margin-left: 393px;">
                        <div id="main-header" style="top: 40px; width: 1447px;" class="">
                        
    <div id="navigation" class="content-navigation view">
                    <ul class="ajs-menu-bar">
                                                                    <li class="ajs-button normal">

        
        
                                            
    
    
    <a id="editPageLink" href="http://wiki.timevale.cn:8081/pages/editpage.action?pageId=*********" rel="nofollow" class="aui-button aui-button-subtle edit" accesskey="e" title="编辑 (e)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-edit"></span>
                        <u>E</u>编辑
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="page-favourite" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle action-page-favourite" accesskey="f" title="收藏 (f)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-unstar"></span>
                        <u>F</u>收藏
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="watch-content-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle watch-menu watch-state-initialised" title="停止关注 (w)">
                <span><span class="aui-icon aui-icon-small aui-iconfont-watch"></span> <u>W</u>关注中</span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="shareContentLink" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle share" title="Share this page with others (s或 k)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-share"></span>
                        <u>S</u>分享
        </span>    </a>
</li>
                    
        <li class="normal ajs-menu-item">
        <a id="action-menu-link" class="action aui-dropdown2-trigger-arrowless aui-button aui-button-subtle ajs-menu-title aui-dropdown2-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" data-container="#navigation" resolved="" aria-controls="action-menu" aria-expanded="false">
            <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-more"></span>
                                
            </span>
        </a>         <div id="action-menu" class="aui-dropdown2 aui-style-default aui-layer most-right-menu-item" aria-hidden="true" resolved="">
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-primary" class="section-primary first">
                                                    <li>

    
        
                                            
    
    
    <a id="view-attachments-link" href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********" rel="nofollow" class="action-view-attachments" accesskey="t" title="查看附件 (t)">
                <span>
                        <u>t</u>附件(15)
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-history-link" href="http://wiki.timevale.cn:8081/pages/viewpreviousversions.action?pageId=*********" rel="nofollow" class="action-view-history" title="">
                <span>
                        页面历史
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-page-permissions-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-page-permissions" title="编辑限制">
                <span>
                        限制
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-secondary" class="section-secondary">
                                                    <li>

    
        
                                            
    
    
    <a id="view-page-info-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-view-info" title="">
                <span>
                        页面信息
        </span>    </a>
</li>
                                                <li>

    
            
                                            
    
    
    <a id="view-resolved-comments" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="" title="">
                <span>已解决评论 (0)</span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="view-in-hierarchy-link" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;openId=*********#selectedPageInHierarchy" rel="nofollow" class="" title="">
                <span>
                        以层级方式查看
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-source-link" href="http://wiki.timevale.cn:8081/plugins/viewsource/viewpagesrc.action?pageId=*********" rel="nofollow" class="action-view-source popup-link" title="">
                <span>
                        查看页面源代码
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-pdf-link" href="http://wiki.timevale.cn:8081/spaces/flyingpdf/pdfpageexport.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        导出为PDF
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="com-k15t-confluence-scroll-html-launcher" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        Export to HTML
        </span>    <section role="dialog" id="k15t-exp-html-no-export-dialog" class="aui-layer aui-dialog2 aui-dialog2-small aui-dialog2-warning" aria-hidden="true">    <header class="aui-dialog2-header">        <h2 class="aui-dialog2-header-main">Page Not Available</h2>        <a class="aui-dialog2-header-close">            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog">Close</span>        </a>    </header>    <div class="aui-dialog2-content">        <p>You cannot export this page, because it is not available in the current version, variant, or language.</p>    </div>    <footer class="aui-dialog2-footer">        <div class="aui-dialog2-footer-actions">            <button id="k15t-exp-html-no-export-dialog-close-button" class="aui-button aui-button-link">Close</button>        </div>        <div class="aui-dialog2-footer-hint">           <span style="  background: linear-gradient(to right, #1062fb 0, #1062fb 33.3%, #2eb785 33.3%, #2eb785 66.6%, #ffc420 66.6%);  height: .5em;  width: 2.625em;  display: inline-block;  position: relative;           "></span>         </div>    </footer></section></a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-word-link" href="http://wiki.timevale.cn:8081/exportword?pageId=*********" rel="nofollow" class="action-export-word" title="">
                <span>
                        导出为Word
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="import-word-doc" href="http://wiki.timevale.cn:8081/pages/worddav/uploadimport.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        Doc文件导入
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-modify" class="section-modify">
                                                    <li>

    
        
                                            
    
    
    <a id="action-copy-page-link" href="http://wiki.timevale.cn:8081/pages/copypage.action?idOfPageToCopy=*********&amp;spaceKey=PRODUCT" rel="nofollow" class="action-copy" title="">
                <span>
                        复制
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-move-page-dialog-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="action-move" title="">
                <span>
                        移动
        </span>    </a>
</li>
                                        </ul>
                </div>
                    </div>
    </li>
            </ul>
    </div>

            
            <div id="title-heading" class="pagetitle with-breadcrumbs">
                
                                    <div id="breadcrumb-section">
                        
    
    
    <ol id="breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT&amp;src=breadcrumbs-collector">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=breadcrumbs-expanded">技术中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607659&amp;src=breadcrumbs-expanded">业务域</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=147435016&amp;src=breadcrumbs-expanded">公有云-电子签名</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607707&amp;src=breadcrumbs-expanded">1.签署服务组</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28608453&amp;src=breadcrumbs-expanded">项目&amp;迭代</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322665&amp;src=breadcrumbs-expanded">迭代详细设计</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210155734&amp;src=breadcrumbs-expanded">2025年迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674785&amp;src=breadcrumbs-parent">25年Q3迭代</a></span>
                                                                    </li></ol>


                    </div>
                
                
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-end" class="assistive">跳到banner的尾部</a>
<div id="page-banner-start" class="assistive"></div>

                    
            <div id="page-metadata-banner" style="visibility: visible;"><ul class="banner"><li id="system-content-items" class="noprint"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="未限制" id="content-metadata-page-restrictions" class="aui-icon aui-icon-small aui-iconfont-unlocked system-metadata-restrictions" resolved=""></a><a href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********&amp;metadataLink=true" id="content-metadata-attachments" class="aui-icon aui-icon-small aui-iconfont-attachment" original-title="15 个附件"></a></li><li class="page-metadata-item noprinthas-button" id="content-metadata-jira-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="" id="content-metadata-jira" class="aui-button aui-button-subtle content-metadata-jira tipsy-disabled hidden"><span>JIRA 链接</span></a></li></ul></div>
            

<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-start" class="assistive">回到标题开始</a>
<div id="page-banner-end" class="assistive"></div>
    

                <h1 id="title-text" class="with-breadcrumbs" style="display: block;">
                                                <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">********签署迭代</a>
                                    </h1>
            </div>
        </div><!-- \#main-header -->
        
        

        <div id="sidebar-container">
                                                </div><!-- \#sidebar-container -->

        
    

        




            
    

                                
    

    
    
        
    
    
                    
    

    

    
            
        

    
    

    
            
        



    
<div id="content" class="page view">
    


<div id="action-messages">
                        </div>



            <script type="text/x-template" title="searchResultsGrid">
    <table class="aui">
        <thead>
            <tr class="header">
                <th class="search-result-title">页面标题</th>
                <th class="search-result-space">空间</th>
                <th class="search-result-date">更新于</th>
            </tr>
        </thead>
    </table>
</script>
<script type="text/x-template" title="searchResultsGridCount">
    <p class="search-result-count">{0}</p>
</script>
<script type="text/x-template" title="searchResultsGridRow">
    <tr class="search-result">
        <td class="search-result-title"><a href="{1}" class="content-type-{2}"><span>{0}</span></a></td>
        <td class="search-result-space"><a class="space" href="/display/{4}/" title="{3}">{3}</a></td>
        <td class="search-result-date"><span class="date" title="{6}">{5}</span></td>
    </tr>
</script>
        
    
            

        
                            
    

                    

        
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-end" class="assistive">转至元数据结尾</a>
<div id="page-metadata-start" class="assistive"></div>

    <div class="page-metadata">
        <ul>
            <li class="page-metadata-modification-info">
                
        
    
        
    
        
            
            由<span class="author">     <a href="http://wiki.timevale.cn:8081/display/~sanyi" class="url fn confluence-userlink userlink-0" data-username="sanyi" title="" data-user-hover-bound="true">三一</a></span>创建, 最终由<span class="editor">     <a href="http://wiki.timevale.cn:8081/display/~luoan" class="url fn confluence-userlink userlink-1" data-username="luoan" title="" data-user-hover-bound="true">洛安</a></span>修改于<a class="last-modified" title="查看变更" href="http://wiki.timevale.cn:8081/pages/diffpagesbyversion.action?pageId=*********&amp;selectedPageVersions=42&amp;selectedPageVersions=43">昨天8:58 下午</a>
                </li>
        </ul>
    </div>


<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-start" class="assistive">转至元数据起始</a>
<div id="page-metadata-end" class="assistive"></div>

        
                                            
        <div id="main-content" class="wiki-content">
                           
        <p><strong></strong></p><div class="toc-macro client-side-toc-macro  conf-macro output-block hidden-outline" data-headerelements="H1,H2,H3,H4,H5,H6,H7" data-hasbody="false" data-macro-name="toc"><ul style=""><li><span class="toc-item-body" data-outline="1"><span class="toc-outline">1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-0.%E5%8F%98%E6%9B%B4" class="toc-link">0. 变更</a></span></li><li><span class="toc-item-body" data-outline="2"><span class="toc-outline">2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-1.%E9%A1%B9%E7%9B%AE%E5%90%8D" class="toc-link">1. 项目名</a></span></li><li><span class="toc-item-body" data-outline="3"><span class="toc-outline">3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-2.%E6%96%B9%E6%A1%88%E7%9B%AE%E6%A0%87" class="toc-link">2. 方案目标</a></span></li><li><span class="toc-item-body" data-outline="4"><span class="toc-outline">4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.%E8%AE%BE%E8%AE%A1%E5%8F%8A%E6%B5%81%E7%A8%8B" class="toc-link">3. 设计及流程</a></span><ul style=""><li><span class="toc-item-body" data-outline="4.1"><span class="toc-outline">4.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.1SaaS%E5%8D%B0%E7%AB%A0%E4%BD%BF%E7%94%A8%E6%83%85%E5%86%B5%E5%9F%8B%E7%82%B9" class="toc-link">3.1&nbsp;SaaS印章使用情况埋点</a></span><ul style=""><li><span class="toc-item-body" data-outline="4.1.1"><span class="toc-outline">4.1.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.1.1SaaS%E7%BA%B8%E8%B4%A8%E5%8D%B0%E7%AB%A0%E5%AE%A1%E6%A0%B8%E6%83%85%E5%86%B5%E5%88%86%E6%9E%90(%E5%90%8E%E7%AB%AF%E5%9F%8B%E7%82%B9)" class="toc-link">3.1.1 SaaS纸质印章审核情况分析(后端埋点)</a></span></li><li><span class="toc-item-body" data-outline="4.1.2"><span class="toc-outline">4.1.2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.1.2SaaS%E5%8D%B0%E7%AB%A0%E6%8E%88%E6%9D%83%E5%AE%8C%E6%88%90%E4%BD%BF%E7%94%A8%E6%83%85%E5%86%B5%E5%88%86%E6%9E%90(%E5%90%8E%E7%AB%AF%E5%9F%8B%E7%82%B9)" class="toc-link">3.1.2&nbsp;SaaS印章授权完成使用情况分析(后端埋点)</a></span></li></ul></li><li><span class="toc-item-body" data-outline="4.2"><span class="toc-outline">4.2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.2%E6%B3%95%E4%BA%BA%E7%AB%A0%E5%88%9B%E5%BB%BA%E6%B5%81%E7%A8%8B%E4%BC%98%E5%8C%96" class="toc-link">3.2&nbsp;法人章创建流程优化</a></span></li><li><span class="toc-item-body" data-outline="4.3"><span class="toc-outline">4.3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.3%E3%80%90V3api%E3%80%91%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6%E9%98%85%E8%AF%BB%E6%97%B6%E9%95%BF%E6%8E%A7%E5%88%B6%EF%BC%88%E4%BA%91%E5%BC%80%EF%BC%89%EF%BC%88%E5%BB%B6%E8%87%B3%E4%B8%8B%E6%9C%9F%EF%BC%89" class="toc-link">3.3&nbsp;&nbsp;【V3api】合同文件阅读时长控制 （云开）（延至下期）</a></span></li><li><span class="toc-item-body" data-outline="4.4"><span class="toc-outline">4.4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.4%E3%80%90%E7%AD%BE%E7%BD%B2%E3%80%91v3api%E6%94%AF%E6%8C%81%E9%98%85%E8%AF%BB%E5%88%B0%E5%BA%95%E4%BA%8C%E6%AC%A1%E7%A1%AE%E8%AE%A4%EF%BC%88%E4%BA%91%E5%BC%80%EF%BC%89" class="toc-link">3.4&nbsp;【签署】v3api支持阅读到底二次确认 （云开）</a></span></li><li><span class="toc-item-body" data-outline="4.5"><span class="toc-outline">4.5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.5%E3%80%90SaaS%E5%8F%91%E8%B5%B7%E3%80%91%E8%87%AA%E5%AE%9A%E4%B9%89%E7%AD%BE%E7%BD%B2%E5%A3%B0%E6%98%8E%E6%A8%A1%E6%9D%BF%EF%BC%88%E4%BA%91%E5%BC%80%EF%BC%89%EF%BC%88%E5%BB%B6%E8%87%B3%E4%B8%8B%E6%9C%9F%EF%BC%89" class="toc-link">3.5&nbsp;【SaaS发起】自定义签署声明模板&nbsp; （云开）&nbsp;（延至下期）</a></span></li><li><span class="toc-item-body" data-outline="4.6"><span class="toc-outline">4.6</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.6%E3%80%90V3api%E3%80%91%E4%B8%AA%E4%BA%BA%E8%BA%AB%E4%BB%BD%E4%BF%A1%E6%81%AF%E4%B8%8D%E4%B8%80%E8%87%B4%E6%8A%A5%E9%94%99%E4%BC%98%E5%8C%96%EF%BC%88%E4%BA%91%E5%BC%80%EF%BC%89" class="toc-link">3.6&nbsp;【V3api】个人身份信息不一致报错优化 （云开）</a></span></li><li><span class="toc-item-body" data-outline="4.7"><span class="toc-outline">4.7</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.7%E3%80%90%E5%AD%98%E5%87%BA%E8%AF%81%E3%80%91%E5%87%BA%E8%AF%81%E6%A8%A1%E6%9D%BF%E4%BC%98%E5%8C%96%EF%BC%88%E6%B4%9B%E5%AE%89%EF%BC%89" class="toc-link">3.7&nbsp;&nbsp;【存出证】出证模板优化（洛安）</a></span></li><li><span class="toc-item-body" data-outline="4.8"><span class="toc-outline">4.8</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.8%E3%80%90%E5%AD%98%E5%87%BA%E8%AF%81%E3%80%91%E5%87%BA%E8%AF%81%E5%9F%8B%E7%82%B9%E5%A2%9E%E5%8A%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E5%B1%9E%E6%80%A7%EF%BC%88%E6%B4%9B%E5%AE%89%EF%BC%89" class="toc-link">3.8&nbsp;【存出证】出证埋点增加客户端属性（洛安）</a></span></li><li><span class="toc-item-body" data-outline="4.9"><span class="toc-outline">4.9</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.10%E3%80%90%E7%AD%BE%E7%BD%B2%E3%80%91%E9%9D%9E%E6%A0%87api%E6%94%AF%E6%8C%81%E6%B3%95%E4%BA%BA%E6%8E%88%E6%9D%83%E9%9D%99%E9%BB%98%E7%AD%BE%EF%BC%88%E6%B4%9B%E5%AE%89%EF%BC%89" class="toc-link">3.10&nbsp; 【签署】非标api支持法人授权静默签 （洛安）</a></span></li><li><span class="toc-item-body" data-outline="4.10"><span class="toc-outline">4.10</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3-3.11%E3%80%90V3api%E3%80%91%E6%89%B9%E9%87%8F%E7%AD%BE%E6%8E%A5%E5%8F%A3%E6%94%AF%E6%8C%81%E5%8F%AF%E4%B8%8D%E4%BC%A0%E5%85%A5%E5%A7%93%E5%90%8D%EF%BC%88%E6%B4%9B%E5%AE%89%EF%BC%89" class="toc-link">3.11 【V3api】批量签接口支持可不传入姓名（洛安）</a></span></li></ul></li></ul></div><strong><br></strong><p></p><p><strong><br></strong></p><h1 id="id-********签署迭代-0.变更"><strong>0. 变更</strong></h1><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 291.25px;" resolved=""><colgroup><col style="width: 64.3px;"><col style="width: 84.2125px;"><col style="width: 81.575px;"><col style="width: 60.1625px;"></colgroup><tbody><tr><td class="confluenceTd">版本号</td><td class="confluenceTd">日期</td><td class="confluenceTd">修改人</td><td class="confluenceTd">备注</td></tr><tr><td class="confluenceTd">v1.0</td><td class="confluenceTd"><div class="content-wrapper"><p><time datetime="2025-07-22" class="date-past">2025-7-22</time>&nbsp;</p></div></td><td class="confluenceTd"><p><br></p></td><td class="confluenceTd"><p><br></p></td></tr></tbody></table></div><h1 id="id-********签署迭代-1.项目名"><strong><strong>1. 项目名</strong><br></strong></h1><p><span style="color: rgb(51,51,51);"><strong>迭代名：</strong></span><strong>********迭代</strong></p><p><strong>需求文档：<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221515371">0807迭代</a></strong></p><p><span style="color: rgb(0,0,0);"><strong><br></strong></span></p><h1 id="id-********签署迭代-2.方案目标"><span style="color: rgb(0,0,0);"><strong>2. 方案目标</strong></span></h1><p><br></p><h1 id="id-********签署迭代-3.设计及流程"><span style="color: rgb(0,0,0);"><strong>3. 设计及流程</strong></span></h1><h2 id="id-********签署迭代-3.1SaaS印章使用情况埋点"><span style="color: rgb(0,0,0);"><strong>3.1&nbsp;<span>SaaS印章使用情况埋点</span></strong></span></h2><p><span style="color: rgb(0,0,0);"><strong><span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********">Java后端埋点接入</a><br></span></strong></span></p><h3 class="auto-cursor-target" id="id-********签署迭代-3.1.1SaaS纸质印章审核情况分析(后端埋点)"><strong>3.1.1 SaaS纸质印章审核情况分析<strong>(后端埋点)</strong></strong></h3><p><span class="confluence-embedded-file-wrapper"><img class="confluence-embedded-image" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/印章审核埋点.png" data-image-src="/download/attachments/*********/%E5%8D%B0%E7%AB%A0%E5%AE%A1%E6%A0%B8%E5%9F%8B%E7%82%B9.png?version=3&amp;modificationDate=1753406734000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521821" data-linked-resource-version="3" data-linked-resource-type="attachment" data-linked-resource-default-alias="印章审核埋点.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><br></p><p class="auto-cursor-target"><strong>a.&nbsp;<span>印章_个人印章审核情况</span></strong></p><p><strong>事件id：<span style="color: rgb(51,51,51);">seal_creation_completed_</span><span>review_per</span></strong></p><p><strong><span>描述：用于纸质印章<strong>审核完成</strong>后统计</span></strong></p><p><strong>埋点数据：</strong></p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 50.6015%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 22.619%;"><col style="width: 19.0476%;"><col style="width: 58.3333%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 433px; z-index: 3; width: 721px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">seal_subject_name</td><td class="confluenceTd">印章主体名称</td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd"><p>oid</p></td><td class="confluenceTd">印章主体oid</td><td class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">review_method</td><td colspan="1" class="confluenceTd">审核方式</td><td colspan="1" class="confluenceTd"><p><span>AI审核/人工审核</span></p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">result</td><td colspan="1" class="confluenceTd">结果</td><td colspan="1" class="confluenceTd">是/否</td></tr></tbody></table></div><p><strong><span><br></span></strong></p><p><strong><span>b.&nbsp;<span style="color: rgb(71,86,105);">印章_企业印章审核情况</span></span></strong></p><p><strong>事件id：<a rel="nofollow"><span style="color: rgb(51,51,51);">seal_creation_completed_</span></a>review_ent</strong></p><p><strong>描述：用于纸质印章<strong>审核完成</strong>后统计</strong></p><p><strong>埋点数据：</strong></p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 50.6015%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 22.619%;"><col style="width: 19.0476%;"><col style="width: 58.3333%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 433px; z-index: 3; width: 721px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">seal_subject_name</td><td class="confluenceTd">印章主体名称</td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd"><p>oid</p></td><td class="confluenceTd">印章主体oid</td><td class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">seal_type</td><td colspan="1" class="confluenceTd">印章类型</td><td colspan="1" class="confluenceTd"><p>公章/法人章/合同专用章/财务专用章/人事专用章/其他</p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">vipVersion</td><td colspan="1" class="confluenceTd">会员版本</td><td colspan="1" class="confluenceTd"><p>体验版/基础班/专业版/高级版/旗舰版/生态版</p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">review_method</td><td colspan="1" class="confluenceTd">审核方式</td><td colspan="1" class="confluenceTd"><p>AI审核/人工审核</p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">result</td><td colspan="1" class="confluenceTd">结果</td><td colspan="1" class="confluenceTd">是/否</td></tr></tbody></table></div><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong><span>埋点入口：</span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong style="color: rgb(0,0,0);letter-spacing: 0.0px;"><span>seal-manager</span></strong></span></p><ol><li><span style="color: rgb(0,0,0);"><strong><span><span style="color: rgb(13,13,13);">人工审核</span></span></strong></span><ol><li><span style="color: rgb(0,0,0);"><span style="color: rgb(13,13,13);">SealService#auditSeal</span><strong><span><br></span></strong></span></li></ol></li><li><span style="color: rgb(0,0,0);"><strong><span><span style="color: rgb(13,13,13);">AI审核</span></span></strong></span></li><ol><li><p>SealWaitAuditForAiAuditConsumer</p></li><li><p><span style="color: rgb(13,13,13);">AiAuditSealIdentifyResultConsumer</span></p></li></ol></ol><p><span style="color: rgb(255,0,0);">待确认：&nbsp;会员版本数据在saas-common-manage服务，seal-manager是saas-common-manage 的下游服务，反向调用？？？</span></p><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><h3 class="auto-cursor-target" id="id-********签署迭代-3.1.2SaaS印章授权完成使用情况分析(后端埋点)"><strong>3.1.2&nbsp;</strong><strong style="font-size: 14.0px;letter-spacing: 0.0px;">SaaS印章授权完成使用情况分析<strong>(后端埋点)</strong></strong></h3><p><strong style="font-size: 14.0px;letter-spacing: 0.0px;"><strong><br></strong></strong></p><pre>端的信息一路透传保存在扩展表<span>seal_grant_ext</span>的<span>ext</span>字段中，目前seal_grant_ext 只有开放接口在使用</pre><p>目前埋点数据要先将cleintId持久化，所以所有印章授权新增和更新操作都要透传参数</p><p>一级授权：新增、更新 （单个、批量）</p><p>二级授权：新增、更新（单个、批量）</p><p>需要埋点的端均需要透传</p><p><span style="color: rgb(255,0,0);">注意：还在授权过程中的数据，授权完成以后 来源会被标记为其它，因为没有存clientId</span></p><p><span class="confluence-embedded-file-wrapper"><img class="confluence-embedded-image" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/印章授权神策埋点.png" data-image-src="/download/attachments/*********/%E5%8D%B0%E7%AB%A0%E6%8E%88%E6%9D%83%E7%A5%9E%E7%AD%96%E5%9F%8B%E7%82%B9.png?version=2&amp;modificationDate=1753406745000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521822" data-linked-resource-version="2" data-linked-resource-type="attachment" data-linked-resource-default-alias="印章授权神策埋点.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><strong style="font-size: 14.0px;letter-spacing: 0.0px;"><strong><br></strong></strong></p><p class="auto-cursor-target"><strong>a.&nbsp;企业印章授权-企业内授权</strong></p><p><strong>事件id：<span style="color: rgb(51,51,51);">internal_</span><span style="color: rgb(51,51,51);">ent_seal_authorization</span></strong></p><p><strong>描述：用于分析企业内SaaS企业印章授权完成后统计</strong></p><p><strong>埋点数据：</strong></p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 64.8872%;" resolved=""><colgroup><col style="width: 19.8892%;"><col style="width: 18.6657%;"><col style="width: 61.4441%;"></colgroup><tbody><tr><th class="confluenceTh">字段</th><th class="confluenceTh">名称</th><th class="confluenceTh">说明</th></tr><tr><td colspan="1" class="confluenceTd">client_type</td><td colspan="1" class="confluenceTd">端类型</td><td colspan="1" class="confluenceTd">WEB、飞书、钉钉、企微、SaaS纯API、SaaS开放页面、其他</td></tr><tr><td class="confluenceTd">seal_subject_name</td><td class="confluenceTd">印章主体名称</td><td rowspan="2" class="confluenceTd"><p>如果印章是跨企业印章 印章主体oid 取哪个？谁操作 取谁吗</p></td></tr><tr><td class="confluenceTd"><p>oid</p></td><td class="confluenceTd">印章主体oid</td></tr><tr><td class="confluenceTd">seal_type</td><td class="confluenceTd">印章类型</td><td class="confluenceTd"><p><span>公章/法人章 (<span style="color: rgb(255,0,0);">应该是</span></span><span style="letter-spacing: 0.0px;color: rgb(255,0,0);">法定代表人章</span>)/合同专用章/财务专用章/人事专用章/其他</p></td></tr><tr><td class="confluenceTd">vipVersion</td><td class="confluenceTd">会员版本</td><td class="confluenceTd"><p><span>体验版/基础班/专业版/高级版/旗舰版/生态版</span></p></td></tr><tr><td class="confluenceTd">authorization_object</td><td class="confluenceTd">授权对象</td><td class="confluenceTd"><p>指定角色/指定部门/指定成员/全部成员</p></td></tr><tr><td class="confluenceTd">authorization_permissions</td><td class="confluenceTd">授权权限</td><td class="confluenceTd">印章使用、仅用印审批、使用+审批</td></tr><tr><td colspan="1" class="confluenceTd">authority_scope</td><td colspan="1" class="confluenceTd">授权范围</td><td colspan="1" class="confluenceTd">全部合同/合同模板/合同审批流/开发者应用/单次用印授权/平台用印</td></tr></tbody></table></div><p><strong style="font-size: 14.0px;letter-spacing: 0.0px;"><strong><br></strong></strong></p><p><strong style="font-size: 14.0px;letter-spacing: 0.0px;"><strong>b.&nbsp;企业印章授权-企业外授权</strong></strong></p><p><strong>事件id：<span style="color: rgb(51,51,51);">cross_</span><span style="color: rgb(51,51,51);">ent_seal_authorization</span></strong></p><p><strong>描述：用于分析跨企业SaaS企业印章授权完成后统计</strong></p><p><strong>埋点数据：</strong></p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 64.8872%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 19.8892%;"><col style="width: 18.6657%;"><col style="width: 61.4441%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 433px; z-index: 3; width: 925px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="字段: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">字段</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="名称: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">名称</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td colspan="1" class="confluenceTd">client_type</td><td colspan="1" class="confluenceTd">端类型</td><td colspan="1" class="confluenceTd">WEB、飞书、钉钉、企微、SaaS纯API、SaaS开放页面、其他</td></tr><tr role="row"><td class="confluenceTd">seal_subject_name</td><td class="confluenceTd">印章主体名称</td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd"><p>oid</p></td><td class="confluenceTd">印章主体oid</td><td class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">authorization_oid</td><td colspan="1" class="confluenceTd">被授权企业主体oid</td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">seal_type</td><td class="confluenceTd">印章类型</td><td class="confluenceTd"><p>公章/法人章/合同专用章/财务专用章/人事专用章/其他</p></td></tr><tr role="row"><td class="confluenceTd">vipVersion</td><td class="confluenceTd">会员版本</td><td class="confluenceTd"><p>体验版/基础班/专业版/高级版/旗舰版/生态版</p></td></tr><tr role="row"><td class="confluenceTd">authorization_object</td><td class="confluenceTd">授权对象</td><td class="confluenceTd"><p>被授权企业下应用/被授权企业的管理员/法人角色</p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">authority_scope</td><td colspan="1" class="confluenceTd">授权范围</td><td colspan="1" class="confluenceTd">全部合同/合同模板/合同审批流/开发者应用/单次用印授权/平台用印</td></tr></tbody></table></div><p><br></p><p class="auto-cursor-target">目前各端（PC）<span style="color: rgb(0,51,102);">x-tsign-client-id传值情况</span></p><div class="table-wrap"><table class="confluenceTable" resolved=""><colgroup><col><col><col><col></colgroup><tbody><tr><th colspan="2" class="confluenceTh">业务端</th><th class="confluenceTh"><p><span style="color: rgb(0,51,102);">x-tsign-client-id</span></p></th><th class="confluenceTh">备注</th></tr><tr><td colspan="2" class="confluenceTd"><span class="td-span md-focus">saas </span></td><td class="confluenceTd"><span class="td-span">WEB</span></td><td class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd">飞书</td><td colspan="1" class="confluenceTd">FEI_SHU</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td rowspan="2" class="confluenceTd">钉钉</td><td colspan="1" class="confluenceTd">钉签</td><td colspan="1" class="confluenceTd">DING_TALK</td><td rowspan="2" class="confluenceTd"><p><br></p></td></tr><tr><td colspan="1" class="confluenceTd">e人事</td><td colspan="1" class="confluenceTd">DING_TALK</td></tr><tr><td colspan="2" class="confluenceTd">企微</td><td colspan="1" class="confluenceTd">WE_WORK</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd">SaaS纯API</td><td colspan="1" class="confluenceTd">无</td><td colspan="1" class="confluenceTd">后端接口自己定义一个标识 OPEN_API</td></tr><tr><td rowspan="2" class="confluenceTd">SaaS开放页面</td><td colspan="1" class="confluenceTd">印章开放页面</td><td colspan="1" class="confluenceTd">WEB</td><td colspan="1" class="confluenceTd"><p>前端传 OPEN_PAGE</p></td></tr><tr><td colspan="1" class="confluenceTd">企业控制台开放页面</td><td colspan="1" class="confluenceTd">没有值</td><td colspan="1" class="confluenceTd">需要前端传值 <span>OPEN_PAGE</span></td></tr><tr><td rowspan="3" class="confluenceTd">其他</td><td colspan="1" class="confluenceTd">薪福通（橙蜂）（单独的老版本企业控制台）</td><td colspan="1" class="confluenceTd">Unite</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li class="checked" data-inline-task-id="2">这个归位其他</li></ul></td></tr><tr><td colspan="1" class="confluenceTd">平台用印</td><td colspan="1" class="confluenceTd"><br></td><td rowspan="2" class="confluenceTd"><p>后端，标记为其他</p><p>com.timevale.footstone.seal.facade.SealGrantFacade#sealUseApply</p></td></tr><tr><td colspan="1" class="confluenceTd">用印申请（单次用印、平台用印）</td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><p class="auto-cursor-target"><span style="color: rgb(255,0,0);">备注：有些其他业务域 直接调用的RPC接口，本次埋点 可能无法改到，来源暂时标记为其它？还是说让他们配合改呢？</span></p><p>比如：钉签&nbsp;</p><p>创建跨企业印章授权</p><p>com.timevale.footstone.seal.facade.openv3.OpenPageV3SealGrantFacade#createSealGrant</p><p><br></p><p><strong>前置处理：新增更新授权需要记录 端类型&nbsp;</strong></p><p><strong><span class="confluence-embedded-file-wrapper"><img class="confluence-embedded-image" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-22_16-30-26.png" data-image-src="/download/attachments/*********/image2025-7-22_16-30-26.png?version=1&amp;modificationDate=1753173027000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521229" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_16-30-26.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></strong></p><ul><li><strong>1.前端往请求头传入段类型标识给后端</strong><ul><li><strong>WEB、飞书、钉钉、企微、SaaS纯API、SaaS开放页面、其他<br></strong></li></ul></li><li><strong style="letter-spacing: 0.0px;">2.后端存授权扩展表。</strong></li><li><strong style="letter-spacing: 0.0px;">3.签授权书/意愿完成后埋点</strong><ul><li><strong style="letter-spacing: 0.0px;">埋点入口：</strong><strong style="letter-spacing: 0.0px;">footstone-seal</strong><ul><li>FlowArchiveConsumer&nbsp; 签署完成-&gt;更新一级授权信息</li><li>SecondSealGrantFlowArchiveConsumer&nbsp;签署完成-&gt;更新二级授权信息</li><li>WillFinishConsumer 意愿完成-&gt;更新一级、二级授权信息</li><li><p>RealNameForSealUseApplyConsumer&nbsp;<span style="color: rgb(0,51,102);">监听实名消息-&gt;处理用印申请</span></p></li></ul></li></ul></li></ul><p><br></p><p>神策地址：<a href="https://data-web.esign.cn/zerotrustQRV/?redirect=%2Fdefault" class="external-link" rel="nofollow">https://data-web.esign.cn/zerotrustQRV/?redirect=%2Fdefault</a></p><p>对接文档：<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********">Java后端埋点接入</a></p><p><br></p><p><br></p><p><strong>获取企业会员版本rpc接口：</strong></p><p>com.timevale.saas.common.manage.common.service.api.SaasVipCrmRpcService#getAccountVip</p><div class="code panel pdl conf-macro output-block" style="border-width: 1px;" data-hasbody="true" data-macro-name="code"><div class="codeContent panelContent pdl">
<div><div id="highlighter_325448" class="syntaxhighlighter sh-confluence nogutter  xml"><div class="toolbar"><span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar_item command_help help">?</a></span></div><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container" title="Hint: double-click to select code"><div class="line number1 index0 alt2"><code class="xml plain">&lt;</code><code class="xml keyword">dependency</code><code class="xml plain">&gt;</code></div><div class="line number2 index1 alt1"><code class="xml spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="xml plain">&lt;</code><code class="xml keyword">artifactId</code><code class="xml plain">&gt;saas-common-manage-common-service-facade&lt;/</code><code class="xml keyword">artifactId</code><code class="xml plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="xml spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="xml plain">&lt;</code><code class="xml keyword">name</code><code class="xml plain">&gt;saas-common-manage/facade&lt;/</code><code class="xml keyword">name</code><code class="xml plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="xml spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="xml plain">&lt;</code><code class="xml keyword">version</code><code class="xml plain">&gt;1.4.6-SNAPSHOT&lt;/</code><code class="xml keyword">version</code><code class="xml plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="xml plain">&lt;/</code><code class="xml keyword">dependency</code><code class="xml plain">&gt;</code></div></div></td></tr></tbody></table></div></div>
</div></div><p>请求参数：{"accountGid":"","accountId":"","clientId":""}</p><p><br></p><p><br></p><h2 id="id-********签署迭代-3.2法人章创建流程优化"><strong style="color: rgb(0,0,0);font-size: 20.0px;letter-spacing: -0.008em;"><span>3.2&nbsp;法人章创建流程优化</span></strong></h2><p><strong>新增法定代表人章页面</strong></p><p><strong>场景1：未进行法人授权，页面如下</strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="400" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-23_11-0-34.png" data-image-src="/download/attachments/*********/image2025-7-23_11-0-34.png?version=1&amp;modificationDate=*************&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="*********" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-0-34.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p>改动：<br>footstone-seal facade接口：<span style="color: rgb(31,31,31);">RuleGrantFacade#queryLegalAuthInfo</span></p><p><span style="color: rgb(31,31,31);">返回字段<span style="color: rgb(142,0,75);">authSignDoc里面的</span></span><span style="color: rgb(199,125,187);">filekey、</span><span style="color: rgb(199,125,187);">url<span style="color: rgb(0,51,102);">返回的是线下法人授权书，改成线上法人授权书。</span></span></p><p><span style="color: rgb(199,125,187);"><br></span></p><p><strong><span style="color: rgb(0,51,102);">场景2：发起线上法人授权申请，页面如下：</span></strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-23_11-2-7.png" data-image-src="/download/attachments/*********/image2025-7-23_11-2-7.png?version=1&amp;modificationDate=1753239727000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521712" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-2-7.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><strong style="letter-spacing: 0.0px;">方案：</strong></p><p><span style="color: rgb(0,0,0);">footstone-seal facade接口：RuleGrantFacade#queryLegalAuthInfo 返回字段authFlowInfo包含了fileKey、下载地址，</span></p><p><br></p><p><span style="color: rgb(0,0,0);">运营支撑-用户中心-授权管理-审核页面 有预览功能，看下是不是能参照这个实现</span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-23_10-35-24.png" data-image-src="/download/attachments/*********/image2025-7-23_10-35-24.png?version=1&amp;modificationDate=1753238124000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521688" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_10-35-24.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span>前端实现</p><p><br></p><p><br></p><p><strong>场景3:发起线下法人授权申请，页面如下</strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-23_14-55-13.png" data-image-src="/download/attachments/*********/image2025-7-23_14-55-13.png?version=1&amp;modificationDate=1753253713000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521862" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_14-55-13.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p>现状：发起线下授权申请后，身份证正反面会异步添加完水印才会保存到数据库。这时候查法人授权详情接口查不到下载地址。</p><p>改动：</p><ol><li>这里需要前端重新调用查详情接口。</li><li>预览功能参照场景2</li></ol><p><br></p><p><br></p><p><br></p><p><br></p><p><strong>场景4：线下法人授权书模版内容优化</strong></p><p>修改配置项</p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 433px; z-index: 3; width: 520px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="配置项key: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">配置项key</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="value: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">value</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="配置项key: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">配置项key</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="value: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">value</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd"><pre class="modal-body no-radius ng-binding">legal.auth.offline.html.filekey</pre></td><td class="confluenceTd"><br></td><td class="confluenceTd">线下法人授权书填充模版</td></tr></tbody></table></div><p><br></p><p><br></p><p><strong>影响接口：</strong></p><p><strong>1.&nbsp;查询法人授权详情(现有接口)</strong></p><p>webserver接口：<span style="color: rgb(31,31,31);">/webserver/v1/saas-common/organizations/seals/legal-auth-detail</span></p><p>footstone-seal facade接口：<span style="color: rgb(31,31,31);">RuleGrantFacade#queryLegalAuthInfo</span></p><p>请求参数：</p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 433px; z-index: 3; width: 380px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="方式: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">方式</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="必填: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">必填</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="方式: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">方式</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="必填: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">必填</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">orgId</td><td class="confluenceTd">query</td><td class="confluenceTd">String</td><td class="confluenceTd">是</td><td colspan="1" class="confluenceTd">企业oid</td></tr><tr role="row"><td class="confluenceTd">sealOwnerOid</td><td class="confluenceTd">query</td><td class="confluenceTd">String</td><td class="confluenceTd">是</td><td colspan="1" class="confluenceTd">印章主体oid</td></tr></tbody></table></div><p>返回参数：</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 39.3985%;" resolved=""><colgroup><col style="width: 11.662%;"><col style="width: 12.618%;"><col style="width: 11.3513%;"><col style="width: 31.359%;"><col style="width: 33.0082%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh"><p><span style="color: rgb(128,0,0);">参数</span></p></th><th class="confluenceTh"><p><span style="color: rgb(0,0,0);">类型</span></p></th><th colspan="1" class="confluenceTh">说明</th><th colspan="1" class="confluenceTh">改动范围</th></tr><tr><td colspan="2" class="confluenceTd"><p><span style="color: rgb(128,0,0);">authSignDoc</span></p></td><td colspan="1" class="confluenceTd">Object</td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">待授权模板文档</span></pre></td><td rowspan="3" class="confluenceTd">场景1</td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">filekey</span></p></li></ul></td><td colspan="1" class="confluenceTd">String</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">url</span></p></li></ul></td><td colspan="1" class="confluenceTd">String</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><p><span style="color: rgb(128,0,0);">authFlowInfo</span></p></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">Object</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">线上授权流程信息</span></pre></td><td rowspan="5" class="confluenceTd">场景2</td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">flowId</span></p></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">授权签署流程</span><span style="color: rgb(106,171,115);">id</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">docFilekey</span></p></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">授权签署文档</span><span style="color: rgb(106,171,115);">filekey</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">docUrl</span></p></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">授权签署文档下载地址</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">reason</span></p></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">授权失败原因</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><p><span style="color: rgb(128,0,0);">authFileInfo</span></p></td><td class="confluenceTd"><p><span style="color: rgb(0,0,0);">Object</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">线下授权信息</span></pre></td><td rowspan="11" class="confluenceTd"><span>场景3</span></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">authDoc</span></p></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">Object</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">授权文档</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li style="list-style-type: none;background-image: none;"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">fileKey</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li style="list-style-type: none;background-image: none;"><ul style="list-style-type: square;"><li><p><span style="color: rgb(128,0,0);">url</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><span style="color: rgb(128,0,0);">authIDFront</span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">Object</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">身份证正面照</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul><li style="list-style-type: none;background-image: none;"><ul><li><p><span style="color: rgb(128,0,0);">fileKey</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul><li style="list-style-type: none;background-image: none;"><ul><li><p><span style="color: rgb(128,0,0);">url</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><span style="color: rgb(128,0,0);">authIDReverse</span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">Object</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">身份证反面照</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><ul><li style="list-style-type: none;background-image: none;"><ul><li><p><span style="color: rgb(128,0,0);">fileKey</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul><li style="list-style-type: none;background-image: none;"><ul><li><p><span style="color: rgb(128,0,0);">url</span></p></li></ul></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><ul style="list-style-type: square;"><li><span style="color: rgb(128,0,0);">reason</span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">String</span></p></td><td colspan="1" class="confluenceTd"><pre><span style="color: rgb(106,171,115);">审核原因</span></pre></td></tr><tr><td colspan="2" class="confluenceTd"><p><span style="color: rgb(128,0,0);">...</span></p></td><td class="confluenceTd"><p><span style="color: rgb(128,0,0);">...</span></p></td><td colspan="1" class="confluenceTd">...</td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><p><br></p><p><br></p><h2 id="id-********签署迭代-3.3【V3api】合同文件阅读时长控制（云开）（延至下期）">3.3&nbsp;&nbsp;【V3api】合同文件阅读时长控制 （云开）（延至下期）</h2><p>背景：<span style="color: rgb(51,51,51);">目前签署阅读倒计时是针对签署方维度，不针对签署文件，比如一个签署方存在多份待签署文件，但是只需要总共倒计时10秒就可以，客户反馈其他电子签名厂商是支持针对每份文件单独设置倒计时时间的。</span></p><p><br></p><p>涉及接口：</p><p>接口名称：文件发起签署</p><p>接口路径：/v3/sign-flow/create-by-file</p><p>接口类型：开放接口</p><p>请求方式：POST</p><p>请求参数：</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 81.6327%;" resolved=""><colgroup><col style="width: 2.86976%;"><col style="width: 3.53201%;"><col style="width: 3.60559%;"><col style="width: 3.67918%;"><col style="width: 3.67918%;"><col style="width: 3.67918%;"><col style="width: 7.50552%;"><col style="width: 4.78293%;"><col style="width: 48.5651%;"><col style="width: 18.1015%;"></colgroup><tbody><tr><th colspan="6" class="confluenceTh">参数名</th><th class="confluenceTh">参数类型</th><th class="confluenceTh">必填</th><th class="confluenceTh">参数描述</th><th colspan="1" class="confluenceTh">备注</th></tr><tr><td colspan="6" class="confluenceTd"><pre><span style="color: rgb(135,16,148);">signers<br></span></pre></td><td class="confluenceTd">array</td><td class="confluenceTd">是</td><td class="confluenceTd"><pre>签署人列表</pre></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd"><br></td><td colspan="5" class="confluenceTd">signConfig</td><td colspan="1" class="confluenceTd">object</td><td colspan="1" class="confluenceTd">是</td><td colspan="1" class="confluenceTd">签署人维度签署配置</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="4" class="confluenceTd"><pre><span style="color: rgb(255,0,0);">fileForcedReadingTime</span></pre></td><td class="confluenceTd"><span style="color: rgb(255,0,0);">String</span></td><td class="confluenceTd"><span style="color: rgb(255,0,0);">否</span></td><td class="confluenceTd"><pre><span style="color: rgb(255,0,0);">设置页面强制阅读倒计时时间，默认值为 0（单位：秒，最大值999）<br></span></pre></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(255,0,0);">fileForcedReadingTime和forcedReadingTime不可同时入参</span></p></td></tr></tbody></table></div><p>数据存储：</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; flow_config_info 表新增签署人维度配置项，新增configKey</p><p><br></p><h2 id="id-********签署迭代-3.4【签署】v3api支持阅读到底二次确认（云开）">3.4&nbsp;【签署】v3api支持阅读到底二次确认 （云开）</h2><p>背景:<span style="color: rgb(51,51,51);">金融客户对强制阅读到底二次确认有诉求，并且希望文案内容可以自定义</span></p><p>现状: 贝壳有类似需求需求，会在获取链接时传入&nbsp;<span style="color: rgb(135,16,148);">forceReadConfirm （boolean）<span style="color: rgb(0,51,102);">参数，值为true 前端展示默认文案弹窗</span></span></p><p><br></p><p><span style="color: rgb(51,51,51);">新增appid配置：强制阅读到底二次确认文案，forcedReadTwiceConfirm 文本，默认为空</span></p><p>配置中心&nbsp; &nbsp;required.app.config&nbsp; &nbsp;新增配置项&nbsp;&nbsp;<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm</span></p><p>** 前端接口 **</p><p><span style="color: rgb(31,31,31);">/openwebserver/v1/openplatform/getAppConfigByAppId&nbsp; &nbsp;&nbsp;</span></p><p>传入配置项&nbsp;&nbsp;<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm&nbsp;&nbsp;</span></p><p><br></p><p>** 前端展示逻辑 **&nbsp;</p><p>如果Context 中&nbsp; &nbsp;<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm 为true ，优先展示 已有默认文案</span></p><p><span style="color: rgb(51,51,51);">如果Context&nbsp;&nbsp;中&nbsp; &nbsp;<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm 为false，且&nbsp;<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm 内容不为空，展示<span style="color: rgb(51,51,51);">forcedReadTwiceConfirm文案</span></span></span></span></p><p><span style="color: rgb(51,51,51);">如果Context&nbsp;&nbsp;中&nbsp; &nbsp;</span><span style="color: rgb(51,51,51);">forcedReadTwiceConfirm 为false，且<span style="color: rgb(51,51,51);">且&nbsp;</span><span style="color: rgb(51,51,51);">forcedReadTwiceConfirm 内容为空，不弹窗</span></span></p><p><br></p><h2 id="id-********签署迭代-3.5【SaaS发起】自定义签署声明模板（云开）（延至下期）">3.5&nbsp;【SaaS发起】自定义签署声明模板&nbsp; （云开）&nbsp;（延至下期）</h2><p><br></p><h2 id="id-********签署迭代-3.6【V3api】个人身份信息不一致报错优化（云开）">3.6&nbsp;【V3api】个人身份信息不一致报错优化 （云开）</h2><p>背景：</p><p>V3发起签署，指定【签署人手机号】+【签署人信息（姓名、手机号）】，会校验【手机号对应用户信息】与【发起时指定签署人信息】是否一致；</p><p>如果姓名、证件号都不一致，目前只会返回姓名不一致，客户希望一次返回全部错误信息。</p><p>改定影响范围：</p><p>RpcV3、ApiV3</p><p>改动代码：</p><p>com.timevale.footstone.core.service.flow.create.RpcV3CreateFlowHandler#checkSignerInfo(com.timevale.footstone.rpc.model.createflow.bean.SignerInfoBean, java.lang.String)</p><p>com.timevale.footstone.core.service.flow.support.ApiV3FlowCreateSupport#checkAssignedSignerInfo</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="900" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-25_10-8-15.png" data-image-src="/download/attachments/*********/image2025-7-25_10-8-15.png?version=1&amp;modificationDate=1753409296000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523458" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_10-8-15.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p>修改failReason，姓名、证件号信息都不正确同时返回</p><p>目前错误提示语</p><p><span style="color: rgb(4,81,165);">该签署人账号：139****1991对应用户已存在个人信息，传入的指定签署人信息与已有个人信息不一致，请先确认信息是否准确：姓名****</span></p><p>修改后错误提示语</p><p><span style="color: rgb(4,81,165);">该签署人账号：139****1991对应用户已存在个人信息，传入的指定签署人信息与已有个人信息不一致，请先确认信息是否准确：姓名****、证件号**********</span></p><p><br></p><h2 id="id-********签署迭代-3.7【存出证】出证模板优化（洛安）">3.7&nbsp;&nbsp;【存出证】出证模板优化（洛安）</h2><p>需求背景：<a href="https://forward.esign.cn/productManagement/edit?id=12768&amp;type=check" class="external-link" rel="nofollow">https://forward.esign.cn/productManagement/edit?id=12768&amp;type=check</a></p><p>方案：区分出用户第一次签署，还是第二次签署，不同的签署认证信息展示的文案不同</p><p>改动点：展示文案时判断，如果是授权签署展示 授权信息&nbsp; &nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;如果是第一次签署，展示<span style="color: rgb(51,51,51);">实名认证信息</span></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;如果是第二次签署，展示<span style="color: rgb(51,51,51);">实名及意愿认证信息</span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="400" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-24_19-11-22.png" data-image-src="/download/attachments/*********/image2025-7-24_19-11-22.png?version=1&amp;modificationDate=1753355483000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523277" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_19-11-22.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><br></p><p>影响范围：sdk3.0出证</p><h2 id="id-********签署迭代-3.8【存出证】出证埋点增加客户端属性（洛安）">3.8&nbsp;【存出证】出证埋点增加客户端属性（洛安）</h2><p>需求背景：<a href="https://forward.esign.cn/productManagement/edit?id=12766&amp;type=check" class="external-link" rel="nofollow">https://forward.esign.cn/productManagement/edit?id=12766&amp;type=check</a></p><p>方案：1.前端在申请出证的时候，在请求头上加&nbsp;Client-Type</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2.客户通过api调用出证，客户端属性为开放服务&nbsp;OPEN_SERVICE</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3.保存埋点时，在请求头上去取客户端属性</p><p><br></p><p>改动点：在埋点时保存客户端属性</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="400" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-24_14-24-10.png" data-image-src="/download/attachments/*********/image2025-7-24_14-24-10.png?version=1&amp;modificationDate=1753338250000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221522824" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_14-24-10.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><br></p><p>影响范围：</p><p>v1/flash/applyIssue</p><p>block-chain-report/apply</p><p>v3/evidence-report/apply</p><p><br></p><h2 id="id-********签署迭代-3.10【签署】非标api支持法人授权静默签（洛安）">3.10&nbsp; 【签署】非标api支持法人授权静默签 （洛安）</h2><p>背景：<a href="https://forward.esign.cn/productManagement/edit?id=12773&amp;type=check" class="external-link" rel="nofollow">https://forward.esign.cn/productManagement/edit?id=12773&amp;type=check</a></p><p>方案：复用非标api企业线下授权那套，支持法人个人授权，生成的授权记录为个人授权记录</p><p>时序图：</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="1200" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/【签署】非标api支持法人授权静默签.png" data-image-src="/download/attachments/*********/%E3%80%90%E7%AD%BE%E7%BD%B2%E3%80%91%E9%9D%9E%E6%A0%87api%E6%94%AF%E6%8C%81%E6%B3%95%E4%BA%BA%E6%8E%88%E6%9D%83%E9%9D%99%E9%BB%98%E7%AD%BE.png?version=2&amp;modificationDate=1753412546000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523247" data-linked-resource-version="2" data-linked-resource-type="attachment" data-linked-resource-default-alias="【签署】非标api支持法人授权静默签.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p>涉及到的接口：</p><p><span style="color: rgb(0,51,102);">接口URL：/v1/signAuthApi/offline</span></p><p><span style="color: rgb(0,51,102);">请求方式：post</span></p><p><span style="color: rgb(0,51,102);">请求参数：</span></p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 66.5195%;" resolved=""><colgroup><col style="width: 5.40665%;"><col style="width: 17.8355%;"><col style="width: 10.53%;"><col style="width: 6.92468%;"><col style="width: 11.3839%;"><col style="width: 42.7867%;"><col style="width: 5.12861%;"></colgroup><tbody><tr><td colspan="2" class="confluenceTd"><p class="ne-p"><span style="color: rgb(0,51,102);">参数名称</span></p></td><td class="confluenceTd"><p class="ne-p" style="text-align: center;"><span style="color: rgb(0,51,102);">类型</span></p></td><td class="confluenceTd"><p class="ne-p" style="text-align: center;"><span style="color: rgb(0,51,102);">必选</span></p></td><td class="confluenceTd"><p class="ne-p" style="text-align: center;"><span style="color: rgb(0,51,102);">参数类型</span></p></td><td class="confluenceTd"><p class="ne-p"><span style="color: rgb(0,51,102);">参数说明</span></p></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">备注</span></td></tr><tr><td colspan="2" class="confluenceTd"><p><span style="color: rgb(255,0,0);">authType<br></span></p></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">否</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">body</span></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(255,0,0);">ORG-企业</span></p><p><span style="color: rgb(255,0,0);">LEGALREP-法定代表人</span></p><p><span style="color: rgb(255,0,0);">默认为ORG</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(0,51,102);">accountId</span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string</span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">是</span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">body</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">机构账号id</span></td><td colspan="1" class="confluenceTd"><p><br></p></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(255,0,0);">legalRepAccountId</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">否</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(255,0,0);">body</span></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(255,0,0);">法人个人账号id</span></p><p><span style="color: rgb(255,0,0);">authType-</span><span style="color: rgb(255,0,0);">LEGALREP-法定代表人时必传</span></p><p><span style="color: rgb(255,0,0);">传入后校验企业四要素，不通过则报错</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(0,51,102);">transactorAccountId</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">是</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">body</span></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,51,102);">经办人账号id</span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(0,51,102);">realnameFlowId</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">是</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">body</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">经办人实名流程id（30分钟有效）</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">validDate<br></span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">string</span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">是</span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">body</span></p></td><td class="confluenceTd"><p class="ne-p">授权有效期截止时间 不能为空 格式为yyyyMMdd 列如 "20260101"</p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">sealScope<br></span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">string<br></span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">否<br></span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">body</span></p></td><td class="confluenceTd"><p>自定义授权印章范围，不传则默认为全部</p><p>仅做为授权书内容的展示，不做接口校验。例如：合同专用章、人事专用章等</p><p class="ne-p"><span style="color: rgb(255,0,0);">authType-LEGALREP-法定代表人时，固定为<strong>法定代表人章</strong></span></p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(0,51,102);">fileType<br></span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string<br></span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">否</span></td><td style="text-align: center;" colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">body</span></td><td colspan="1" class="confluenceTd"><p>自定义签署文件类型，不传则默认为全部</p><p>仅做为授权书内容的展示，不做接口校验。例如：借贷协议、运输协议、物流条款等</p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><p class="ne-p"><span style="color: rgb(0,51,102);">notifyUrl</span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">string<br></span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">否</span></p></td><td style="text-align: center;" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">body</span></p></td><td class="confluenceTd"><p class="ne-p">回调通知地址，点击了解 回调通知服务</p><p class="ne-p">签署完成结果通知平台，签署完成或者拒签触发回调</p></td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><p><span style="color: rgb(0,51,102);">响应参数：</span></p><div class="table-wrap"><table class="confluenceTable" resolved=""><colgroup><col><col><col><col></colgroup><tbody><tr><td colspan="1" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">参数名称</span></p></td><td colspan="1" class="confluenceTd"><p class="ne-p" style="text-align: center;"><span class="ne-text" style="color: rgb(0,51,102);">类型</span></p></td><td colspan="1" class="confluenceTd"><p class="ne-p" style="text-align: center;"><span class="ne-text" style="color: rgb(0,51,102);">必选</span></p></td><td colspan="1" class="confluenceTd"><p class="ne-p"><span class="ne-text" style="color: rgb(0,51,102);">参数说明</span></p></td></tr><tr><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">authId<br></span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">否</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">授权流程id</span></td></tr><tr><td colspan="1" class="confluenceTd"><p class="ne-p"><span class="ne-text">fileUrl</span></p></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">string</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,51,102);">否</span></td><td colspan="1" class="confluenceTd"><p class="ne-p"><span class="ne-text">授权书原文件下载地址（有效期为60分钟，请在60分钟内尽快完成下载。如果链接超时未获取到文件，需要重新发起新的授权流程获取新的下载地址）</span></p></td></tr></tbody></table></div><h2 id="id-********签署迭代-3.11【V3api】批量签接口支持可不传入姓名（洛安）">3.11 【V3api】批量签接口支持可不传入姓名（洛安）</h2><p>背景：<a href="https://forward.esign.cn/productManagement/edit?id=12775&amp;type=check" class="external-link" rel="nofollow">https://forward.esign.cn/productManagement/edit?id=12775&amp;type=check</a></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 目前获取批量签链接时，如果存在签署流程发起时，签署人未实名且未指定签署人姓名，无法获取批量签链接</p><p>方案：在获取批量签链接时，对发起时签署人未实名未指定姓名的校验放开，允许存在流程未指定签署人姓名</p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="1200" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-28_20-31-46.png" data-image-src="/download/attachments/*********/image2025-7-28_20-31-46.png?version=1&amp;modificationDate=1753705906000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525596" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_20-31-46.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></strong></p><p>流程图：</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="1200" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/image2025-7-28_20-58-12.png" data-image-src="/download/attachments/*********/image2025-7-28_20-58-12.png?version=1&amp;modificationDate=1753707493000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525603" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_20-58-12.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p>时序图：</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="1200" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/批量签支持个人未实名且不指定姓名.png" data-image-src="/download/attachments/*********/%E6%89%B9%E9%87%8F%E7%AD%BE%E6%94%AF%E6%8C%81%E4%B8%AA%E4%BA%BA%E6%9C%AA%E5%AE%9E%E5%90%8D%E4%B8%94%E4%B8%8D%E6%8C%87%E5%AE%9A%E5%A7%93%E5%90%8D.png?version=1&amp;modificationDate=1753705884000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525595" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="批量签支持个人未实名且不指定姓名.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="43"></span></p><p><br></p><p><br></p><p>影响范围：</p><p><span style="color: rgb(0,0,0);"><strong><span>接口：{flowId}/organ/auth/{orgId}/identifyUrl</span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong><span>请求方式：post</span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong><span>入参：</span></strong></span></p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否必填: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">是否必填</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否必填: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">是否必填</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">batchSerialId</td><td class="confluenceTd">String</td><td class="confluenceTd">否</td><td class="confluenceTd">批量签id,用户获取签署操作人信息</td></tr></tbody></table></div><p><span style="color: rgb(0,0,0);"><strong><span>出参：</span></strong></span></p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">realNameFlowId</td><td class="confluenceTd"><pre>String</pre></td><td class="confluenceTd">实名流程Id</td></tr><tr role="row"><td colspan="1" class="confluenceTd">url</td><td colspan="1" class="confluenceTd"><pre>String</pre></td><td colspan="1" class="confluenceTd">实名或者意愿的长链地址</td></tr><tr role="row"><td colspan="1" class="confluenceTd">shortLink</td><td colspan="1" class="confluenceTd"><pre>String</pre></td><td colspan="1" class="confluenceTd">实名或者意愿的短链地址</td></tr></tbody></table></div><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong>接口：{flowId}/individual/realNameWill/{accountId}/identifyUrl</strong></span></p><p><span style="color: rgb(0,0,0);"><strong>请求方式：post</strong></span></p><p><span style="color: rgb(0,0,0);"><strong>入参：</strong></span></p><p><br></p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否必填: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">是否必填</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">参数</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否必填: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">是否必填</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">描述</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">batchSerialId</td><td class="confluenceTd">String</td><td class="confluenceTd">否</td><td class="confluenceTd">批量签id,用户获取签署操作人信息</td></tr></tbody></table></div><p><span style="color: rgb(0,0,0);"><strong>出参：</strong></span></p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>参数</span></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>类型</span></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>描述</span></div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="参数: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>参数</span></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>类型</span></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span>描述</span></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">type</td><td class="confluenceTd">String</td><td class="confluenceTd">认证URL类型，0:实名，1：意愿</td></tr><tr role="row"><td class="confluenceTd">bizId</td><td class="confluenceTd"><pre>String</pre></td><td class="confluenceTd">业务方id</td></tr><tr role="row"><td class="confluenceTd">realNameFlowId</td><td class="confluenceTd"><pre>String</pre></td><td class="confluenceTd">实名流程Id</td></tr><tr role="row"><td colspan="1" class="confluenceTd">url</td><td colspan="1" class="confluenceTd"><pre>String</pre></td><td colspan="1" class="confluenceTd">实名或者意愿的长链地址</td></tr><tr role="row"><td colspan="1" class="confluenceTd">shortUrl</td><td colspan="1" class="confluenceTd"><pre>String</pre></td><td colspan="1" class="confluenceTd">实名或者意愿的短链地址</td></tr></tbody></table></div><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><p><span style="color: rgb(0,0,0);"><strong><span><br></span></strong></span></p><p><br></p>

                
        
    
        </div>

        <!--
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:dc="http://purl.org/dc/elements/1.1/"
         xmlns:trackback="http://madskills.com/public/xml/rss/module/trackback/">
         <rdf:Description
    rdf:about="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:identifier="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:title="********签署迭代"
    trackback:ping="http://wiki.timevale.cn:8081/rpc/trackback/*********"/>
</rdf:RDF>
-->

                        
    



<div id="likes-and-labels-container"><div id="likes-section" class="no-print"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" class="like-button"><span class="aui-icon aui-icon-small aui-iconfont-like"></span><span class="like-button-text">赞</span></a><span class="like-summary like-summary-margin-left">成为第一个赞同者</span></div><div id="labels-section" class="pageSection group">
    <div class="labels-section-content content-column" entityid="*********" entitytype="page">
	<div class="labels-content">
		
    <ul class="label-list label-list-right  has-pen">
            <li class="no-labels-message">
            无标签
        </li>
                <li class="labels-edit-container">
            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="show-labels-editor" title="编辑标签 (l)">
                <span class="aui-icon aui-icon-small aui-iconfont-devtools-tag-small">编辑标签</span>
            </a>
        </li>
        </ul>

    </div>
</div>
</div></div>
        
		
            




            
        








                        
    
<div id="comments-section" class="pageSection group">
        
    


    <div class="bottom-comment-panels comment-panels">
                
                    
    


        
        
    
    <div class="quick-comment-container comment add"><p class="comment-user-logo"><a class="userLogoLink userlink-2" data-username="taolang" href="http://wiki.timevale.cn:8081/display/~taolang" title="" data-user-hover-bound="true"><img class="userLogo logo" src="./********签署迭代 - 2.技术中心 - 天谷百科_files/user-avatar" alt="用户图标: taolang" title=""></a></p><div class="quick-comment-body"><div class="quick-comment-loading-container" style="display:none;"></div><div id="editor-messages"></div><div id="all-messages"></div><form style="display:block;" class="quick-comment-form aui" method="post" name="inlinecommentform" action="http://wiki.timevale.cn:8081/pages/doaddcomment.action?pageId=*********"><div title="添加评论 (m)" class="quick-comment-prompt"><span>编写评论...</span></div></form></div></div>

            </div>

            <div id="comments-actions" class="aui-toolbar noprint" style="display: none;">
            <p class="toolbar-group">
                <span class="toolbar-item"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;showComments=true&amp;showCommentArea=true#addcomment" id="add-comment-rte" accesskey="m" class="toolbar-trigger">添加评论</a></span>
            </p>
        </div>
    </div>
        


                
    
                <div id="watermark" style="pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background: url(&quot;data:image/png;base64,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&quot;) right top;">
     <canvas width="280" height="280" style="display: none;"></canvas></div>

        <script>
   function addWaterMarker(str){
	  var can = document.createElement('canvas');
	  var body = document.getElementById('watermark');

	  body.appendChild(can);

	 can.width=280;
	 can.height=280;
	 can.style.display='none';
	 var cans = can.getContext('2d');
	 cans.rotate(-20*Math.PI/180);
	 cans.font = "16px Microsoft JhengHei"; 
	 cans.fillStyle = "rgba(17, 17, 17, 0.20)";
	 cans.textAlign = 'left'; 
	 cans.textBaseline = 'Middle';
	 cans.fillText(str, can.width/3-40, can.height/2,200);
	 cans.fillText("e签宝版权所有请勿外传", can.width/3-40, can.height/2+40,200);
	 body.style = "pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background:url("+can.toDataURL("image/png")+") right top;"
   
   }
   </script>
  
  　    <script>
            var myDate = new Date();
　　     addWaterMarker("桃浪"+"-"+myDate.toLocaleDateString())
　    </script>
  </div>

    

    




    
    

    
    
    


    
<div id="space-tools-web-items" class="hidden">
                <div data-label="概览" data-href="/spaces/viewspacesummary.action?key=PRODUCT">概览</div>
            <div data-label="内容工具" data-href="/pages/reorderpages.action?key=PRODUCT">内容工具</div>
    </div>
        



            </div><!-- \#main -->
            
    
    
        
            
            

<div id="footer" role="contentinfo" style="margin-left: 393px;">
    <section class="footer-body">

                                                    
        

        <ul id="poweredby">
            <li class="noprint">基于 <a href="http://www.atlassian.com/software/confluence" class="hover-footer-link" rel="nofollow">Atlassian Confluence</a> <span id="footer-build-information">6.13.4</span> 技术构建</li>
            <li class="print-only">由 Atlassian 合流6.13.4 打印</li>
            <li class="noprint"><a href="https://support.atlassian.com/help/confluence" class="hover-footer-link" rel="nofollow">报告缺陷</a></li>
            <li class="noprint"><a href="http://www.atlassian.com/about/connected.jsp?s_kwcid=Confluence-stayintouch" class="hover-footer-link" rel="nofollow">Atlassian 新闻</a></li>
        </ul>

        

        <div id="footer-logo"><a href="http://www.atlassian.com/" rel="nofollow">Atlassian</a></div>

                    
        
    </section>
</div>

    
</div>

</div><!-- \#full-height-container -->
</div><!-- \#page -->

    <span style="display:none;" id="confluence-server-performance">{"serverDuration": 496, "requestCorrelationId": "19202df670e00014"}</span>


<script type="text/javascript">
    AJS.BigPipe = AJS.BigPipe || {};
    AJS.BigPipe.metrics = AJS.BigPipe.metrics || {};
    AJS.BigPipe.metrics.pageEnd = typeof window.performance !== "undefined" && typeof window.performance.now === "function"
                                    ? Math.ceil(window.performance.now()) : 0;
    AJS.BigPipe.metrics.isBigPipeEnabled = 'false' === 'true';
</script>


    
<div id="editor-preload-container" style="display: none;">
 

<div class="hidden">
        


<content tag="breadcrumbs">
    
    
    <ol id="quickedit-breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/display/PRODUCT" target="_blank">2.技术中心</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT" target="_blank">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056" target="_blank">技术中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607659" target="_blank">业务域</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=147435016" target="_blank">公有云-电子签名</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607707" target="_blank">1.签署服务组</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28608453" target="_blank">项目&amp;迭代</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126322665" target="_blank">迭代详细设计</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210155734" target="_blank">2025年迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674785" target="_blank">25年Q3迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class="edited-page-title"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" target="_blank">********签署迭代</a></span>
                                                                    </li></ol>

</content>
</div>


        
    

                                                                                        

<script type="text/x-template" title="editor-css" id="editor-css-resources">
    <link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.0/_/download/batch/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-font/net.customware.confluence.plugin.vault:secenc-font.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-font" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-editor/net.customware.confluence.plugin.vault:secenc-editor.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-editor" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secure-macro-browser-override/net.customware.confluence.plugin.vault:secure-macro-browser-override.css" data-wrm-key="net.customware.confluence.plugin.vault:secure-macro-browser-override" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-typography/com.atlassian.auiplugin:aui-page-typography.css" data-wrm-key="com.atlassian.auiplugin:aui-page-typography" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-link/com.atlassian.auiplugin:aui-link.css" data-wrm-key="com.atlassian.auiplugin:aui-link" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-avatars/com.atlassian.auiplugin:aui-avatars.css" data-wrm-key="com.atlassian.auiplugin:aui-avatars" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/47a8861ddeabe407d6a865c6dde5d487-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-layout/com.atlassian.auiplugin:aui-page-layout.css" data-wrm-key="com.atlassian.auiplugin:aui-page-layout" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:editor-content-styles/com.atlassian.confluence.editor:editor-content-styles.css" data-wrm-key="com.atlassian.confluence.editor:editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-editor-content-styles/com.atlassian.confluence.editor:table-resizable-editor-content-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-styles/com.atlassian.confluence.editor:table-resizable-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-lozenge/com.atlassian.auiplugin:aui-lozenge.css" data-wrm-key="com.atlassian.auiplugin:aui-lozenge" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:view_content_status/com.atlassian.confluence.plugins.status-macro:view_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:view_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:editor_content_status/com.atlassian.confluence.plugins.status-macro:editor_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:editor_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/9daf7afbce8f12b08a5c226d5aed29c7-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.14/_/download/batch/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/1bf6e69c18341244d990250bf5aa3ce0-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.4.0/_/download/batch/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/a0e894d2f295b40fda5171460781b200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.0.3/_/download/batch/confluence.extra.attachments:attachments-css/confluence.extra.attachments:attachments-css.css" data-wrm-key="confluence.extra.attachments:attachments-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/14.2.1/_/download/batch/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:panel-styles/confluence.web.resources:panel-styles.css" data-wrm-key="confluence.web.resources:panel-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/17017df768625b4c50edd34ec564513c-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:content-styles/confluence.web.resources:content-styles.css" data-wrm-key="confluence.web.resources:content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css?conditionalComment=lte+IE+9" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="/s/5baeb58a5608e28c5f241eee74f064b2-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/4.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/5.1.4/_/download/batch/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources" data-wrm-batch-type="resource" media="all">

</script>













        

<div class="editor-container">

        
            

<div id="link-browser-tab-items" class="hidden">
                <div title="搜索" data-weight="10">search</div>
            <div title="最近浏览过" data-weight="20">recentlyviewed</div>
            <div title="文件" data-weight="30">attachments</div>
            <div title="Web链接" data-weight="40">weblink</div>
            <div title="高级" data-weight="50">advanced</div>
    </div>
            <div id="image-properties-tab-items" class="hidden">
                <div title="效果" data-weight="10">image-effects</div>
            <div title="标题" data-weight="20">image-attributes</div>
    </div>
            

 










<div id="toolbar">
    <div id="rte-toolbar" class="aui-toolbar aui-toolbar2">

        <div class="aui-toolbar2-primary toolbar-primary">
            <ul class="aui-buttons rte-toolbar-group-formatting">
                            <li id="format-dropdown" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="format-dropdown-display" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-control-id="formatselect">
                            <span class="dropdown-text">正文</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <ul id="format-dropdown-display-menu" class="aui-dropdown hidden">
                            <li class="dropdown-item format-p" data-format="p" data-tooltip="正文 (Ctrl+0)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">正文</a>
</li>
                                <li class="dropdown-item format-h1" data-format="h1" data-tooltip="标题 1 (Ctrl+1)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 1</a>
</li>
                                <li class="dropdown-item format-h2" data-format="h2" data-tooltip="标题 2 (Ctrl+2)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 2</a>
</li>
                                <li class="dropdown-item format-h3" data-format="h3" data-tooltip="标题 3 (Ctrl+3)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 3</a>
</li>
                                <li class="dropdown-item format-h4" data-format="h4" data-tooltip="标题 4 (Ctrl+4)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 4</a>
</li>
                                <li class="dropdown-item format-h5" data-format="h5" data-tooltip="标题 5 (Ctrl+5)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 5</a>
</li>
                                <li class="dropdown-item format-h6" data-format="h6" data-tooltip="标题 6 (Ctrl+6)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 6</a>
</li>
                                <li class="dropdown-item format-pre" data-format="pre" data-tooltip="预格式化 (Ctrl+7)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">预格式化</a>
</li>
                                <li class="dropdown-item format-blockquote" data-format="blockquote" data-tooltip="引用 (Ctrl+8)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">引用</a>
</li>
                        </ul>
                    </div>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-style">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bold" data-tooltip="粗体 (Ctrl+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bold">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-bold ">粗体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-italic" data-tooltip="斜体 (Ctrl+I)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="italic">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-italic ">斜体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-underline" data-tooltip="下划线 (Ctrl+U)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="underline">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-underline ">下划线</span>
    </a>
</li>
                            <li id="color-picker-control" class="toolbar-item toolbar-splitbutton">
                    <a class="toolbar-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color" data-color="003366" data-tooltip="颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-editor-color ">颜色选取器</span><span class="selected-color"></span></a><div class="aui-dd-parent"><a class="toolbar-trigger aui-dd-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color-selector" data-control-id="colorSelector" data-tooltip="更多颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-dropdown ">更多颜色</span></a><div class="color-picker-container"><div class="color-picker aui-dropdown hidden"><ul><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黑色" data-tooltip="黑色" style="background-color: #000000" data-color="000000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橙黄色" data-tooltip="深橙黄色" style="background-color: #993300" data-color="993300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橄榄绿色" data-tooltip="深橄榄绿色" style="background-color: #333300" data-color="333300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深绿色" data-tooltip="深绿色" style="background-color: #003300" data-color="003300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="藏青色" data-tooltip="藏青色" style="background-color: #003366" data-color="003366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海蓝色" data-tooltip="海蓝色" style="background-color: #000080" data-color="000080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="靛蓝色" data-tooltip="靛蓝色" style="background-color: #333399" data-color="333399">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深灰色" data-tooltip="深灰色" style="background-color: #333333" data-color="333333">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="褐红色" data-tooltip="褐红色" style="background-color: #800000" data-color="800000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橙色" data-tooltip="橙色" style="background-color: #FF6600" data-color="FF6600">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橄榄绿色" data-tooltip="橄榄绿色" style="background-color: #808000" data-color="808000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿色" data-tooltip="绿色" style="background-color: #008000" data-color="008000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝绿色" data-tooltip="蓝绿色" style="background-color: #008080" data-color="008080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝色" data-tooltip="蓝色" style="background-color: #0000FF" data-color="0000FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰蓝色" data-tooltip="灰蓝色" style="background-color: #666699" data-color="666699">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰色" data-tooltip="灰色" style="background-color: #7A869A" data-color="7A869A">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红色" data-tooltip="红色" style="background-color: #FF0000" data-color="FF0000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="琥珀色" data-tooltip="琥珀色" style="background-color: #FF9900" data-color="FF9900">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄绿色" data-tooltip="黄绿色" style="background-color: #99CC00" data-color="99CC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海绿色" data-tooltip="海绿色" style="background-color: #339966" data-color="339966">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="青绿色" data-tooltip="青绿色" style="background-color: #33CCCC" data-color="33CCCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="宝蓝色" data-tooltip="宝蓝色" style="background-color: #3366FF" data-color="3366FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="紫色" data-tooltip="紫色" style="background-color: #800080" data-color="800080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="中灰色" data-tooltip="中灰色" style="background-color: #A5ADBA" data-color="A5ADBA">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="洋红色" data-tooltip="洋红色" style="background-color: #FF00FF" data-color="FF00FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="金色" data-tooltip="金色" style="background-color: #FFCC00" data-color="FFCC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄色" data-tooltip="黄色" style="background-color: #FFFF00" data-color="FFFF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿黄色" data-tooltip="绿黄色" style="background-color: #00FF00" data-color="00FF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="湖绿色" data-tooltip="湖绿色" style="background-color: #00FFFF" data-color="00FFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="天蓝色" data-tooltip="天蓝色" style="background-color: #00CCFF" data-color="00CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红紫色" data-tooltip="红紫色" style="background-color: #993366" data-color="993366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅灰色" data-tooltip="浅灰色" style="background-color: #C1C7D0" data-color="C1C7D0">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="粉色" data-tooltip="粉色" style="background-color: #FF99CC" data-color="FF99CC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="桃红色" data-tooltip="桃红色" style="background-color: #FFCC99" data-color="FFCC99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅黄色" data-tooltip="浅黄色" style="background-color: #FFFF99" data-color="FFFF99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅绿色" data-tooltip="浅绿色" style="background-color: #CCFFCC" data-color="CCFFCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅蓝绿色" data-tooltip="浅蓝绿色" style="background-color: #CCFFFF" data-color="CCFFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅天蓝色" data-tooltip="浅天蓝色" style="background-color: #99CCFF" data-color="99CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绛紫色" data-tooltip="绛紫色" style="background-color: #CC99FF" data-color="CC99FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="白色" data-tooltip="白色" style="background-color: #FFFFFF" data-color="FFFFFF">&nbsp;</a></li></ul></div></div></div>
                </li>
                <li id="more-menu" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="rte-button-more" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-tooltip="更多">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-styles ">格式</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <div id="rte-button-more-menu" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <ul>
                                                        <li class="dropdown-item more-menu-trigger" data-control-id="strikethrough" data-tooltip="删除线 (Ctrl+Shift+S)">
    <a id="rte-strikethrough" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
删除线
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sub" data-tooltip="">
    <a id="rte-sub" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
下标
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sup" data-tooltip="">
    <a id="rte-sup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
上标
    </a>
</li>
                                                    <li class="dropdown-item more-menu-trigger" data-control-id="monospace" data-tooltip="用等宽字体格式化文本">
    <a id="rte-monospace" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
等宽
    </a>
</li>

                                                                                                                </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul>
                                    <li class="dropdown-item more-menu-trigger no-icon" data-format="removeformat" data-tooltip="当前选中文本清除格式">
<a id="rte-removeformat" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    清除格式
</a>
</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-lists">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bullist" data-tooltip="无序列表 (Ctrl+Shift+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bullist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-bullet ">无序列表</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-numlist" data-tooltip="有序列表 (Ctrl+Shift+N)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="numlist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-number ">有序列表</span>
    </a>
</li>
            </ul>
                            <ul class="aui-buttons rte-toolbar-group-task-lists">
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-tasklist" data-tooltip="任务列表 (tinymce.confluence.layout.three_col=三栏)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="tasklist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-task ">任务列表</span>
    </a>
</li>
                </ul>
            
            <ul class="aui-buttons rte-toolbar-group-indentation">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-outdent" data-tooltip="减小缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="outdent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-left ">减小缩进</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-indent" data-tooltip="增大缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="indent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-right ">增大缩进</span>
    </a>
</li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-justification">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyleft" data-tooltip="左对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyleft">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-left ">左对齐</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifycenter" data-tooltip="居中">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifycenter">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-center ">居中</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyright" data-tooltip="右对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyright">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-right ">右对齐</span>
    </a>
</li>
            </ul>

                            <ul class="aui-buttons hidden" id="page-layout-2-group">
                    <li id="page-layout-2" class="toolbar-item" data-tooltip="页面布局">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="aui-button aui-button-subtle toolbar-trigger" id="rte-button-pagelayout-2">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-layout ">页面布局</span>
                        </a>
                    </li>
                </ul>
            

            <ul class="aui-buttons rte-toolbar-group-files hidden"></ul>

            <ul class="aui-buttons rte-toolbar-group-link no-separator">
                <li class="toolbar-item" data-tooltip="插入链接 (Ctrl+K)">
                    <a id="rte-button-link" class="toolbar-trigger aui-button aui-button-subtle" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="linkbrowserButton">
                                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
                        <span class="trigger-text">链接</span>
                    </a>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-table no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-table-dropdown">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert-table" data-tooltip="插入表格">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-table "></span>
                            <span class="dropdown-text">表格</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="table-picker-container" class="hidden aui-box-shadow">
                            <div class="table-picker-box" data-tooltip="按住 SHIFT键，创建无表头表格 。">
                                <div class="table-picker-background">
                                    <div class="picker picker-cell"></div>
                                    <div class="picker picker-heading heading"></div>
                                    <div class="picker picker-selected-cell"></div>
                                    <div class="picker picker-selected-heading heading"></div>
                                </div>
                                <p class="desc"></p>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                                    
            
            <ul class="aui-buttons rte-toolbar-group-insert no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-menu">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert" data-tooltip="插入更多内容">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-add "></span>
                            <span class="dropdown-text">插入</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="insert-menu-options" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <span class="assistive">插入内容</span>
                                <ul id="content-insert-list">
                                    
        
                <li class="dropdown-item content-image" data-command="mceConfimage" data-tooltip="插入文件和图片 (Ctrl+M)">
<a id="rte-insert-image" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-image "></span>
 文件和图片
</a>
</li>
                                            
        
                <li class="dropdown-item content-link" data-control-id="linkbrowserButton" data-tooltip="插入链接 (Ctrl+K)">
<a id="rte-insert-link" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
 链接
</a>
</li>
                                            
        
                <li class="dropdown-item content-wikimarkup" data-command="InsertWikiMarkup" data-tooltip="插入Wiki标记 (Ctrl+Shift+D)">
<a id="rte-insert-wikimarkup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-code "></span>
 Wiki标记
</a>
</li>
                                            
    
                <li class="dropdown-item content-hr" data-command="InsertHorizontalRule" data-tooltip="插入水平线(----)">
<a id="rte-insert-hr" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-horizontal-rule "></span>
 水平线
</a>
</li>
                                                                                
        
                <li class="dropdown-item content-tasklist" data-command="InsertInlineTaskListNoToggle" data-tooltip="插入任务列表 (tinymce.propertypanel.images.link.remove.tooltip=删除图片链接)">
<a id="rte-insert-tasklist" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-task "></span>
 任务列表
</a>
</li>
                                                                            
        
                <li class="dropdown-item content-date" data-command="confMenuInsertDate" data-tooltip="插入日期 (/ then /)">
<a id="rte-insert-date" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-calendar "></span>
 日期
</a>
</li>
                                            
    
                <li class="dropdown-item content-emoticon" data-command="mceEmotion" data-tooltip="插入表情">
<a id="rte-insert-emoticon" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-emoji "></span>
 表情符号
</a>
</li>
                                            
    
                <li class="dropdown-item content-symbol" data-command="confCharmap" data-tooltip="插入符号">
<a id="rte-insert-symbol" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-symbol "></span>
 符号
</a>
</li>
                                    </ul>
                                <span class="assistive">插入宏</span>
                                <ul id="macro-insert-list">
                                                                                                                                                                                                <li class="dropdown-item macro-insertmention-button" data-macro-name="insertmention-button" data-tooltip="插入&#39;用户提及&#39;宏">
<a id="insertmention-button" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-mention "></span>
 用户提及
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-jiralink" data-macro-name="jiralink" data-tooltip="插入&#39;Jira问题/过滤器&#39;宏">
<a id="jiralink" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-jira "></span>
 Jira问题/过滤器
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-info" data-macro-name="info" data-tooltip="插入&#39;信息&#39;宏">
<a id="info" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-info-filled "></span>
 信息
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-status" data-macro-name="status" data-tooltip="插入&#39;状态&#39;宏">
<a id="status" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                        
    
    <span class="icon confluence-icon-status-macro"></span>
 状态
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-gallery" data-macro-name="gallery" data-tooltip="插入&#39;画廊&#39;宏">
<a id="gallery" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-gallery "></span>
 画廊
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-toc" data-macro-name="toc" data-tooltip="插入&#39;目录&#39;宏">
<a id="toc" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-overview "></span>
 目录
</a>
</li>
                                                                    </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul id="more-macros-list">
                                    
        
                <li class="dropdown-item content-macro" data-command="mceConfMacroBrowser" data-tooltip="打开宏浏览器 (Ctrl+Shift+A)">
<a id="rte-insert-macro" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    其它宏
</a>
</li>
                                    </ul>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                            <ul class="aui-buttons rte-toolbar-group-page-layouts-section-types">
                    <li id="pagelayout-menu" class="toolbar-item toolbar-dropdown">
                        <div class="aui-dd-parent">
                            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-pagelayout" data-tooltip="页面布局">
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-default">页面布局</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                            </a>

                            <ul id="pagelayout-menu-options" class="aui-dropdown hidden">
                                <li class="dropdown-item" data-tooltip="无布局">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-none&quot;, &quot;columns&quot;: 0   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-none">无布局</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple">两栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-left">两栏 (简单，左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-right">两栏 (简单，右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-simple">三栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two">两栏</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-left">两栏 (左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-right">两栏 (右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三列">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three">三列</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (左边和右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-sidebars&quot;, &quot;columns&quot;: [&quot;sidebars&quot;, &quot;large&quot;, &quot;sidebars&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-sidebars">三栏 (左边和右侧栏)</span>
    </a>
</li>
                            </ul>
                        </div>
                    </li>
                </ul>
            
                        
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-undo">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-undo" data-tooltip="回退 (Ctrl+Z)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="undo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-undo ">回退</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-redo" data-tooltip="重做 (Ctrl+Y)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="redo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-redo ">重做</span>
    </a>
</li>
            </ul>
        </div>                                                    <div id="draft-status" style="display:none"></div>
                <div class="aui-toolbar2-secondary">
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-searchreplace">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-searchreplace" data-tooltip="查找/替换">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="searchreplace">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-search ">查找/替换</span>
    </a>
</li>
            </ul>
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-help">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-help" data-tooltip="帮助">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="help">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-help ">键盘快捷方式帮助</span>
    </a>
</li>
            </ul>
        </div>    </div></div>


                <div id="editor-notifications-container"><div id="all-messages"><div id="action-messages"></div></div></div><div id="editor-precursor"><div class="cell"><div class="aui-buttons aui-toolbar2"><button class="aui-button aui-button-subtle rte-button-labels" type="button" data-tooltip="标签" id="rte-button-labels" data-explicit-restrictions="" data-inherited-restrictions=""><span class="icon aui-icon aui-icon-small aui-iconfont-devtools-tag"></span></button><button class="aui-button aui-button-subtle rte-button-restrictions" type="button" data-tooltip="未限制" id="rte-button-restrictions" data-explicit-restrictions="false" data-inherited-restrictions="false"><span class="icon aui-icon aui-icon-small aui-iconfont-unlocked"></span></button></div><div id="content-title-div" class="collaborative"><input type="text" name="title" id="content-title" tabindex="1" class="text pagetitle" autocomplete="off" value="********签署迭代" placeholder="页面标题"></div></div></div>
    
<div id="wysiwyg">
    <div id="rte" class="cell editor-default collaborative">
        <textarea id="wysiwygTextarea" name="wysiwygContent" class="hidden tinymce-editor"></textarea>
    </div>
</div>
<div id="editor-html-source-container" class="hidden">
    <textarea id="editor-html-source" class="monospaceInput"></textarea>
</div>

<div id="preview">
    <div id="previewArea" class="cell">
    </div>
</div>

    <div id="savebar-container"><div id="rte-savebar" class="aui-toolbar aui-toolbar2"><span id="watermark_span" disabled="" style="float:right;display:none"> <input id="watermark_checkbox" onchange="sendCheckboxStatus(this)" type="checkbox"> <font id="add_watermark"> </font>  </span><div class="toolbar-split toolbar-split-row"><div class="toolbar-split toolbar-split-left"><div class="aui-buttons"></div></div><div class="toolbar-split toolbar-split-right"><div id="pluggable-status-container" class="toolbar-item rte-toolbar-pluggable-status"><div id="pluggable-status" class="synchrony"><div class="synchrony-status-indicator"><div class="status-indicator-icon aui-icon aui-icon-small aui-iconfont-devtools-task-in-progress" data-tooltip="自动保存所有修改到草稿中"></div><div class="status-indicator-message" data-tooltip="自动保存所有修改到草稿中">连接中...</div></div></div></div><div class="aui-buttons" id="rte-savebar-tinymce-plugin-point"></div><div class="aui-buttons"><span id="rte-spinner" class="toolbar-item shared-drafts">&nbsp;</span></div><div class="aui-buttons toolbar-group-edit assistive"><button id="rte-button-edit" class="aui-button" title="返回编辑模式" type="button"><span class="trigger-text">编辑</span></button></div><div class="aui-buttons toolbar-group-preview toolbar-group-preview-page toolbar-group-preview-shared-draft"></div><div class="save-button-container"><button class="aui-button aui-button-primary" type="submit" id="rte-button-publish" name="confirm" value="Save" title="保存"><span class="trigger-text">保存</span></button></div><div class="aui-buttons cancel-button-container-shared-draft"><button class="aui-button" type="submit" id="rte-button-cancel" name="cancel" value="cancel">取消</button></div><div class="aui-buttons toolbar-group-preview toolbar-group-ellipsis"><button class="aui-button toolbar-item aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" id="rte-button-ellipsis" type="button" resolved="" aria-controls="rte-ellipsis-menu" aria-expanded="false"><span class="aui-icon aui-icon-small aui-iconfont-more"></span></button></div><div id="rte-ellipsis-menu" data-aui-alignment="top auto" class="aui-style-default aui-dropdown2 aui-layer" resolved="" aria-hidden="true"><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-preview">预览</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-changes">查看更改</a></li></ul></div><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-revert">恢复到最新已发布版本</a></li></ul></div></div></div></div></div></div>

    <section role="dialog" id="quit-editor-dialog" class="aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true"><header class="aui-dialog2-header"></header><div class="aui-dialog2-content"></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button id="qed-publish-button" class="aui-button aui-button-primary update">更新</button><button id="qed-discard-button" title="丢弃所有未发布的变更" class="aui-button toolbar-item exit">恢复页面</button><button id="qed-save-exit-button" class="aui-button aui-button-primary exit">保留草稿</button><button id="qed-close-button" class="aui-button toolbar-item">取消</button></div></footer></section>

    
</div>



<script type="text/x-template" title="dynamic-editor-metadata" id="dynamic-editor-metadata-template">
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.129">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="221520831">
            <meta name="ajs-draft-share-id" content="0b8e5a4a-12c9-43e4-951b-05ff873dd6b7">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTgwZDE1Y2FhMDE4MWY1OTc2YzE4MDA1MCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjIxNTIwODMwLXRpdGxlIjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMjE1MjA4MzAiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE4MGQxNWNhYTAxODFmNTk3NmMxODAwNTAifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTUzMjQ5Mjc5XC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0YW9sYW5nIiwiZnVsbG5hbWUiOiLmoYPmtaoifSwiaXNzIjoiU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZiIsImV4cCI6MTc1Mzg0MzYwNywiaWF0IjoxNzUzNzU3MjA3fQ.12SG2xyB-z-eJZ27VlLEoZCcPExIE8dy1mIra-O0sEA">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="1753842707">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    </script>

<script type="text/x-template" title="tableForm" id="table-form-template">
    <form id="tinymce-table-form" class="aui">
        <div class="field-group">
            <label for="rows">行</label>
            <input id="rows" name="rows" type="text" size="3" autocomplete="off" value="{0}">
        </div>
        <div class="field-group">
            <label for="cols">列</label>
            <input id="cols" name="cols" type="text" size="3" autocomplete="off" value="{1}">
        </div>
        <div class="field-group hidden">
            <input id="width" type="hidden" name="width" value="">
            <label for="width">宽</label>
        </div>
        <div class="group">
            <div class="checkbox">
                <input id="table-heading-checkbox" class="checkbox" type="checkbox" name="heading" checked="checked" value="true">
                <label for="table-heading-checkbox">首行设为表头</label>
            </div>
        </div>
        <div class="group hidden">
            <div class="checkbox">
                <input id="table-equal-width-columns-checkbox" class="checkbox" type="checkbox" name="equal-width-columns" value="false">
                <label for="table-equal-width-columns-checkbox">等宽列</label>
            </div>
        </div>
    </form>
</script>
<input type="hidden" name="draftId" value="221520831" id="draftId"><input type="hidden" name="originalVersion" value="43" id="originalVersion">
<input type="hidden" name="syncRev" value="18.wj7PWu29QbpUmRvZlb56oTw.4" id="syncRev">    <input type="hidden" name="atl_token" value="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">
</div><div class="confluence-page-loading-errors"></div><div class="confluence-page-loading-blanket aui-blanket" aria-hidden="false" style="display: none;"><div class="confluence-loading-indicator"><aui-spinner filled="" size="small" style="color: rgb(240, 240, 240);" resolved=""><div class="aui-spinner spinner"><svg focusable="false" size="20" height="20" width="20" viewBox="0 0 20 20" style="top: 40px;"><circle cx="10" cy="10" r="9"></circle></svg></div></aui-spinner></div></div><div id="content-hover-0" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="content-hover-1" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="content-hover-2" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="inline-dialog-selection-action-panel" class="aui-inline-dialog" style="left: 429px; right: auto; top: 13746px; display: none;"><div class="aui-inline-dialog-contents contents" unselectable="on" style="width: auto; max-height: 726px;"><button data-key="com.atlassian.confluence.plugins.confluence-inline-comments:create-inline-comment" class="aui-button aui-button-compact aui-button-subtle" original-title="Add inline comment" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-comment"></span></button><button data-key="com.atlassian.confluence.plugins.confluence-jira-content:create-Jira-issue-summary" class="aui-button aui-button-compact aui-button-subtle" original-title="Create Jira issue" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-jira"></span></button></div><div id="arrow-selection-action-panel" class="aui-inline-dialog-arrow arrow aui-css-arrow aui-bottom-arrow" unselectable="on" style="position: absolute; left: 31.5px; right: auto; top: 34px;"></div></div><div id="action-dialog-target" style="top: 13788px; height: 17px; left: 433px; width: 56px; display: none;" class=""></div><div id="fileuploadShim" style="position: relative; z-index: 0;"></div><div id="p1j1antjg1ghtfbs14akue8p3ol_html5_container" class="plupload html5" style="position: absolute; background: transparent; width: 1920px; height: 0px; overflow: hidden; z-index: -1; opacity: 0; top: 968px; left: 0px;"><input id="p1j1antjg1ghtfbs14akue8p3ol_html5" style="font-size: 999px; position: absolute; width: 100%; height: 100%;" type="file" accept="" multiple="multiple"></div></body><div id="pvtMessageDiv" class="pvt-message-div"></div></html>