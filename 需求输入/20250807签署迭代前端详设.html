<!DOCTYPE html>
<!-- saved from url=(0312)http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.6saas%E5%8D%B0%E7%AB%A0%E5%9B%BE%E7%89%87%E4%B8%8A%E4%BC%A0%E5%8E%9F%E5%9B%BE%E6%88%AA%E5%9B%BE%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E9%95%BF%E6%AD%8C%EF%BC%89 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                            <title>********签署迭代详设 - 2.技术中心 - 天谷百科</title>
    
        

                        
    
                        
    

                
    
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=IE7">

<meta id="confluence-context-path" name="confluence-context-path" content="">
<meta id="confluence-base-url" name="confluence-base-url" content="http://wiki.timevale.cn:8081">

<meta id="atlassian-token" name="atlassian-token" content="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">


<meta id="confluence-space-key" name="confluence-space-key" content="PRODUCT">
<script type="text/javascript">
        var contextPath = '';
</script>

    

    <meta name="confluence-request-time" content="1753781046221">
        
    
        
            <meta name="ajs-is-space-admin" content="false"> <meta name="ajs-has-space-config" content="false">
            <meta name="ajs-show-space-welcome-dialog" content="true">
            <style>.ia-fixed-sidebar, .ia-splitter-left {width: 285px;}.theme-default .ia-splitter #main {margin-left: 285px;}.ia-fixed-sidebar {visibility: hidden;}</style>
            <meta name="ajs-use-keyboard-shortcuts" content="true">
            <meta name="ajs-discovered-plugin-features" content="{&quot;com.atlassian.confluence.plugins.confluence-page-banner&quot;:[&quot;recently-work-on-contributor-lozenge&quot;],&quot;com.atlassian.confluence.plugins.confluence-dashboard&quot;:[&quot;recently-worked-on-drafts&quot;]}">
            <meta name="ajs-keyboardshortcut-hash" content="5d0be2eb1aace87b32328c0177e65e28">
            <meta name="ajs-is-confluence-admin" content="false">
            <meta name="ajs-connection-timeout" content="10000">
            
    
    
            <meta name="ajs-page-title" content="********签署迭代详设">
            <meta name="ajs-latest-published-page-title" content="********签署迭代详设">
            <meta name="ajs-space-name" content="2.技术中心">
            <meta name="ajs-page-id" content="*********">
            <meta name="ajs-latest-page-id" content="*********">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-parent-page-title" content="签署-2025Q3">
            <meta name="ajs-parent-page-id" content="221511894">
            <meta name="ajs-space-key" content="PRODUCT">
            <meta name="ajs-max-number-editors" content="12">
            <meta name="ajs-macro-placeholder-timeout" content="5000">
            <meta name="ajs-jira-metadata-count" content="0">
            <meta name="ajs-from-page-title" content="">
            <meta name="ajs-can-remove-page" content="false">
            <meta name="ajs-can-remove-page-hierarchy" content="false">
            <meta name="ajs-browse-page-tree-mode" content="view">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-user-display-name" content="桃浪">
            <meta name="ajs-context-path" content="">
            <meta name="ajs-base-url" content="http://wiki.timevale.cn:8081">
            <meta name="ajs-version-number" content="6.13.4">
            <meta name="ajs-build-number" content="7901">
            <meta name="ajs-remote-user" content="taolang">
            <meta name="ajs-remote-user-key" content="2c9d835180d15caa0181f5976c180050">
            <meta name="ajs-remote-user-has-licensed-access" content="true">
            <meta name="ajs-remote-user-has-browse-users-permission" content="true">
            <meta name="ajs-current-user-fullname" content="桃浪">
            <meta name="ajs-current-user-avatar-url" content="/download/attachments/153249279/user-avatar">
            <meta name="ajs-current-user-avatar-uri-reference" content="/download/attachments/153249279/user-avatar">
            <meta name="ajs-static-resource-url-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/_">
            <meta name="ajs-global-settings-attachment-max-size" content="209715200">
            <meta name="ajs-user-locale" content="zh_CN">
            <meta name="ajs-enabled-dark-features" content="site-wide.shared-drafts,site-wide.synchrony,confluence.view.edit.transition,confluence-inline-comments-resolved,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareContentEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.mentions.api.ConfluenceMentionEvent,frontend.editor.v4.compatibility,notification.plugin.api.enabled.com.atlassian.confluence.event.events.security.ForgotPasswordEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.tasklist.event.SendTaskEmailEvent,file-annotations,confluence.efi.onboarding.rich.space.content,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessGrantedEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageMoveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.follow.FollowEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentReplyEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostCreateEvent,lucene.caching.filter,confluence.table.resizable,notification.batch,confluence-inline-comments-rich-editor,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostUpdateEvent,site-wide.synchrony.opt-in,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageCreatedEvent,mobile.supported.version,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentMentionUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.hipchat.api.events.HipChatUserMapped,quick-reload-inline-comments-flags,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostMovedEvent,clc.quick.create,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageUpdateEvent,cql.search.screen,nps.survey.inline.dialog,confluence.efi.onboarding.new.templates,pdf-preview,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageMovedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareCustomEvent,previews.sharing,previews.versions,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.attachment.AttachmentBatchUploadCompletedEvent,collaborative-audit-log,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingLessUsersEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentRemoveEvent,confluence.wrap.macro,previews.conversion-service,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentUpdateEvent,editor.ajax.save,graphql,read.only.mode,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageEditedEvent,previews.trigger-all-file-types,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentResolveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.like.LikeCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentCreateEvent,attachment.extracted.text.extractor,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessRequestedEvent,previews.sharing.pushstate,file-annotations.likes,v2.content.name.searcher,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingNoSpaceCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareDraftEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareAttachmentEvent,confluence-inline-comments,confluence-inline-comments-dangling-comment">
            <meta name="ajs-atl-token" content="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">
            <meta name="ajs-confluence-flavour" content="VANILLA">
            <meta name="ajs-user-date-pattern" content="yyyy-M-d">
            <meta name="ajs-access-mode" content="READ_WRITE">
            <meta name="ajs-render-mode" content="READ_WRITE">
            <meta name="ajs-date.format" content="MMM dd, yyyy">
    
    <link rel="shortcut icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">
    <link rel="icon" type="image/x-icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">

<link rel="search" type="application/opensearchdescription+xml" href="http://wiki.timevale.cn:8081/opensearch/osd.action" title="天谷百科">
    
                    
            <meta name="ajs-create-issue-metadata-show-discovery" content="true">
            

    <script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\u0022\u0022";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-hipchat-integration-plugin:discovery-javascript-data.link-active"]="{\u0022linkActive\u0022:false,\u0022conditionsMet\u0022:true,\u0022admin\u0022:false}";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-feature-discovery-plugin:confluence-feature-discovery-plugin-resources.test-mode"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-help-paths"]="{\u0022entries\u0022:{\u0022applinks.docs.root\u0022:\u0022https://confluence.atlassian.com/display/APPLINKS-054/\u0022,\u0022applinks.docs.diagnostics.troubleshoot.sslunmatched\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthsignatureinvalid\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthtimestamprefused\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.delete.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.adding.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administration.guide\u0022:\u0022Application+Links+Documentation\u0022,\u0022applinks.docs.oauth.security\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.troubleshoot.application.links\u0022:\u0022Troubleshoot+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownerror\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.trusted.apps\u0022:\u0022Configuring+Trusted+Applications+authentication+for+an+application+link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelunsupported\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.ssluntrusted\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownhost\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.delete.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.link.applications\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthproblem\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.migration\u0022:\u0022Update+application+links+to+use+OAuth\u0022,\u0022applinks.docs.relocate.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administering.entity.links\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.upgrade.application.link\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.connectionrefused\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.oauth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.insufficient.remote.permission\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.configuring.application.link.auth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics\u0022:\u0022Application+links+diagnostics\u0022,\u0022applinks.docs.configured.authentication.types\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.adding.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unexpectedresponse\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.basic\u0022:\u0022Configuring+Basic+HTTP+Authentication+for+an+Application+Link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelmismatch\u0022:\u0022OAuth+troubleshooting+guide\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-types"]="{\u0022crowd\u0022:\u0022\u4eba\u7fa4\u0022,\u0022confluence\u0022:\u0022Confluence\u0022,\u0022fecru\u0022:\u0022Fisheye / Crucible\u0022,\u0022stash\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u0022,\u0022jira\u0022:\u0022Jira\u0022,\u0022refapp\u0022:\u0022\u76f8\u5173\u5e94\u7528\u7a0b\u5e8f\u0022,\u0022bamboo\u0022:\u0022\u7af9\u0022,\u0022generic\u0022:\u0022\u901a\u7528\u5e94\u7528\u7a0b\u5e8f\u0022}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.entity-types"]="{\u0022singular\u0022:{\u0022refapp.charlie\u0022:\u0022Charlie\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022},\u0022plural\u0022:{\u0022refapp.charlie\u0022:\u0022\u67e5\u7406\u65af\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u96c6\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.authentication-types"]="{\u0022com.atlassian.applinks.api.auth.types.BasicAuthenticationProvider\u0022:\u0022\u57fa\u672c\u8bbf\u95ee\u0022,\u0022com.atlassian.applinks.api.auth.types.TrustedAppsAuthenticationProvider\u0022:\u0022\u4fe1\u4efb\u7684\u5e94\u7528\u65e0\u6548\u0022,\u0022com.atlassian.applinks.api.auth.types.CorsAuthenticationProvider\u0022:\u0022\u6b4c\u73e5\u0022,\u0022com.atlassian.applinks.api.auth.types.OAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthWithImpersonationAuthenticationProvider\u0022:\u0022Oauth\u0022}";
WRM._unparsedData["com.atlassian.confluence.plugins.synchrony-interop:************************loader.synchrony-status"]="false";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-license-banner:confluence-license-banner-resources.license-details"]="{\u0022daysBeforeLicenseExpiry\u0022:0,\u0022daysBeforeMaintenanceExpiry\u0022:0,\u0022showLicenseExpiryBanner\u0022:false,\u0022showMaintenanceExpiryBanner\u0022:false,\u0022renewUrl\u0022:null,\u0022salesEmail\u0022:null}";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="./********签署迭代前端详设_files/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lt+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lte+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="./********签署迭代前端详设_files/batch(1).css" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d29c9770d1d3dafb92b965ac248fc207-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/bcb04377762ea4527933daff33201ece/_/download/contextbatch/css/atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super/batch.css?conditionalComment=lt+IE+9&amp;confluence.table.resizable=true&amp;highlightactions=true&amp;hostenabled=true" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<script type="text/javascript" src="./********签署迭代前端详设_files/batch.js" data-wrm-key="_super" data-wrm-batch-type="context"></script>
<script type="text/javascript" src="./********签署迭代前端详设_files/batch(2).js" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context"></script><style type="text/css">/* Applied to body so that the parent document does not scroll while the full screen IFrame dialog is open. */
.spark-no-scroll {
  overflow: hidden; }

.YvpxiV5x7vy1i-QG5gfAe {
  /*
     * Styles for iframe (full screen) dialog emulating Atlassian Connect
     */
  /*
     * Styling to imitate dialog chrome styling from Atlassian Connect (at least
     * one specific version).
     * If an add-on would like to override these styles, it could be done by
     * prefixing with the predictable 'app-id' that will be added as an id
     * to the main dialog wrapper div.
     */ }
  .YvpxiV5x7vy1i-QG5gfAe.spark-app-iframe {
    border: none; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper {
    position: fixed;
    z-index: 9000;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    margin: 0;
    padding: 0;
    overflow: hidden;
    /*
         * Change background on fullscreen dialogs to grey.
         * There are two types of fullscreen dialogs in Atlassian Connect:
         * Dialogs with width/height set to 100% have a half-transparent black background,
         * dialogs with size set to "fullscreen" have a grey background.
         */ }
    .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper.spark-fullscreen-dialog {
      background-color: #f5f5f5; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper {
    position: static;
    width: 100%;
    height: 100%;
    overflow: auto;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper.spark-fullscreen-haschrome {
    height: calc(100% - 51px); }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome {
    width: 100%;
    background: black;
    height: 50px;
    margin: 0;
    padding: 0;
    border: none;
    border-bottom: 1px solid #cccccc; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap {
    float: right;
    margin: 0;
    padding: 0;
    height: 50px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-icon-small::before {
    margin-left: 16px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-button {
    height: 50px;
    width: 50px;
    border: none;
    border-left: 1px solid #333;
    border-radius: 0;
    float: left;
    margin-left: 0;
    background: black;
    color: white; }
</style>

    

        
    

        
        <meta name="ajs-site-title" content="天谷百科">
            
    

    
                <link rel="canonical" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
        <link rel="shortlink" href="http://wiki.timevale.cn:8081/x/WRo0DQ">
    <meta name="wikilink" content="[PRODUCT:********签署迭代详设]">
    <meta name="page-version" content="36">
    <meta name="ajs-page-version" content="36">

<style>
        .inject_highlight_overlay {
            position: fixed;
            background-color: #e6171759;
            border-radius: 3px;
            z-index: 99999999999998;
            pointer-events: none
        }

        .inject_highlight_overlay-sub {
            background-color: #e6171733
        }

        .inject_highlight_overlay_content {
            position: fixed;
            padding: 4px 8px;
            background-color: #fff;
            background-clip: padding-box;
            border-radius: 3px;
            color: #333;
            text-align: center;
            border: 1px solid rgba(65, 184, 131, .5);
            font-size: 11px;
            font-family: monospace;
            z-index: 99999999999999;
            pointer-events: none
        }
    </style><link rel="stylesheet" type="text/css" href="./********签署迭代前端详设_files/batch(3).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代前端详设_files/batch(4).js"></script><link rel="stylesheet" type="text/css" href="./********签署迭代前端详设_files/batch(5).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代前端详设_files/batch(6).js"></script><script type="text/javascript" charset="utf-8" src="./********签署迭代前端详设_files/batch(7).js"></script>
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.108">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="221518426">
            <meta name="ajs-draft-share-id" content="52567c9b-818f-4cd3-9b30-4dffc93b2837">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTgwZDE1Y2FhMDE4MWY1OTc2YzE4MDA1MCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjIxNTE4NDI1IjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMjE1MTg0MjUtdGl0bGUiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE4MGQxNWNhYTAxODFmNTk3NmMxODAwNTAifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTUzMjQ5Mjc5XC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0YW9sYW5nIiwiZnVsbG5hbWUiOiLmoYPmtaoifSwiaXNzIjoiU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZiIsImV4cCI6MTc1Mzg2NzUwNywiaWF0IjoxNzUzNzgxMTA3fQ.XoNrvqwn0qGYpC_Eeu_7hmlBqLn0dWbwdAmwyZdPhEs">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="1753866607">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    <link rel="stylesheet" type="text/css" href="./********签署迭代前端详设_files/batch(8).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代前端详设_files/batch(9).js"></script><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><link rel="stylesheet" type="text/css" href="./********签署迭代前端详设_files/batch(10).css"><script type="text/javascript" charset="utf-8" src="./********签署迭代前端详设_files/batch(11).js"></script><style type="text/css">.tooltip {
  text-indent: initial;
  width: auto;
  display: inline-block;
  text-transform: none;
  font-family: Arial, sans-serif;
  font-size: 12px;
  line-height: 20px;
  border-radius: 3px;
  background: rgba(51, 51, 51, 0.9);
  color: #fff;
  left: 50%;
  white-space: nowrap;
  margin-bottom: 15px;
  opacity: 0;
  padding: 5px 10px;
  pointer-events: none;
  position: absolute;
  z-index: 999;
  max-width: 200px;
  -webkit-transition: all 0s 0.3s ease-out;
  -moz-transition: all 0s 0.3s ease-out;
  -ms-transition: all 0s 0.3s ease-out;
  -o-transition: all 0s 0.3s ease-out;
  transition: all 0s 0.3s ease-out;
  /* This bridges the gap so you can mouse into the tooltip without it disappearing */
  /* CSS Triangles */
}
.tooltip:before {
  bottom: -20px;
  content: " ";
  display: block;
  height: 20px;
  left: 0;
  position: absolute;
  width: 100%;
}
.tooltip:after {
  border-left: solid transparent 4px;
  border-right: solid transparent 4px;
  content: " ";
  height: 0;
  left: 50%;
  margin-left: -4px;
  position: absolute;
  width: 0;
}
.tooltip.tooltip-s {
  bottom: 100%;
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
.tooltip.tooltip-s:after {
  border-top: solid rgba(51, 51, 51, 0.9) 4px;
  bottom: -4px;
}
.tooltip.tooltip-n {
  top: 100%;
  -webkit-transform: translateY(0) translateX(-50%);
  -moz-transform: translateY(0) translateX(-50%);
  -ms-transform: translateY(0) translateX(-50%);
  -o-transform: translateY(0) translateX(-50%);
  transform: translateY(0) translateX(-50%);
}
.tooltip.tooltip-n:after {
  border-bottom: solid rgba(51, 51, 51, 0.9) 4px;
  top: -4px;
}
*:hover > .tooltip {
  opacity: 1;
  pointer-events: auto;
}
*:hover > .tooltip.tooltip-s {
  -webkit-transform: translateY(0px) translateX(-50%);
  -moz-transform: translateY(0px) translateX(-50%);
  -ms-transform: translateY(0px) translateX(-50%);
  -o-transform: translateY(0px) translateX(-50%);
  transform: translateY(0px) translateX(-50%);
}
*:hover > .tooltip.tooltip-n {
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
/*
    Header
 */
.cp-header {
  position: relative;
  z-index: 99;
  background-color: #000000;
  padding-left: 10px;
}
.cp-header.cp-header-group {
  display: table;
  box-sizing: border-box;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
}
.cp-header.cp-header-group .cp-header-item {
  display: table-cell;
  vertical-align: top;
}
.cp-header.cp-header-group > .cp-header-item {
  margin: 0;
  box-sizing: border-box;
}
.cp-title-container {
  max-width: 70%;
  display: block;
  float: left;
  height: 50px;
}
.cp-title-container div {
  color: #fff;
  line-height: 50px;
  white-space: nowrap;
  overflow: hidden;
}
.cp-title-container .cp-file-icon {
  width: 45px;
  height: 50px;
  display: inline-block;
  float: left;
  background: no-repeat 10px center;
}
.cp-file-controls {
  padding-left: 0;
  text-align: right;
  float: right;
  max-width: 30%;
  display: block;
  height: 50px;
}
.cp-file-controls > span {
  display: inline-block;
}
/*
    Body
 */
.cp-body {
  height: 100%;
  margin: 0;
}
.cp-error-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  max-width: 640px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.cp-error-message .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-error-message p {
  margin: 10px 0;
  color: #fff;
  line-height: 1.4em;
}
.cp-error-message p.message {
  margin: 10px 0 0 0;
  word-wrap: break-word;
}
.cp-preview-password {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-preview-password .cp-password-lock-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: url("data:image/svg+xml;base64,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") no-repeat center center;
  background-size: contain;
}
.cp-preview-password p {
  margin: 0;
  color: #fff;
}
.cp-preview-password p.message {
  line-height: 1.4em;
  margin-top: 10px;
}
.cp-preview-password .cp-password-fullscreen {
  display: none;
}
.cp-preview-password .cp-password-base .cp-password-input {
  font-size: 14px;
  text-align: left;
  height: 28px;
  border: 1px solid #ccc;
  border-radius: 2px;
  outline: none;
  padding: 0 6px;
  margin: 10px 10px 0 0;
}
.cp-preview-password .cp-password-base .cp-password-button:focus {
  outline: none !important;
}
.cp-file-body .cp-baseline-extension {
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
.cp-file-body.presentation {
  background: #000;
  top: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  left: 0 !important;
  height: 100% !important;
  width: 100% !important;
  z-index: 100;
}
.cp-container {
  font-family: Arial, sans-serif;
  font-size: 16px;
  background-color: rgba(51, 51, 51, 0.95);
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* CONFDEV-26487: z-index is greater than an AUI inline dialog, whose default is 100. */
  z-index: 101;
}
.cp-container .hidden {
  display: none;
}
.cp-container *[role='button'] {
  position: relative;
  -webkit-transform: translateZ(0);
  /* webkit flicker fix */
  -webkit-font-smoothing: antialiased;
  /* webkit text rendering fix */
}
.cp-container .cp-icon {
  padding: 0;
  margin: 0;
  background-color: transparent;
  background-position: center !important;
  background-repeat: no-repeat !important;
  height: 50px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 50px;
  border-radius: 0;
}
.cp-container a.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.cp-container a.cp-icon:hover {
  background-color: #707070;
}
.cp-container a.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
.cp-container button.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: none;
  outline: none;
  cursor: pointer;
}
.cp-container button.cp-icon:hover {
  background-color: #707070;
}
.cp-container button.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
*[id^='cp-container-'][data-embedded='true'],
*[id^='cp-container-'][data-contained='true'] {
  position: relative;
  background: none;
  z-index: auto;
}
*[id^='cp-container-'][data-embedded='true'] .cp-body .cp-file-body,
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  position: relative;
  overflow: auto;
  top: 0;
}
*[id^='cp-container-'][data-embedded='true'] .cp-footer,
*[id^='cp-container-'][data-contained='true'] .cp-footer {
  display: none;
}
*[id^='cp-container-'][data-embedded='true'] .cp-file-controls .fv-close-button,
*[id^='cp-container-'][data-contained='true'] .cp-file-controls .fv-close-button {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-header {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  height: 100%;
}
.cp-small-icon {
  background: no-repeat left center;
  height: 20px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 25px;
}
.cp-file-body {
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50px;
  vertical-align: middle;
  height: calc(100% - 50px);
}
.cp-viewer-layer {
  height: 100%;
}
.cp-file-body.narrow {
  right: 350px;
}
.cp-file-body.short {
  bottom: 160px;
  /* to account for the thumbnail pane */
  height: calc(100% - 210px);
}
.no-scroll {
  overflow: hidden !important;
}
.cp-nav {
  cursor: pointer;
  outline: none;
  border: none;
  background: transparent no-repeat center center;
  color: #fff;
  position: absolute;
  top: 50%;
  margin-top: -50px;
  width: 65px;
  height: 100px;
  text-indent: -999em;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
  -moz-transition: background-color 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s ease-in-out;
}
.cp-nav:after {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}
.cp-nav:hover,
.cp-nav:focus {
  background-color: rgba(0, 0, 0, 0.8);
  transition: background-color 0.1s ease-in-out;
  -moz-transition: background-color 0.1s ease-in-out;
  -webkit-transition: background-color 0.1s ease-in-out;
}
.cp-nav.disabled {
  opacity: 0.5;
  cursor: default;
}
.cp-nav.disabled:hover {
  background-color: transparent;
}
.cp-nav-left {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  left: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvcHJldmlvdXMtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xMCAyNC41MTR2LTEuMDI4TDI1LjMyMyA2Ljc0Yy4zNzQtLjQxLjk4NC0uNDI1IDEuMzczLS4wMjRsMS4xMDggMS4xNGMuMzg0LjM5NS4zOTMgMS4wMzguMDE2IDEuNDRMMTQgMjRsMTMuODIgMTQuNzA1Yy4zNzYuNC4zNzMgMS4wNC0uMDE2IDEuNDRsLTEuMTA4IDEuMTRjLS4zODQuMzk0LTEgLjM4LTEuMzczLS4wMjVMMTAgMjQuNTE0eiIgZmlsbD0iIzk5OSIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-nav-right {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  right: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvbmV4dC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTM4IDI0LjUxNHYtMS4wMjhMMjIuNjc3IDYuNzRjLS4zNzQtLjQxLS45ODQtLjQyNS0xLjM3My0uMDI0bC0xLjEwOCAxLjE0Yy0uMzg0LjM5NS0uMzkzIDEuMDM4LS4wMTYgMS40NEwzNCAyNCAyMC4xOCAzOC43MDVjLS4zNzYuNC0uMzczIDEuMDQuMDE2IDEuNDRsMS4xMDggMS4xNGMuMzg0LjM5NCAxIC4zOCAxLjM3My0uMDI1TDM4IDI0LjUxNHoiIGZpbGw9IiM5OTkiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-error-layer,
.cp-waiting-layer,
.cp-password-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#inline-dialog-sharePreviewPopup {
  z-index: 120;
}
.cp-share-dialog-spinner {
  position: relative;
  top: 12px;
  left: 8px;
}
.cp-waiting-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-waiting-message .file-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-waiting-message p {
  margin: 0;
}
/*
    Sidebar
 */
.cp-sidebar {
  background-color: #fff;
  height: calc(100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  right: 0;
  /* CONFDEV-30274: Delegate rendering of this element to the GPU to avoid painting issues in latest Chrome/Safari */
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
.cp-sidebar.meta-infobar {
  height: calc(100% - 100px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
a.cp-button {
  box-sizing: border-box;
  background: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 3.01px;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: inherit;
  font-size: 14px;
  font-variant: normal;
  font-weight: 400;
  height: 2.14285714em;
  line-height: 1.42857143;
  margin: 0;
  padding: 4px 10px;
  vertical-align: baseline;
  white-space: nowrap;
  text-decoration: none;
  margin: 20px 10px 0 10px;
}
a.cp-button:hover {
  background: #e9e9e9;
  border-color: #999;
}
a.cp-button span.cp-button-icon {
  background-position: 0 0;
  border: none;
  margin: 0;
  padding: 0;
  text-indent: -999em;
  vertical-align: text-bottom;
  display: inline-block;
  text-align: left;
  line-height: 0;
  position: relative;
  vertical-align: text-top;
  height: 16px;
  width: 16px;
}
a.cp-button span.cp-button-icon.icon-download {
  position: relative;
  top: 3px;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=") no-repeat 0 0;
}
/*
    Footer
 */
.cp-footer {
  background-color: #000000;
  position: absolute;
  width: 100%;
  bottom: 0;
}
.cp-footer a {
  color: #fff;
  text-decoration: none;
}
.cp-file-body.meta-infobar {
  bottom: 50px;
  /* when no footer is present */
  height: calc(100% - 100px);
}
.cp-file-body.meta-minimode-toggle {
  bottom: 40px;
  /* when no footer is present */
  height: calc(100% - 90px);
}
.cp-file-body.meta-minimode-toggle.short {
  bottom: 200px;
  height: calc(100% - 250px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
</style><style type="text/css">.cp-image-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDFfZmlsZS1pbWctbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAzIDI0IDI0IDIzLjEwMyAyNCAyMS45OTZWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMjMgMjEuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTZWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTcgMTNsLTMuNzMgMy43M0w4IDlsLTUgNSAuNSAxczMuMDA1LTIuNzggNC41LTQuNWMuNDUzLjU1MyA3IDEwLjUgNyAxMC41aDFsLTIuMTItMy4zNzggMy42Mi0zLjYyIDMgMyAuNS0xLTMtM2gtMXptLTQuMzU3LTQuMDdsLS4yIDIuOCAyLjAyNC0xLjkwNSAyLjAyNCAxLjkwNS0uMi0yLjggMi43MjYtLjQyMy0yLjI3NC0xLjU4NSAxLjM3My0yLjQzNC0yLjYzNi44MjMtMS4wMTMtMi42MS0xLjAxMyAyLjYxLTIuNjM2LS44MjIgMS4zNzQgMi40MzQtMi4yNzUgMS41ODUgMi43MjYuNDI0em0xLjgyNC0zLjM4N2MxLjAzIDAgMS44NjcuODUzIDEuODY3IDEuOTA3IDAgMS4wNTUtLjgzOCAxLjkwOC0xLjg2NyAxLjkwOC0xLjAzIDAtMS44NjctLjg1My0xLjg2Ny0xLjkwOCAwLTEuMDU0LjgzOC0xLjkwNyAxLjg2Ny0xLjkwN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-image-icon.size-48,
.cp-image-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDBfZmlsZS1pbWFnZS14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDM5Ljk4ek0yOS45IDE5LjU2bC0xLjY4IDMgLjQtMy40MTVjLS42NzQtLjMxMy0xLjI3Ny0uNzU0LTEuNzc2LTEuMjkzTDIzLjcyIDE5LjI5bDIuMzMyLTIuNTI2Yy0uMzU0LS42MzMtLjU5LTEuMzQtLjY4LTIuMDlMMjIgMTRsMy4zNzMtLjY3M2MuMDktLjc1Mi4zMjUtMS40NTguNjgtMi4wOUwyMy43MTggOC43MWwzLjEyNCAxLjQzOGMuNS0uNTQgMS4xMDItLjk4IDEuNzc3LTEuMjkzbC0uNC0zLjQxNSAxLjY4IDNjLjM1Ni0uMDcuNzI0LS4xMDcgMS4xLS4xMDcuMzc2IDAgLjc0NC4wMzcgMS4xLjEwN2wxLjY4LTMtLjQgMy40MTVjLjY3NC4zMTMgMS4yNzcuNzU0IDEuNzc2IDEuMjkzTDM4LjI4IDguNzFsLTIuMzMyIDIuNTI2Yy4zNTQuNjMzLjU5IDEuMzQuNjggMi4wOUw0MCAxNGwtMy4zNzMuNjczYy0uMDkuNzUyLS4zMjUgMS40NTgtLjY4IDIuMDlsMi4zMzQgMi41MjctMy4xMjQtMS40MzhjLS41LjU0LTEuMTAyLjk4LTEuNzc3IDEuMjkzbC40IDMuNDE1LTEuNjgtM2MtLjM1Ni4wNy0uNzI0LjEwNy0xLjEuMTA3LS4zNzYgMC0uNzQ0LS4wMzctMS4xLS4xMDd6TTI2LjMzNCAxNGMwLTIuNTc3IDIuMDktNC42NjcgNC42NjctNC42NjdzNC42NjcgMi4wOSA0LjY2NyA0LjY2Ny0yLjA5IDQuNjY3LTQuNjY3IDQuNjY3LTQuNjY3LTIuMDktNC42NjctNC42Njd6bTEuNDg0IDIwLjY4M0wzMyA0MmwtLjUgMUwxNiAxOS41IDUuNSAzMiA1IDMxbDExLTEzIDExLjE5NSAxNS44MDVMMzUgMjZsOCAxMC0uNSAxLTcuNS05LjUtNy4xODMgNy4xODN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-pdf-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDJfZmlsZS1wZGYtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMy44OTYgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMSAyMS45OTVWOC45NkM1LjE3NSA4Ljc1OCA5LjQ0MyA3LjMxNyAxNSA1djE4SDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NXpNMTYgOWgydjE0aC0yVjl6bTcgMTIuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gxOVY4aC0zVjMuNUMxMC40MzQgNS44MiA1LjMwNyA3LjY5IDEgNy45NTJWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTIgMTkuNXYtMWMtMyAxLTYgMi05IDIuNXYxYzMtLjUgNi0xLjUgOS0yLjV6TTMgMThjMy0uNSA2LTEuNSA5LTIuNXYtMWMtMyAxLTYgMi05IDIuNXYxem0wLTV2MWMzLS41IDYtMS41IDktMi41di0xYy0zIDEtNiAyLTkgMi41eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-pdf-icon.size-48,
.cp-pdf-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDFfZmlsZS1wZGYteGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDB6TTQuMDEgNDdDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxNC45ODVjOS4zODQtLjA3NyAxOS4xMzItMi4zMjUgMjktNi43MTJWNDdINC4wMXpNNDAgNDdoLTRWMTZoMy41MDJjLjI3NCAwIC40OTguMjIuNDk4LjQ5M1Y0N3ptLTUgMGgtNFYxMmgzLjUwMmMuMjc0IDAgLjQ5OC4yMjQuNDk4LjVWNDd6bTguOTkgMEg0MVYxNi40OTNDNDEgMTUuNjcgNDAuMzI4IDE1IDM5LjUwMiAxNUgzNnYtMi41YzAtLjgyNy0uNjcyLTEuNS0xLjQ5OC0xLjVIMzFWNi43MjRsLS43MDcuMzJjLTkuOTc4IDQuNTM3LTE5LjgzIDYuODYyLTI5LjI5MyA2Ljk0VjQuMDFDMSAyLjM0NyAyLjM0OCAxIDQuMDEgMWgzOS45OEM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDggMy4wMS0zLjAxIDMuMDF6TTUgMjRjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6bTAgNmM4LjUtLjUgMTItMSAyMC00di0xYy04IDMtMTIgMy41LTIwIDR2MXptMCA2YzguNS0uNSAxMi0xIDIwLTR2LTFjLTggMy0xMiAzLjUtMjAgNHYxem0wIDZjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-text-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDNfZmlsZS10ZXh0LWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0zIDE0djFoMTh2LTFIM3ptMC04djFoMTVWNkgzem0wIDR2MWgxMXYtMUgzem0wIDh2MWgxM3YtMUgzeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-text-icon.size-48,
.cp-text-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDJfZmlsZS10ZXh0LXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTUgMzF2MWgzOHYtMUg1em0wLTE0djFoMzN2LTFINXptMC03djFoMjh2LTFINXptMCAxNHYxaDI0di0xSDV6bTAgMTR2MWgyOHYtMUg1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-document-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDRfZmlsZS1kb2N1bWVudC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5OCAwIDIuMDA1djE5Ljk5QzAgMjMuMTAzLjg5NiAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwNCAwIDIxLjk5NSAwek0yMyAyMS45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTlDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5ek0zIDEwaDd2MUgzdi0xem0wLTRoOXYxSDNWNnptMCA4aDE2djFIM3YtMXptMCA0aDE0djFIM3YtMXpNMTkuOTk3IDVoLTQuOTk0QzE0LjQ1IDUgMTQgNS40NSAxNCA1Ljk5djQuMDJjMCAuNTQ2LjQzNy45ODggMSAuOTloNC45OTdjLjU1NCAwIDEuMDAzLS40NSAxLjAwMy0uOTlWNS45OWMwLS41NDctLjQzOC0uOTktMS4wMDMtLjk5em0tNC4wMDcgNWwyLjQ3Ni0yLjUgMS41MzYuOTFjMCAuODUtLjAwMiAxLjYtLjAwMiAxLjYgMC0uMDEtMi41NTUtLjAxLTQuMDEtLjAxek0xNSA5LjU0M2MtLjAwNC0xLjA0OCAwLTMuNTUyIDAtMy41NTIgMCAuMDEyIDQuOTk3LjAxIDQuOTk3LjAxLjAwNCAwIC4wMDUuNjEyLjAwNSAxLjM1N2wtMS41MzYtLjk5MkwxNSA5LjU0M3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-document-icon.size-48,
.cp-document-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDNfZmlsZS1kb2N1bWVudC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2NC0xLjM0OCAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjU0IDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ4IDEgNC4wMSAxaDM5Ljk4ek01IDM5aDI4di0xSDV2MXptMC03aDMydi0xSDV2MXptMC0yMWgxN3YtMUg1djF6bTAgN2gxNHYtMUg1djF6bTAgN2gxOXYtMUg1djF6bTM2LjAwOC0xNUgyOS45OTJjLTEuMSAwLTEuOTkyLjg5NC0xLjk5MiAxLjk5MnYxMS4wMTZjMCAxLjEuODk0IDEuOTkyIDEuOTkyIDEuOTkyaDExLjAxNmMxLjEgMCAxLjk5Mi0uODkzIDEuOTkyLTEuOTkyVjExLjk5MmMwLTEuMS0uODk0LTEuOTkyLTEuOTkyLTEuOTkyek0yOS45OTIgMjRjLS41NDcgMC0uOTkyLS40NDQtLjk5Mi0uOTkyVjIxbDMtMy41IDUgNi41aC03LjAwOHptMTEuMDE2IDBoLTIuODlsLTEuMS0xLjQzNSAyLjQ2Ni0yLjA2NSAyLjQzMyAyLjljLS4xNTMuMzUzLS41MDIuNi0uOTEuNnpNNDIgMjEuOTJMMzkuNDg0IDE5bC0zLjEzIDIuNjk1TDMyIDE2bC0zIDMuNXYtNy41MDhjMC0uNTQ3LjQ0NS0uOTkyLjk5Mi0uOTkyaDExLjAxNmMuNTQ3IDAgLjk5Mi40NDUuOTkyLjk5MnY5LjkyOHptLTEuNzY0LTkuMTAzbC0xLjk3Ni42MDYtLjc2LTEuOTIzLS43NiAxLjkyMy0xLjk3Ni0uNjA2IDEuMDMgMS43OTQtMS43MDYgMS4xNyAyLjA0NC4zMS0uMTUgMi4wNjRMMzcuNSAxNi43NWwxLjUyIDEuNDA0LS4xNTItMi4wNjMgMi4wNDQtLjMxLTEuNzA2LTEuMTcgMS4wMy0xLjc5M3pNMzcuNSAxNi4zYy0uNzE4IDAtMS4zLS41ODItMS4zLTEuMyAwLS43MTguNTgyLTEuMyAxLjMtMS4zLjcxOCAwIDEuMy41ODIgMS4zIDEuMyAwIC43MTgtLjU4MiAxLjMtMS4zIDEuM3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDVfZmlsZS1zcHJlYWRzaGVldC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTIxLjk5NSAxQzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OUMyMyAyMi41NSAyMi41NSAyMyAyMS45OTUgMjNIMi4wMDVDMS40NSAyMyAxIDIyLjU1IDEgMjEuOTk1VjIuMDA1QzEgMS40NSAxLjQ1IDEgMi4wMDUgMWgxOS45OXpNMTYgMTh2LTRoN3YtMWgtN1Y5aDdWOEg4VjFIN3Y3SDF2MWg2djE0aDFWOWg3djRIOHYxaDd2NEg4djFoN3Y0aDF2LTRoN3YtMWgtN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-48,
.cp-spreadsheet-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDRfZmlsZS1zcHJlYWRzaGVldC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00NyAxNlY0LjAxQzQ3IDIuMzQ3IDQ1LjY1MyAxIDQzLjk5IDFIMTZ2MTVoMzF6bTAgMXYxMEgzMlYxN2gxNXptMCAxMXYxMEgzMlYyOGgxNXptMCAxMXY0Ljk5YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFIMzJ2LThoMTV6bS0xNiA4SDE2di04aDE1djh6bS0xNiAwSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxN2gxNHYzMHpNMSAxNlY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFIMTV2MTVIMXptMTUgMTJoMTV2MTBIMTZWMjh6bTAtMTFoMTV2MTBIMTZWMTd6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDZfZmlsZS1wcmVzZW50YXRpb24tbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgMi4wMDV2MTkuOTlDMCAyMy4xMDIuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5NyAwIDIuMDA1ek0yMS45OTUgMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTE2IDd2MWg1VjdoLTV6bTAgM3YxaDV2LTFoLTV6bS0xIDN2MWg2di0xaC02ek0zIDE3djFoMTh2LTFIM3pNOC41IDQuNWMtMi43NiAwLTUgMi4yMzgtNSA1czIuMjQgNSA1IDUgNS0yLjIzOCA1LTUtMi4yNC01LTUtNXptLTQgNWMwLTIuMDQgMS41MjctMy43MjIgMy41LTMuOTd2My42MjNsLTMuMzkzIDEuMjczYy0uMDctLjI5Ny0uMTA3LS42MDgtLjEwNy0uOTI2em00IDRjLTEuNTM3IDAtMi44Ny0uODY3LTMuNTQtMi4xMzhsMy40MTQtMS4yOCAyLjU4IDIuNTc4Yy0uNjguNTI3LTEuNTMuODQtMi40NTQuODR6bTMuMTYtMS41NDdMOSA5LjI5M1Y1LjUzYzEuOTczLjI0OCAzLjUgMS45MyAzLjUgMy45NyAwIC45MjUtLjMxNCAxLjc3Ni0uODQgMi40NTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-48,
.cp-presentation-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDVfZmlsZS1wcmVzZW50YXRpb24teGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgNC4wMXYzOS45OEMwIDQ2LjIwNSAxLjc5NCA0OCA0LjAxIDQ4aDM5Ljk4YzIuMjE1IDAgNC4wMS0xLjc5NCA0LjAxLTQuMDFWNC4wMUM0OCAxLjc5NSA0Ni4yMDYgMCA0My45OSAwSDQuMDFDMS43OTUgMCAwIDEuNzk0IDAgNC4wMXpNNDMuOTkgMUM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFINC4wMUMyLjM0NyA0NyAxIDQ1LjY1MyAxIDQzLjk5VjQuMDFDMSAyLjM0NyAyLjM0NyAxIDQuMDEgMWgzOS45OHpNMjggMTJ2MWgxNXYtMUgyOHptMCA2djFoMTV2LTFIMjh6bS0yIDZ2MWgxN3YtMUgyNnpNNSAzOHYxaDM4di0xSDV6bTAtN3YxaDM4di0xSDV6bTAtMTQuNWMwIDUuMjQ3IDQuMjUzIDkuNSA5LjUgOS41czkuNS00LjI1MyA5LjUtOS41UzE5Ljc0NyA3IDE0LjUgNyA1IDExLjI1MyA1IDE2LjV6bTEgMGMwLTQuNTI2IDMuNTM4LTguMjI2IDgtOC40ODV2OC4xMzhsLTcuNjIzIDIuODZDNi4xMzIgMTguMjE3IDYgMTcuMzcyIDYgMTYuNXptOC41IDguNWMtMy40NjcgMC02LjQ1LTIuMDc1LTcuNzcyLTUuMDUybDcuNjQ2LTIuODY3IDUuNzczIDUuNzc0QzE4LjY0NSAyNC4xOSAxNi42NjcgMjUgMTQuNSAyNXptNi4zNTMtMi44NTNMMTUgMTYuMjkzVjguMDE1YzQuNDYyLjI2IDggMy45NiA4IDguNDg1IDAgMi4xNjctLjgxIDQuMTQ2LTIuMTQ3IDUuNjQ3eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-code-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDdfZmlsZS1jb2RlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0yLjUgOS41bDUtNSAxIC41TDQgOS41IDguNSAxNGwtMSAuNS01LTV6bTE5IDVsLTUuMDUtNS0uOTUuNSA0LjUgNC41LTQuNSA0LjUuOTUuNSA1LjA1LTV6TTkgMjFoMWw1LTE4aC0xTDkgMjF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-code-icon.size-48,
.cp-code-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwMzlfZmlsZS1jb2RlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTYgMTcuNWwxMC0xMCAuNSAxLTkgOSA5IDktLjUgMS0xMC0xMHptMzcgMTJsLTEwLTEwLS41IDEgOSA5LTkgOSAuNSAxIDEwLTEwek0xOCA0Mi45NjJoMUwzMCA2aC0xTDE4IDQyLjk2MnoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-multimedia-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDhfZmlsZS12aWRlby1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTguMjkzIDFsLTMgM0gxLjcwN2wzLTNoMy41ODZ6TTYuNzA3IDRoMy41ODZsMy0zSDkuNzA3bC0zIDN6bTgtM2wtMyAzaDMuNTg2bDMtM2gtMy41ODZ6bTguMTMuNDZDMjIuNjYgMS4xOCAyMi4zNSAxIDIxLjk5NiAxaC0yLjI4OGwtMyAzaDMuNTg2bDIuNTQ1LTIuNTR6TTMuMjk0IDFIMi4wMDVDMS40NSAxIDEgMS40NSAxIDIuMDA1djEuMjg4TDMuMjkzIDF6TTIzIDRoLTEuMjkzTDIzIDIuNzA3VjR6bTAgMXYxNi45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVY1aDIyek05IDE5LjVsNy01LjUtNy01LjV2MTF6bTUuNS01LjQ3TDEwIDE3LjV2LTdsNC41IDMuNTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-multimedia-icon.size-48,
.cp-multimedia-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDZfZmlsZS12aWRlby14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMHpNNDcgMTF2MzIuOTljMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlMMS4wMyAxMUg0N3ptLTEyLjc5Mi0xaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMGgtNS4zM2w4Ljg1NS05aDUuMzNsLTguODU1IDl6bS03IDBoLTUuMzNsOC44NTUtOWg1LjMzbC04Ljg1NSA5em0tNyAwaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMEgxLjAzMnYtLjI1TDkuNzI2IDFoNS4zMzhsLTguODU2IDl6TTEgNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDMuOTkzTDEgOC4wNDhWNC4wMXpNNDIuOTc0IDEwTDQ3IDUuOTMzVjEwaC00LjAyNnptLTcuMDk3IDBsOC43ODMtOC45MjVDNDYgMS4zOCA0NyAyLjU3NyA0NyA0LjEyNUw0MS4xODYgMTBoLTUuMzF6TTE5IDM5bDEyLTEwLjVMMTkgMTl2MjB6bTEtMThsOS41IDcuNUwyMCAzN1YyMXoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-archive-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTBfZmlsZS1hcmNoaXZlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0xMCAxOGg0di02aC0ydi0xaC0ydjd6bTItN2gydi0xaC0ydjF6bTAtMmgyVjhoLTJ2MXptLTIgMWgyVjloLTJ2MXptMC0yaDJWN2gtMnYxem0yLTFoMlY2aC0ydjF6bS0yLTFoMlY1aC0ydjF6bTItMWgyVjRoLTJ2MXptLTItMWgyVjNoLTJ2MXptMi0xaDJWMmgtMnYxem0tMi0xaDJWMWgtMnYxem0xMS45OTUtMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTAgMi4wMDV2MTkuOTlDMCAyMy4xMDMuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5NyAwIDAgLjg5OCAwIDIuMDA1ek0xMiAxNWMtLjU1MiAwLTEgLjQ0Ny0xIDEgMCAuNTUzLjQ0OCAxIDEgMXMxLS40NDcgMS0xYzAtLjU1My0uNDQ4LTEtMS0xeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-archive-icon.size-48,
.cp-archive-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDhfZmlsZS1hcmNoaXZlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTIwIDMxLjAwMnY4Ljk5QzIwIDQxLjEgMjAuODg3IDQyIDIxLjk5OCA0Mmg0LjAwNEMyNy4xMDUgNDIgMjggNDEuMDk4IDI4IDM5Ljk5di04Ljk4OGMwLS41NTMtLjQ1My0xLjAwMi0uOTk3LTEuMDAyaC02LjAwNmMtLjU1IDAtLjk5Ny40NTYtLjk5NyAxLjAwMnpNMjcuMDAzIDMxYy0uMDA2IDAtLjAwMyA4Ljk5LS4wMDMgOC45OSAwIC41NTgtLjQ1IDEuMDEtLjk5OCAxLjAxaC00LjAwNGMtLjU1NSAwLS45OTgtLjQ0Ny0uOTk4LTEuMDF2LTguOTg4TDI3LjAwMyAzMXpNMjIgMzh2MWMwIC41NTMuNDQzIDEgMS4wMSAxaDEuOThjLjU1OCAwIDEuMDEtLjQ0MyAxLjAxLTF2LTFjMC0uNTUzLS40NDMtMS0xLjAxLTFoLTEuOThjLS41NTggMC0xLjAxLjQ0My0xLjAxIDF6bTIuOTkgMGMuMDEyIDAgLjAxLS4wMDIuMDEgMHYxaC0xLjk5Yy0uMDEyIDAtLjAxLjAwMi0uMDEgMHYtMWgxLjk5ek0yMCA2djFoNVY2aC01em0wLTR2MWg1VjJoLTV6bTMgNnYxaDVWOGgtNXptMC00djFoNVY0aC01em0tMyA2djFoNXYtMWgtNXptMyAydjFoNXYtMWgtNXptLTMgMnYxaDV2LTFoLTV6bTMgMnYxaDV2LTFoLTV6bS0zIDJ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0zLTZ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTFfZmlsZS1nZW5lcmljLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0xMCAxNS40OGMwIDEuNTEgMSAyLjUyIDIuNSAyLjUyczIuNS0xLjAxIDIuNS0yLjUxNlY2Ljk5N0MxNSA0LjUgMTMgMyAxMSAzUzcgNC41IDcgNy4wMDJ2OC40OEM3IDE4LjUyIDkuNDY2IDIxIDEyLjUgMjFjMy4wMzcgMCA1LjUtMi40NjcgNS41LTUuNVY2aC0xdjkuNWMwIDIuNDgtMiA0LjUtNC41IDQuNVM4IDE3Ljk3IDggMTUuNDhWNy4wMDNDOCA1IDkuNSA0IDExIDRzMyAxIDMgMi45OTd2OC40ODdDMTQgMTYuNSAxMy41IDE3IDEyLjUgMTdzLTEuNS0uNS0xLjUtMS41MlYxMGgtMXY1LjQ4eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-48,
.cp-unknown-file-type-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDlfZmlsZS1nZW5lcmljLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5Ljk4QzQ1LjY1My45OCA0NyAyLjMyNiA0NyAzLjk5djQwLjAxNmMwIDEuNjY1LTEuMzQ3IDMuMDEzLTMuMDEgMy4wMTNINC4wMUMyLjM0NyA0Ny4wMiAxIDQ1LjY3NCAxIDQ0LjAxVjMuOTkyQzEgMi4zMjcgMi4zNDcuOTggNC4wMS45OGgzOS45OHpNMTkgMzAuOTk1QzE5IDM0IDIxIDM2IDI0IDM2czUtMiA1LTVWMTMuOTg2QzI5IDEwIDI2IDYuOTkgMjEuOTk2IDYuOTlzLTcuMDA0IDMuMDEtNy4wMDQgN3YxOC4wMDVjMCA1LjAwNSA0IDkgOS4wMDQgOVMzMyAzNyAzMyAzMS45ODJWMTIuOTk0aC0xdjE4Ljk4OGMwIDQuNTE4LTMuNSA4LjAxMy04LjAwNCA4LjAxM3MtOC4wMDQtMy40OTUtOC4wMDQtOFYxMy45OWMwLTMuNDkgMi41LTYgNi4wMDQtNkMyNS41IDcuOTkgMjggMTAuNSAyOCAxMy45ODVWMzFjMCAyLjUtMiA0LTQgNHMtNC0xLjUtNC00LjAwNFYyMGgtMXYxMC45OTZ6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
</style><style type="text/css">.cp-control-panel-close {
  border-left: 1px solid #333;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAwMjdfY2xvc2UtZGlhbG9nPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48cGF0aCBkPSJNOS42NCA4bDQuMDE2IDQuMDE2Yy4yMy4yMy4zNDQuNTAyLjM0NC44MiAwIC4zMTgtLjExNS41OS0uMzQ0LjgyLS4yMy4yMy0uNTAyLjM0NC0uODIuMzQ0LS4zMTggMC0uNTktLjExNS0uODItLjM0NEw4IDkuNjRsLTQuMDE2IDQuMDE2Yy0uMjMuMjMtLjUwMi4zNDQtLjgyLjM0NC0uMzE4IDAtLjU5LS4xMTUtLjgyLS4zNDQtLjIzLS4yMy0uMzQ0LS41MDItLjM0NC0uODIgMC0uMzE4LjExNS0uNTkuMzQ0LS44Mkw2LjM2IDggMi4zNzQgNC4wMTZjLS4yMy0uMjMtLjM0NC0uNTAzLS4zNDQtLjgyIDAtLjMxOC4xMTYtLjU4Ny4zNDUtLjgwNS4yMy0uMjMuNTAzLS4zNC44Mi0uMzQuMzE4IDAgLjU5LjExMy44Mi4zNDNMOCA2LjM3Nmw0LjAzLTQuMDNjLjIzLS4yMy41MDQtLjM0NS44MjItLjM0NS4zMTcgMCAuNTkuMTE3LjgyLjM0Ni4yMi4yMy4zMjguNTAyLjMyOC44MiAwIC4zMTgtLjExLjU4Ni0uMzI4LjgwNUw5LjY0MiA4eiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-control-panel-download,
.cp-waiting-message-download {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvZG93bmxvYWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0zIDE0di0xaDEwdjFIM3ptMTAtNy44NEw4LjIgMTFoLS4zOUwzIDYuMTZsMS4xMi0xLjEzTDcgNy45MlYzLjVjLjA5LTEgLjQ1LTEuNSAxLTEuNXMuODkuMzcgMSAxLjV2NC40MWwyLjkxLTIuODVMMTMgNi4xNnoiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-control-panel-more {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAxMjdfbW9yZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTQgOGMwLS41NTItLjE5NS0xLjAyMy0uNTg2LTEuNDE0QzMuMDI0IDYuMTk2IDIuNTUyIDYgMiA2Yy0uNTUyIDAtMS4wMjMuMTk1LTEuNDE0LjU4NkMuMTk2IDYuOTc2IDAgNy40NDggMCA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4NkMzLjgwNCA5LjAyNCA0IDguNTUyIDQgOHptNiAwYzAtLjU1Mi0uMTk1LTEuMDIzLS41ODYtMS40MTRDOS4wMjQgNi4xOTYgOC41NTIgNiA4IDZjLS41NTIgMC0xLjAyMy4xOTUtMS40MTQuNTg2QzYuMTk2IDYuOTc2IDYgNy40NDggNiA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4Ni4zOS0uMzkuNTg2LS44NjIuNTg2LTEuNDE0em02IDBjMC0uNTUyLS4xOTUtMS4wMjMtLjU4Ni0xLjQxNEMxNS4wMjQgNi4xOTYgMTQuNTUyIDYgMTQgNmMtLjU1MiAwLTEuMDIzLjE5NS0xLjQxNC41ODYtLjM5LjM5LS41ODYuODYyLS41ODYgMS40MTQgMCAuNTUyLjE5NSAxLjAyMy41ODYgMS40MTQuMzkuMzkuODYyLjU4NiAxLjQxNC41ODYuNTUyIDAgMS4wMjMtLjE5NSAxLjQxNC0uNTg2LjM5LS4zOS41ODYtLjg2Mi41ODYtMS40MTR6IiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  z-index: 201;
}
.cp-control-panel-more.cp-dropdown {
  position: relative;
  display: inline-block;
  text-indent: 0;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content {
  pointer-events: auto;
  display: block;
  margin-top: 0;
  padding: 0;
  white-space: nowrap;
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s ease, visibility 0s ease 0.5s;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li {
  font-size: 14px;
  list-style: none;
  padding: 0;
  background-color: #333;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li:hover {
  background-color: #707070;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li a {
  display: block;
  line-height: 2.25rem;
  padding: 0 1.5rem 0 1rem;
  color: #fff;
  text-decoration: none;
}
.cp-control-panel-more.cp-dropdown:focus {
  pointer-events: none;
}
.cp-control-panel-more.cp-dropdown:focus .cp-dropdown-content {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.25s ease, visibility 0s ease 0s;
}
.cp-control-panel-more.cp-dropdown:focus .tooltip {
  display: none !important;
}
</style><style type="text/css">.cp-toolbar {
  display: block;
  opacity: 0;
  position: absolute;
  left: 50%;
  bottom: 20px;
  background-color: #333333;
  border-radius: 5px;
  margin-left: -75px;
  /* to center the div */
}
.cp-toolbar > * {
  position: relative;
}
.cp-toolbar > *:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.cp-toolbar > *:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.cp-toolbar > *::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  opacity: 1;
}
.cp-toolbar-prev-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctdXA8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTMgMTFsNS4wMS01TDEzIDExbC41LTEtNS40OS01LjVMMi41IDEwbC41IDF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-toolbar-next-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctZG93bjwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMyA1bDUuMDEgNUwxMyA1bC41IDEtNS40OSA1LjVMMi41IDYgMyA1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-toolbar-fit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1maXQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaCBCZXRhLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJXaGl0ZSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImNwLXRvb2xiYXItZml0IiBmaWxsPSIjRkZGRkZGIj4KICAgICAgICAgICAgPGcgaWQ9IlBhZ2UtMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMi41MDAwMDAsIDIuNTAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iRmlsbC0xIiBwb2ludHM9IjAuOTk5OSAxMi4wMDAyIDAuOTk5OSA4Ljk5OTIgLTAuMDAwMSA4Ljk5OTIgLTAuMDAwMSAxNC4wMDAyIDUuMDAwOSAxNC4wMDAyIDUuMDAwOSAxMy4wMDAyIDEuOTk5OSAxMy4wMDAyIDUuMDAwOSAxMC4wMDAyIDMuOTk5OSA4Ljk5OTIiPjwvcG9seWdvbj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTIiIHBvaW50cz0iMCAtMC4wMDA0IDAgNC45OTk2IDEgNC45OTk2IDEgMi4wMDA2IDQgNC45OTk2IDUgNC4wMDA2IDIgMS4wMDA2IDUgMS4wMDA2IDUgLTAuMDAwNCI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTksLTAuMDAwNCBMOSwxLjAwMDYgTDEyLDEuMDAwNiBMOSw0LjAwMDYgTDEwLDQuOTk5NiBMMTMsMi4wMDA2IEwxMyw0Ljk5OTYgTDE0LDQuOTk5NiBMMTQsLTAuMDAwNCBMOSwtMC4wMDA0IFogTTEzLDguOTk5NiBMMTMsMTIuMDAwNiBMMTAsOC45OTk2IEw5LDEwLjAwMDYgTDEyLDEzLjAwMDYgTDksMTMuMDAwNiBMOSwxMy45OTk2IEwxNCwxMy45OTk2IEwxNCw4Ljk5OTYgTDEzLDguOTk5NiBaIiBpZD0iRmlsbC0zIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-minus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1taW51czwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1taW51cyIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxnIGlkPSJQYWdlLTEiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEuMDAwMDAwLCAxLjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHBvbHlnb24gaWQ9IkZpbGwtMSIgcG9pbnRzPSI0LjEyNSA3LjgyNTUgMTAuMTI1IDcuODI1NSAxMC4xMjUgNi43OTU1IDQuMTI1IDYuNzk1NSI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTcuMTI1LDEzLjM3MiBDMy44ODYsMTMuMzcyIDEuMjUsMTAuNjUzIDEuMjUsNy4zMTEgQzEuMjUsMy45NjkgMy44ODYsMS4yNSA3LjEyNSwxLjI1IEMxMC4zNjQsMS4yNSAxMywzLjk2OSAxMyw3LjMxMSBDMTMsMTAuNjUzIDEwLjM2NCwxMy4zNzIgNy4xMjUsMTMuMzcyIE0xNi43MTYsMTUuMTQ3IEwxMy4xOCwxMS42MTIgQzEzLjExOSwxMS41NTIgMTMuMDQ1LDExLjUxOCAxMi45NzQsMTEuNDc2IEMxMy43NzYsMTAuMjkzIDE0LjI1LDguODU4IDE0LjI1LDcuMzExIEMxNC4yNSwzLjI4IDExLjA1NCwwIDcuMTI1LDAgQzMuMTk2LDAgMCwzLjI4IDAsNy4zMTEgQzAsMTEuMzQzIDMuMTk2LDE0LjYyMiA3LjEyNSwxNC42MjIgQzguODQyLDE0LjYyMiAxMC40MTksMTMuOTk1IDExLjY1LDEyLjk1MyBDMTEuNjc5LDEyLjk5IDExLjY5MiwxMy4wMzMgMTEuNzI2LDEzLjA2NiBMMTUuMjYyLDE2LjYwMSBDMTUuNjUyLDE2Ljk5MiAxNi4yODUsMTYuOTkyIDE2LjY3NiwxNi42MDEgTDE2LjcxNiwxNi41NjIgQzE3LjEwNiwxNi4xNzIgMTcuMTA2LDE1LjUzOCAxNi43MTYsMTUuMTQ3IiBpZD0iRmlsbC0yIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-plus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wbHVzPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2ggQmV0YS48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iV2hpdGUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJjcC10b29sYmFyLXBsdXMiIGZpbGw9IiNGRkZGRkYiPgogICAgICAgICAgICA8ZyBpZD0idG9vbGJhci1wbHVzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxLjAwMDAwMCwgMS4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTEiIHBvaW50cz0iNi42MjUgNC4yMjUxIDcuNjI1IDQuMjI1MSA3LjYyNSA2Ljc5NTEgMTAuMTI1IDYuNzk1MSAxMC4xMjUgNy44MjUxIDcuNjI1IDcuODI1MSA3LjYyNSAxMC4zOTQxIDYuNjI1IDEwLjM5NDEgNi42MjUgNy44MjUxIDQuMTI1IDcuODI1MSA0LjEyNSA2Ljc5NTEgNi42MjUgNi43OTUxIj48L3BvbHlnb24+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNNy4xMjUsMTMuMzcyIEMzLjg4NiwxMy4zNzIgMS4yNSwxMC42NTMgMS4yNSw3LjMxMSBDMS4yNSwzLjk2OSAzLjg4NiwxLjI1IDcuMTI1LDEuMjUgQzEwLjM2NCwxLjI1IDEzLDMuOTY5IDEzLDcuMzExIEMxMywxMC42NTMgMTAuMzY0LDEzLjM3MiA3LjEyNSwxMy4zNzIgTTE2LjcxNiwxNS4xNDcgTDEzLjE4LDExLjYxMiBDMTMuMTE5LDExLjU1MiAxMy4wNDUsMTEuNTE4IDEyLjk3NCwxMS40NzYgQzEzLjc3NiwxMC4yOTMgMTQuMjUsOC44NTggMTQuMjUsNy4zMTEgQzE0LjI1LDMuMjggMTEuMDU0LDAgNy4xMjUsMCBDMy4xOTYsMCAwLDMuMjggMCw3LjMxMSBDMCwxMS4zNDMgMy4xOTYsMTQuNjIyIDcuMTI1LDE0LjYyMiBDOC44NDIsMTQuNjIyIDEwLjQxOSwxMy45OTUgMTEuNjUsMTIuOTUzIEMxMS42NzksMTIuOTkgMTEuNjkyLDEzLjAzMyAxMS43MjYsMTMuMDY2IEwxNS4yNjIsMTYuNjAxIEMxNS42NTIsMTYuOTkyIDE2LjI4NSwxNi45OTIgMTYuNjc2LDE2LjYwMSBMMTYuNzE2LDE2LjU2MiBDMTcuMTA2LDE2LjE3MiAxNy4xMDYsMTUuNTM4IDE2LjcxNiwxNS4xNDciIGlkPSJGaWxsLTIiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
}
.cp-toolbar-presentation::before {
  background-image: url("data:image/svg+xml;base64,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");
}
.cp-toolbar-presentation-exit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdCIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJQYWdlLTEiIHBvaW50cz0iMTQuNSAzLjUgOS41MDEgOC40OTkgNC41IDMuNSAzLjUgNC41IDguNTAxIDkuNDk5IDMuNSAxNC41IDQuNSAxNS41IDkuNTAxIDEwLjQ5OSAxNC41IDE1LjUgMTUuNSAxNC41IDEwLjUgOS40OTkgMTUuNSA0LjUiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar button,
.cp-toolbar a {
  height: 40px !important;
  width: 40px !important;
  background-color: transparent;
  cursor: pointer;
}
.cp-toolbar button.inactive,
.cp-toolbar a.inactive {
  pointer-events: none;
  cursor: default;
  opacity: 0.5;
}
.cp-waiting-message-spinner {
  position: relative;
}
.cp-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: 0;
}
.cp-spinner .cp-spinner-blades {
  position: absolute;
  top: -2px;
  opacity: 0.25;
}
.cp-spinner .cp-spinner-blades div {
  position: absolute;
  width: 12px;
  height: 4px;
  box-shadow: rgba(0, 0, 0, 0.0980392) 0px 0px 1px;
  border-radius: 2px;
  background: #ffffff;
  -webkit-transform-origin: left center 0px;
          transform-origin: left center 0px;
}
.cp-spinner .cp-spinner-blade-1 {
  -webkit-animation: cp-spinner-anim-1-12 0.666667s linear infinite;
          animation: cp-spinner-anim-1-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-1 div {
  -webkit-transform: rotate(0deg) translate(10px, 0px);
          transform: rotate(0deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-2 {
  -webkit-animation: cp-spinner-anim-2-12 0.666667s linear infinite;
          animation: cp-spinner-anim-2-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-2 div {
  -webkit-transform: rotate(30deg) translate(10px, 0px);
          transform: rotate(30deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-3 {
  -webkit-animation: cp-spinner-anim-3-12 0.666667s linear infinite;
          animation: cp-spinner-anim-3-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-3 div {
  -webkit-transform: rotate(60deg) translate(10px, 0px);
          transform: rotate(60deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-4 {
  -webkit-animation: cp-spinner-anim-4-12 0.666667s linear infinite;
          animation: cp-spinner-anim-4-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-4 div {
  -webkit-transform: rotate(90deg) translate(10px, 0px);
          transform: rotate(90deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-5 {
  -webkit-animation: cp-spinner-anim-5-12 0.666667s linear infinite;
          animation: cp-spinner-anim-5-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-5 div {
  -webkit-transform: rotate(120deg) translate(10px, 0px);
          transform: rotate(120deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-6 {
  -webkit-animation: cp-spinner-anim-6-12 0.666667s linear infinite;
          animation: cp-spinner-anim-6-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-6 div {
  -webkit-transform: rotate(150deg) translate(10px, 0px);
          transform: rotate(150deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-7 {
  -webkit-animation: cp-spinner-anim-7-12 0.666667s linear infinite;
          animation: cp-spinner-anim-7-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-7 div {
  -webkit-transform: rotate(180deg) translate(10px, 0px);
          transform: rotate(180deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-8 {
  -webkit-animation: cp-spinner-anim-8-12 0.666667s linear infinite;
          animation: cp-spinner-anim-8-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-8 div {
  -webkit-transform: rotate(210deg) translate(10px, 0px);
          transform: rotate(210deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-9 {
  -webkit-animation: cp-spinner-anim-9-12 0.666667s linear infinite;
          animation: cp-spinner-anim-9-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-9 div {
  -webkit-transform: rotate(240deg) translate(10px, 0px);
          transform: rotate(240deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-10 {
  -webkit-animation: cp-spinner-anim-10-12 0.666667s linear infinite;
          animation: cp-spinner-anim-10-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-10 div {
  -webkit-transform: rotate(270deg) translate(10px, 0px);
          transform: rotate(270deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-11 {
  -webkit-animation: cp-spinner-anim-11-12 0.666667s linear infinite;
          animation: cp-spinner-anim-11-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-11 div {
  -webkit-transform: rotate(300deg) translate(10px, 0px);
          transform: rotate(300deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-12 {
  -webkit-animation: cp-spinner-anim-12-12 0.666667s linear infinite;
          animation: cp-spinner-anim-12-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-12 div {
  -webkit-transform: rotate(330deg) translate(10px, 0px);
          transform: rotate(330deg) translate(10px, 0px);
}
@-webkit-keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@-webkit-keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@-webkit-keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@-webkit-keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@-webkit-keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@-webkit-keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@-webkit-keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
@keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
</style><style type="text/css">.cp-unknown-file-type-view-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.cp-unknown-file-type-view {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-unknown-file-type-view .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
.cp-unknown-file-type-view p {
  margin: 0;
  color: #fff;
}
.cp-unknown-file-type-view a.download-button {
  margin: 20px 0 0 0;
}
.cp-unknown-file-type-view a.download-button span.icon-download {
  position: relative;
  top: 3px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  background-repeat: none;
  background-position: 0 0;
}
</style><style type="text/css">.cp-thumbnail-img-container.has-thumbnail {
  background-image: none !important;
}
.cp-sink.expanded .cp-thumbnails {
  display: block;
}
.cp-info {
  color: #fff;
  display: inline-block;
  width: 100%;
  font-size: 0.85em;
  height: 40px;
  line-height: 40px;
  text-decoration: none;
  vertical-align: middle;
}
.cp-info .cp-files-label {
  display: block;
  padding-left: 20px;
}
.cp-info .cp-files-label:focus {
  outline: none;
  background-color: #707070;
}
.cp-info .cp-files-label:active {
  background-color: #000;
}
.cp-files-collapser {
  margin-left: 5px;
}
.cp-files-collapser:after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background-size: 12px;
  background-repeat: no-repeat;
  background-position: 0 0;
  position: relative;
  top: 2px;
  left: 5px;
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDE2IDE2IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDM5ICgzMTQ5NCkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+YXJyb3dfdXA8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaCBCZXRhLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJhcnJvd191cCIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxnIGlkPSJpY29uLXRvZ2dsZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOC4xMDUyNjMsIDguMDAwMDAwKSByb3RhdGUoOTAuMDAwMDAwKSB0cmFuc2xhdGUoLTguMTA1MjYzLCAtOC4wMDAwMDApIHRyYW5zbGF0ZSgyLjEwNTI2MywgMy4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xMC42ODQ0MjExLDkuNzg5NDczNjggQzEwLjQ0MTg5NDcsOS43ODk0NzM2OCAxMC4xOTkzNjg0LDkuNjk3MjYzMTYgMTAuMDE0OTQ3NCw5LjUxMTU3ODk1IEw2LjA2NzU3ODk1LDUuNTY0MjEwNTMgQzUuNjk2ODQyMTEsNS4xOTUzNjg0MiA1LjY5Njg0MjExLDQuNTk0NzM2ODQgNi4wNjc1Nzg5NSw0LjIyNTI2MzE2IEwxMC4wMTQ5NDc0LDAuMjc3ODk0NzM3IEMxMC4zODQ0MjExLC0wLjA5MjIxMDUyNjMgMTAuOTgzNzg5NSwtMC4wOTIyMTA1MjYzIDExLjM1Mzg5NDcsMC4yNzc4OTQ3MzcgQzExLjcyNCwwLjY0NzM2ODQyMSAxMS43MjQsMS4yNDggMTEuMzUzODk0NywxLjYxNjg0MjExIEw4LjA3Niw0Ljg5NDczNjg0IEwxMS4zNTM4OTQ3LDguMTcyNjMxNTggQzExLjcyNCw4LjU0MTQ3MzY4IDExLjcyNCw5LjE0MjEwNTI2IDExLjM1Mzg5NDcsOS41MTE1Nzg5NSBDMTEuMTY4ODQyMSw5LjY5NzI2MzE2IDEwLjkyNjMxNTgsOS43ODk0NzM2OCAxMC42ODQ0MjExLDkuNzg5NDczNjggTDEwLjY4NDQyMTEsOS43ODk0NzM2OCBaIiBpZD0iU2hhcGUiPjwvcGF0aD4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik00Ljg5NDczNjg0LDkuNzg5NDczNjggQzQuNjUyMjEwNTMsOS43ODk0NzM2OCA0LjQwOTY4NDIxLDkuNjk3MjYzMTYgNC4yMjUyNjMxNiw5LjUxMTU3ODk1IEwwLjI3Nzg5NDczNyw1LjU2NDIxMDUzIEMtMC4wOTI4NDIxMDUzLDUuMTk1MzY4NDIgLTAuMDkyODQyMTA1Myw0LjU5NDczNjg0IDAuMjc3ODk0NzM3LDQuMjI1MjYzMTYgTDQuMjI1MjYzMTYsMC4yNzc4OTQ3MzcgQzQuNTk0NzM2ODQsLTAuMDkyMjEwNTI2MyA1LjE5NDEwNTI2LC0wLjA5MjIxMDUyNjMgNS41NjQyMTA1MywwLjI3Nzg5NDczNyBDNS45MzQzMTU3OSwwLjY0NzM2ODQyMSA1LjkzNDMxNTc5LDEuMjQ4IDUuNTY0MjEwNTMsMS42MTY4NDIxMSBMMi4yODYzMTU3OSw0Ljg5NDczNjg0IEw1LjU2NDIxMDUzLDguMTcyNjMxNTggQzUuOTM0MzE1NzksOC41NDE0NzM2OCA1LjkzNDMxNTc5LDkuMTQyMTA1MjYgNS41NjQyMTA1Myw5LjUxMTU3ODk1IEM1LjM3OTc4OTQ3LDkuNjk3MjYzMTYgNS4xMzcyNjMxNiw5Ljc4OTQ3MzY4IDQuODk0NzM2ODQsOS43ODk0NzM2OCBMNC44OTQ3MzY4NCw5Ljc4OTQ3MzY4IFoiIGlkPSJTaGFwZSI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=");
}
.cp-files-collapser.down:after {
  transform: rotate(180deg);
}
.cp-thumbnails {
  background-color: #333;
  display: none;
  height: 130px;
  margin-top: 0;
  padding-bottom: 20px;
  padding-left: 0;
  padding-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;
}
.cp-thumbnail {
  color: #fff;
  display: inline-block;
  text-align: center;
  padding-left: 10px;
  max-width: 145px;
}
.cp-thumbnail.selected .cp-thumbnail-img {
  border: 2px solid #3572B0;
  border-radius: 3px;
}
.cp-thumbnail-group {
  margin: 0;
}
.cp-thumbnail-img-container {
  display: block;
  height: 60px;
  overflow: hidden;
  background-size: contain;
  background: no-repeat center center;
  background-size: 48px 48px;
}
.cp-thumbnail-img {
  background-color: #444;
  border: 2px solid #333;
  width: 100px;
  margin: 10px auto 0 auto;
  padding: 10px 20px;
}
.cp-thumbnail-img:hover {
  background-color: #555;
}
.cp-thumbnail-title {
  color: #f5f5f5;
  font-size: 12px;
  margin-top: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cp-thumbnail-img img {
  height: 60px;
}
</style><script type="text/javascript" charset="utf-8" async="" src="./********签署迭代前端详设_files/mediaviewer-1.js"></script><style type="text/css">.cp-image-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  white-space: nowrap;
  box-sizing: border-box;
}
.cp-image-preview .cp-scale-info {
  position: absolute;
  width: 100px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin-left: -50px;
  margin-top: -20px;
  border-radius: 5px;
  background: #333333;
  background: rgba(38, 38, 38, 0.5);
  line-height: 40px;
  color: #fff;
  z-index: 1;
  display: none;
}
.cp-image-preview .cp-image-container.cp-fit-width {
  width: 100%;
  height: auto;
}
.cp-image-preview .cp-image-container.cp-fit-height {
  width: auto;
  height: 100%;
}
.cp-image-preview img {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKAQMAAAC3/F3+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAABlBMVEXf39////8zI3BgAAAAEUlEQVQIW2Nk38mIjH5wICMAez4Iyz2C/F8AAAAASUVORK5CYII=") repeat;
  /* vertical-align prevents extra space from being added between
           the top of the image and it's container */
  vertical-align: middle;
}
.cp-image-preview img.pixelate {
  /* Prevent images from being smoothed when scaled up */
  image-rendering: optimizeSpeed;
  /* Legal fallback */
  image-rendering: -moz-crisp-edges;
  /* Firefox        */
  image-rendering: -o-crisp-edges;
  /* Opera          */
  image-rendering: -webkit-optimize-contrast;
  /* Safari         */
  image-rendering: optimize-contrast;
  /* CSS3 Proposed  */
  image-rendering: crisp-edges;
  /* CSS4 Proposed  */
  image-rendering: pixelated;
  /* CSS4 Proposed  */
  -ms-interpolation-mode: nearest-neighbor;
  /* IE8+           */
}
.cp-img.pannable {
  cursor: pointer;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  cursor: grab;
}
.cp-img.pannable.panning {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.cp-image-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
</style><link rel="stylesheet" type="text/css" href="./********签署迭代前端详设_files/com.atlassian.confluence.plugins.confluence-previews_confluence-previews-css.css"></head>

    
<body id="com-atlassian-confluence" class="theme-default  aui-layout aui-theme-default synchrony-active" data-aui-version="7.9.7">

        
            <div id="stp-licenseStatus-banner"></div>
    <ul id="assistive-skip-links" class="assistive">
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#title-heading">转至内容</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#breadcrumbs">转至导航栏</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#header-menu-bar">转至主菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#navigation">转至动作菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#quick-search-query">转至快速搜索</a></li>
</ul>
<!-- Render the dialog --><section role="dialog" id="page-view-tracker-dialog" class="aui-layer aui-dialog2" style="width:820px;" data-aui-modal="true" aria-hidden="true"><!-- Dialog header --><header class="aui-dialog2-header"><!-- The dialogs title --><h2 class="aui-dialog2-header-main">Page View Statistics</h2></header><!-- Main dialog content --><div class="aui-dialog2-content" id="page-view-tracker-content" style="height:400px; overflow-y: scroll;"></div><!-- Dialog footer --><footer class="aui-dialog2-footer"><!-- Actions to render on the right of the footer --><div class="aui-dialog2-footer-actions"><button id="page-view-tracker-help" class="aui-button aui-button-link">Help</button><button id="page-view-tracker-close" class="aui-button aui-button-link">Close</button></div></footer></section><div id="page">
<div id="full-height-container"><ul id="messageContainer"></ul>
    <div id="header-precursor">
        <div class="cell">
            
                            </div>
    </div>
        





<header id="header" role="banner" class="">
    <nav class="aui-header aui-dropdown2-trigger-group" role="navigation" data-aui-responsive="true"><div class="aui-header-inner"><div class="aui-header-primary"><h1 id="logo" class="aui-header-logo aui-header-logo-custom"><a href="http://wiki.timevale.cn:8081/"><img src="./********签署迭代前端详设_files/atl.site.logo" alt="天谷百科" data-aui-responsive-header-index="0"><span class="aui-header-logo-text">天谷百科</span></a></h1><ul class="aui-nav" resolved="" style="width: auto;">
                            <li>
            
        
        
<a id="space-menu-link" class=" aui-dropdown2-trigger aui-nav-link" aria-controls="space-menu-link-content" aria-haspopup="true" role="button" title="空间" tabindex="0" data-aui-trigger="" resolved="" aria-expanded="false" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#space-menu-link-content">空间<span class="icon aui-icon-dropdown"></span></a><div id="space-menu-link-content" class="aui-dropdown2 aui-style-default aui-dropdown2-in-header aui-layer" role="menu" aria-hidden="true" resolved=""><div role="presentation"></div></div>
        </li>
                    <li>
            
    
        
<a id="people-directory-link" href="http://wiki.timevale.cn:8081/browsepeople.action" class=" aui-nav-imagelink" title="人员">
            <span>人员</span>
    </a>
        </li>
                                        <li class="aui-buttons">
                                    
                                        <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=PRODUCT&amp;fromPageId=*********&amp;src=quick-create" id="quick-create-page-button" class="aui-button aui-button-primary" title="创建空白页 (c)" tabindex="0">创建</a>
                                <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=PRODUCT&amp;fromPageId=*********" id="create-page-button" class="aui-button aui-button-primary clc-create-dialog-btn" title="从模板创建" tabindex="0"><span class="aui-icon aui-icon-small aui-iconfont-more">创建 </span></a>
            </li>
    </ul>
</div><div class="aui-header-secondary"><ul class="aui-nav" resolved="">
                        <li>
        <form id="quick-search" class="aui-quicksearch dont-default-focus header-quicksearch" action="http://wiki.timevale.cn:8081/dosearchsite.action" method="get"><fieldset><label for="quick-search-query" class="assistive">快速搜索</label><input id="quick-search-query" class="text app-search search quick-search-query" type="text" accesskey="q" autocomplete="off" name="queryString" title="快速搜索 (g ，g或 /)" placeholder="搜索"><input id="quick-search-submit" class="quick-search-submit" type="submit" value="搜索"><div class="aui-dd-parent quick-nav-drop-down"></div></fieldset></form>
    </li>
        <li>
            
        <a id="help-menu-link" class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" title="帮助" resolved="" aria-controls="help-menu-link-content" aria-expanded="false">
        <span class="aui-icon aui-icon-small aui-iconfont-question-filled">帮助</span>
    </a>
    <nav id="help-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                    <div class="aui-dropdown2-section">
                                <ul id="help-menu-link-leading" class="aui-list-truncate section-leading first">
                                            <li>
        
            
<a id="confluence-help-link" href="https://docs.atlassian.com/confluence/docs-613/" class="    " title="访问Confluence文档首页" target="_blank">
        在线帮助
</a>
</li>
                                            <li>
    
                
<a id="keyboard-shortcuts-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="    " title="查看可用的键盘快捷方式 (?)">
        快捷键
</a>
</li>
                                            <li>
    
            
<a id="feed-builder-link" href="http://wiki.timevale.cn:8081/dashboard/configurerssfeed.action" class="    " title="创建个性化的 RSS源。">
        RSS源建立器
</a>
</li>
                                            <li>
    
            
<a id="whats-new-menu-link" href="https://confluence.atlassian.com/display/DOC/Confluence+6.13+Release+Notes?a=false" class="    " title="">
        新功能
</a>
</li>
                                            <li>
    
                
<a id="gadget-directory-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="   user-item administration-link " title="浏览小工具">
        可用的小工具
</a>
</li>
                                            <li>
    
            
<a id="confluence-about-link" href="http://wiki.timevale.cn:8081/aboutconfluencepage.action" class="    " title="获取关于Confluence的更多信息">
        关于Confluence
</a>
</li>
                                            <li>
    
            
<a id="index.section.link" href="https://dragonsoft.atlassian.net/wiki/spaces/DAS/pages/7078120" class="    " title="">
        水印插件 - 用户手册
</a>
</li>
                                    </ul>
            </div>
            </nav>
    
    </li>
        <li>
                
    
    </li>
        <li>
                    
        
            
<a id="notifications-anchor" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="mw-anchor read aui-nav-imagelink" title="打开通知 (g ，n)"><div class="badge-i aui-icon aui-icon-small aui-iconfont-notification"></div><span class="badge-w"><span class="badge">0</span></span></a>
    
    </li>
        <li>
                                            
        <a id="user-menu-link" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless " aria-haspopup="true" data-username="taolang" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" title="桃浪" resolved="" aria-controls="user-menu-link-content" aria-expanded="false">
                    <div class="aui-avatar aui-avatar-small">
                <div class="aui-avatar-inner">
                                                                                                    <img src="./********签署迭代前端详设_files/user-avatar">
                </div>
            </div>
            <span class="aui-icon-dropdown"></span>
        </a>
        <nav id="user-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-preferences" class="aui-list-truncate section-user-preferences first">
                                                    <li>
        
            
<a id="view-personal-space-link" href="http://wiki.timevale.cn:8081/spaces/viewspace.action?key=~taolang" class="   user-item personal-space " title="">
        个人空间
</a>
</li>
                                                    <li>
    
            
<a id="view-user-history-link" href="http://wiki.timevale.cn:8081/users/viewuserhistory.action" class="   user-item user-history popup-link " title=" (g ，r)">
        最近浏览
</a>
</li>
                                                    <li>
    
            
<a id="user-recently-worked-on" href="http://wiki.timevale.cn:8081/dashboard.action#recently-worked" class="   user-item " title="">
        最近的工作
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-content" class="aui-list-truncate section-user-content">
                                                    <li>
    
            
<a id="view-user-profile-link" href="http://wiki.timevale.cn:8081/users/viewmyprofile.action" class="   user-item user-profile " title="">
        用户信息
</a>
</li>
                                                    <li>
    
            
<a id="view-mytasks-link" href="http://wiki.timevale.cn:8081/plugins/inlinetasks/mytasks.action" class="   user-item list-user-status " title="">
        任务
</a>
</li>
                                                    <li>
    
            
<a id="user-favourites-link" href="http://wiki.timevale.cn:8081/users/viewmyfavourites.action" class="   user-item " title="">
        收藏夹
</a>
</li>
                                                    <li>
    
            
<a id="user-watches-link" href="http://wiki.timevale.cn:8081/users/viewnotifications.action" class="   user-item " title="">
        关注
</a>
</li>
                                                    <li>
    
            
<a id="user-drafts-link" href="http://wiki.timevale.cn:8081/users/viewmydrafts.action" class="   user-item " title="">
        草稿
</a>
</li>
                                                    <li>
    
            
<a id="user-network-link" href="http://wiki.timevale.cn:8081/users/viewfollow.action?username=taolang" class="   follow-link " title="">
        网络
</a>
</li>
                                                    <li>
    
            
<a id="user-settings-link" href="http://wiki.timevale.cn:8081/users/viewmysettings.action" class="   user-item " title="">
        设置
</a>
</li>
                                                    <li>
    
            
<a id="upm-requests-link" href="http://wiki.timevale.cn:8081/plugins/servlet/upm/requests?source=header_user" class="    " title="">
        Atlassian Marketplace
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-operations" class="aui-list-truncate section-user-operations">
                                                    <li>
    
            
<a id="logout-link" href="http://wiki.timevale.cn:8081/logout.action" class="   user-item logout-link " title="">
        注销
</a>
</li>
                                            </ul>
                </div>
                    </nav>
                    
    </li>
    </ul>
</div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    <br class="clear">
</header>
    

    
    	<div class="ia-splitter">
    		<div class="ia-splitter-left">
    			<div class="ia-fixed-sidebar" style="width: 393px; visibility: visible; top: 40px; left: 0px;">
                                            
                            <div class="acs-side-bar ia-scrollable-section"><div class="acs-side-bar-space-info tipsy-enabled" data-configure-tooltip="编辑空间详情"><div class="avatar"><div class="space-logo" data-key="PRODUCT" data-name="2.技术中心" data-entity-type="confluence.space"><div class="avatar-img-container"><div class="avatar-img-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=sidebar" title="2.技术中心"><img class="avatar-img" src="./********签署迭代前端详设_files/PRODUCT" alt="2.技术中心"></a></div></div></div></div><div class="space-information-container"><div class="name"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=sidebar" title="2.技术中心">2.技术中心</a></div><div class="flyout-handle icon aui-icon aui-icon-small aui-iconfont-edit"></div><div class="favourite-space-icon "><button title="收藏空间" id="space-favourite-add" class="space-favourite aui-icon aui-icon-small aui-iconfont-unstar"></button><button class=" space-favourite aui-icon aui-icon-small aui-iconfont-star" id="space-favourite-remove" title="取消收藏"></button></div></div></div><div class="acs-side-bar-content"><div class="acs-nav-wrapper"><div class="acs-nav" data-has-create-permission="true" data-quick-links-state="hide" data-page-tree-state="null" data-nav-type="page-tree"><div class="acs-nav-sections"></div></div></div><div class="ia-secondary-container tipsy-enabled" data-tree-type="page-tree"><div class="ia-secondary-header"><h5 class="ia-secondary-header-title page-tree"><span class="icon"></span><span class="label">页面树结构</span></h5></div><div class="ia-secondary-content">


<div class="plugin_pagetree conf-macro output-inline" data-hasbody="false" data-macro-name="pagetree">

        
        
    <ul class="plugin_pagetree_children_list plugin_pagetree_children_list_noleftspace">
        <div class="plugin_pagetree_children" id="children983056-0">
<ul class="plugin_pagetree_children_list" id="child_ul983056-0">
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus8782025-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan8782025-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8782025&amp;src=contextnavpagetreemode">产品发布</a>
        </span>
            </div>

        <div id="children8782025-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28610843-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28610843-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/UED?src=contextnavpagetreemode">UED</a>
        </span>
            </div>

        <div id="children28610843-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607659-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607659-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607659&amp;src=contextnavpagetreemode">业务域</a>
        </span>
            </div>

        <div id="children28607659-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132752553-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132752553-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752553&amp;src=contextnavpagetreemode">大前端</a>
        </span>
            </div>

        <div id="children132752553-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul132752553-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus186830983-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan186830983-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=186830983&amp;src=contextnavpagetreemode">大前端新人指引-2023</a>
        </span>
            </div>

        <div id="children186830983-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132752559-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132752559-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752559&amp;src=contextnavpagetreemode">大前端技术</a>
        </span>
            </div>

        <div id="children132752559-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132766049-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132766049-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132766049&amp;src=contextnavpagetreemode">学习进阶</a>
        </span>
            </div>

        <div id="children132766049-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132756286-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132756286-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132756286&amp;src=contextnavpagetreemode">⌘ 电签组</a>
        </span>
            </div>

        <div id="children132756286-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus128366868-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan128366868-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=128366868&amp;src=contextnavpagetreemode">公有云二组</a>
        </span>
            </div>

        <div id="children128366868-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132752562-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132752562-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752562&amp;src=contextnavpagetreemode">内部支撑组</a>
        </span>
            </div>

        <div id="children132752562-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus128374689-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan128374689-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=128374689&amp;src=contextnavpagetreemode">☁ 私有云组</a>
        </span>
            </div>

        <div id="children128374689-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus77398990-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan77398990-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=77398990&amp;src=contextnavpagetreemode">❤ 内部支撑组</a>
        </span>
            </div>

        <div id="children77398990-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132759957-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132759957-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132759957&amp;src=contextnavpagetreemode">FE-SaaS组</a>
        </span>
            </div>

        <div id="children132759957-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132764860-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132764860-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132764860&amp;src=contextnavpagetreemode">★行业组</a>
        </span>
            </div>

        <div id="children132764860-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus72778219-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan72778219-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=72778219&amp;src=contextnavpagetreemode">∮政务组</a>
        </span>
            </div>

        <div id="children72778219-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus132743625-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan132743625-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132743625&amp;src=contextnavpagetreemode">大前端技术委员会</a>
        </span>
            </div>

        <div id="children132743625-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus136991087-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan136991087-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=136991087&amp;src=contextnavpagetreemode">运维指南</a>
        </span>
            </div>

        <div id="children136991087-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155556906-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155556906-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155556906&amp;src=contextnavpagetreemode">大前端故障汇总</a>
        </span>
            </div>

        <div id="children155556906-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus184066807-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan184066807-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184066807&amp;src=contextnavpagetreemode">基础能力组</a>
        </span>
            </div>

        <div id="children184066807-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus184081815-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan184081815-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184081815&amp;src=contextnavpagetreemode">公有云plus</a>
        </span>
            </div>

        <div id="children184081815-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul184081815-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus210151697-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210151697-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210151697&amp;src=contextnavpagetreemode">公有云前端详设模板</a>
        </span>
            </div>

        <div id="children210151697-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199123641-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199123641-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199123641&amp;src=contextnavpagetreemode">公有云前端详设汇总</a>
        </span>
            </div>

        <div id="children199123641-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul199123641-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199127405-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199127405-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127405&amp;src=contextnavpagetreemode">公有云前端迭代</a>
        </span>
            </div>

        <div id="children199127405-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul199127405-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199127414-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199127414-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127414&amp;src=contextnavpagetreemode">e签盾组详设</a>
        </span>
            </div>

        <div id="children199127414-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199127407-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199127407-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127407&amp;src=contextnavpagetreemode">saas迭代详设</a>
        </span>
            </div>

        <div id="children199127407-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199127410-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199127410-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127410&amp;src=contextnavpagetreemode">基础能力组详设</a>
        </span>
            </div>

        <div id="children199127410-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul199127410-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199130820-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199130820-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/2024Q3?src=contextnavpagetreemode">2024Q3</a>
        </span>
            </div>

        <div id="children199130820-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus206913708-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan206913708-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/2024Q4?src=contextnavpagetreemode">2024Q4</a>
        </span>
            </div>

        <div id="children206913708-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus210157267-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210157267-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/2025Q1?src=contextnavpagetreemode">2025Q1</a>
        </span>
            </div>

        <div id="children210157267-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus214556729-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214556729-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/2025Q2?src=contextnavpagetreemode">2025Q2</a>
        </span>
            </div>

        <div id="children214556729-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus221511894-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221511894-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221511894&amp;src=contextnavpagetreemode">签署-2025Q3</a>
        </span>
            </div>

        <div id="children221511894-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul221511894-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221515177-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221515177&amp;src=contextnavpagetreemode">20250724签署迭代详设</a>
        </span>
            </div>

        <div id="children221515177-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span plugin_pagetree_current" id="childrenspan*********-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;src=contextnavpagetreemode">********签署迭代详设</a>
        </span>
            </div>

        <div id="children*********-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221519150-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221519150&amp;src=contextnavpagetreemode">签署印章支持根据底稿尺寸自适应变化大小前端详设</a>
        </span>
            </div>

        <div id="children221519150-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan203657615-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=203657615&amp;src=contextnavpagetreemode">签署模板历史迭代</a>
        </span>
            </div>

        <div id="children203657615-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199129496-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199129496-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199129496&amp;src=contextnavpagetreemode">实名用户组详设</a>
        </span>
            </div>

        <div id="children199129496-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus214549297-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214549297-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214549297&amp;src=contextnavpagetreemode">移动端组详设</a>
        </span>
            </div>

        <div id="children214549297-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199127403-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199127403-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127403&amp;src=contextnavpagetreemode">公有云前端项目</a>
        </span>
            </div>

        <div id="children199127403-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199123999-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199123999-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199123999&amp;src=contextnavpagetreemode">公有云前端发布计划汇总</a>
        </span>
            </div>

        <div id="children199123999-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199123643-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199123643-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199123643&amp;src=contextnavpagetreemode">其他资料</a>
        </span>
            </div>

        <div id="children199123643-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus203673391-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan203673391-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=203673391&amp;src=contextnavpagetreemode">通用技术方案</a>
        </span>
            </div>

        <div id="children203673391-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus128374770-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan128374770-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=128374770&amp;src=contextnavpagetreemode">回收站</a>
        </span>
            </div>

        <div id="children128374770-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan31916596-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=31916596&amp;src=contextnavpagetreemode">回顾</a>
        </span>
            </div>

        <div id="children31916596-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607662-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607662-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607662&amp;src=contextnavpagetreemode">架构</a>
        </span>
            </div>

        <div id="children28607662-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607665-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607665-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607665&amp;src=contextnavpagetreemode">测试</a>
        </span>
            </div>

        <div id="children28607665-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus28607650-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan28607650-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=28607650&amp;src=contextnavpagetreemode">规范</a>
        </span>
            </div>

        <div id="children28607650-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus49545565-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan49545565-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=49545565&amp;src=contextnavpagetreemode">安全</a>
        </span>
            </div>

        <div id="children49545565-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan76448355-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=76448355&amp;src=contextnavpagetreemode">会议纪要</a>
        </span>
            </div>

        <div id="children76448355-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus78839942-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan78839942-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=78839942&amp;src=contextnavpagetreemode">流程规范</a>
        </span>
            </div>

        <div id="children78839942-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus95584345-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan95584345-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=95584345&amp;src=contextnavpagetreemode">技术保障</a>
        </span>
            </div>

        <div id="children95584345-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113880344-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113880344-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113880344&amp;src=contextnavpagetreemode">技术知识库</a>
        </span>
            </div>

        <div id="children113880344-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan136984739-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=136984739&amp;src=contextnavpagetreemode">1迭代记录</a>
        </span>
            </div>

        <div id="children136984739-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145142028-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145142028-0">                        <a href="http://wiki.timevale.cn:8081/display/PRODUCT/PMO?src=contextnavpagetreemode">PMO</a>
        </span>
            </div>

        <div id="children145142028-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155561693-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155561693-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155561693&amp;src=contextnavpagetreemode">技术委员会</a>
        </span>
            </div>

        <div id="children155561693-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus155563455-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155563455-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155563455&amp;src=contextnavpagetreemode">管理机制沉淀</a>
        </span>
            </div>

        <div id="children155563455-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan155564343-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=155564343&amp;src=contextnavpagetreemode">研发中心&amp;技术委员会双周会</a>
        </span>
            </div>

        <div id="children155564343-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus169195421-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan169195421-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=169195421&amp;src=contextnavpagetreemode">PBG项目积分制</a>
        </span>
            </div>

        <div id="children169195421-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan186842522-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=186842522&amp;src=contextnavpagetreemode">编译器</a>
        </span>
            </div>

        <div id="children186842522-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan190241004-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=190241004&amp;src=contextnavpagetreemode">签署页接口安全</a>
        </span>
            </div>

        <div id="children190241004-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus206909778-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan206909778-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=206909778&amp;src=contextnavpagetreemode">CMMI文档</a>
        </span>
            </div>

        <div id="children206909778-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
    </ul>
</div>
    </ul>

    <fieldset class="hidden">
        <input type="hidden" name="treeId" value="">
        <input type="hidden" name="treeRequestId" value="/plugins/pagetree/naturalchildren.action?decorator=none&amp;excerpt=false&amp;sort=position&amp;reverse=false&amp;disableLinks=false&amp;expandCurrent=true">
        <input type="hidden" name="treePageId" value="*********">

        <input type="hidden" name="noRoot" value="false">
        <input type="hidden" name="rootPageId" value="983056">

        <input type="hidden" name="rootPage" value="">
        <input type="hidden" name="startDepth" value="0">
        <input type="hidden" name="spaceKey" value="PRODUCT">

        <input type="hidden" name="i18n-pagetree.loading" value="载入中...">
        <input type="hidden" name="i18n-pagetree.error.permission" value="无法载入页面树。看来你没有权限查看根页面。">
        <input type="hidden" name="i18n-pagetree.eeror.general" value="检索页面树时发生问题。有关详细信息，请检查服务器的日志文件。">
        <input type="hidden" name="loginUrl" value="/login.action?os_destination=%2Fpages%2Fviewpage.action%3FpageId%3D*********&amp;permissionViolation=true">
        <input type="hidden" name="mobile" value="false">

                <fieldset class="hidden">
                                                <input type="hidden" name="ancestorId" value="221511894">
                                    <input type="hidden" name="ancestorId" value="199127410">
                                    <input type="hidden" name="ancestorId" value="199127405">
                                    <input type="hidden" name="ancestorId" value="199123641">
                                    <input type="hidden" name="ancestorId" value="184081815">
                                    <input type="hidden" name="ancestorId" value="132752553">
                                    <input type="hidden" name="ancestorId" value="983056">
                                    </fieldset>
    </fieldset>
</div>
</div></div></div><div class="hidden"><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT&amp;src=sidebar" id="space-pages-link" title=" (g ，s)"></a><script type="text/x-template" title="logo-config-content"><h2>空间详情</h2><div class="personal-space-logo-hint">您的个人头像被作为您的个人空间的logo使用。<a href="/users/profile/editmyprofilepicture.action" target="_blank">更改您的个人头像</a>.</div></script></div></div><div class="space-tools-section"><div id="space-tools-menu-additional-items" class="hidden"><div data-label="重排页面" data-class="" data-href="/pages/reorderpages.action?key=PRODUCT">重排页面</div></div><button id="space-tools-menu-trigger" class=" aui-dropdown2-trigger aui-button aui-button-subtle tipsy-enabled aui-dropdown2-trigger-arrowless " aria-controls="space-tools-menu" aria-haspopup="true" role="button" data-aui-trigger="" resolved="" aria-expanded="false" data-collapsed-tooltip="空间管理"><span class="aui-icon aui-icon-small aui-iconfont-configure">设置</span><span class="aui-button-label">空间管理</span><span class="icon aui-icon-dropdown"></span></button><div id="space-tools-menu" class="aui-dropdown2 aui-style-default space-tools-dropdown aui-layer" role="menu" aria-hidden="true" data-aui-alignment="top left" resolved=""><div role="presentation" class="aui-dropdown2-section space-tools-navigation"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/spaces/viewspacesummary.action?key=PRODUCT&amp;src=spacetools">概览</a></li><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;src=spacetools">内容工具</a></li></ul></div></div><div role="presentation" class="aui-dropdown2-section space-operations"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;src=spacetools">重排页面</a></li></ul></div></div></div><a class="expand-collapse-trigger aui-icon aui-icon-small aui-iconfont-chevron-double-left" data-tooltip="收起侧边栏 ( [ )"></a></div>
                    
                        			<div class="ia-splitter-handle tipsy-enabled" data-tooltip="收起侧边栏 ( [ )" title=" ([)"><div class="ia-splitter-handle-highlight confluence-icon-grab-handle"></div></div></div>
    		</div>
        <!-- \#header -->

            
    
        <div id="main" class=" aui-page-panel" style="margin-left: 393px;">
                        <div id="main-header" style="top: 40px; width: 1447px;" class="">
                        
    <div id="navigation" class="content-navigation view">
                    <ul class="ajs-menu-bar">
                                                                    <li class="ajs-button normal">

        
        
                                            
    
    
    <a id="editPageLink" href="http://wiki.timevale.cn:8081/pages/editpage.action?pageId=*********" rel="nofollow" class="aui-button aui-button-subtle edit" accesskey="e" title="编辑 (e)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-edit"></span>
                        <u>E</u>编辑
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="page-favourite" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle action-page-favourite" accesskey="f" title="收藏 (f)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-unstar"></span>
                        <u>F</u>收藏
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="watch-content-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle watch-menu watch-state-initialised" title="停止关注 (w)">
                <span><span class="aui-icon aui-icon-small aui-iconfont-watch"></span> <u>W</u>关注中</span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="shareContentLink" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle share" title="Share this page with others (s或 k)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-share"></span>
                        <u>S</u>分享
        </span>    </a>
</li>
                    
        <li class="normal ajs-menu-item">
        <a id="action-menu-link" class="action aui-dropdown2-trigger-arrowless aui-button aui-button-subtle ajs-menu-title aui-dropdown2-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" data-container="#navigation" resolved="" aria-controls="action-menu" aria-expanded="false">
            <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-more"></span>
                                
            </span>
        </a>         <div id="action-menu" class="aui-dropdown2 aui-style-default aui-layer most-right-menu-item" aria-hidden="true" resolved="">
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-primary" class="section-primary first">
                                                    <li>

    
        
                                            
    
    
    <a id="view-attachments-link" href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********" rel="nofollow" class="action-view-attachments" accesskey="t" title="查看附件 (t)">
                <span>
                        <u>t</u>附件(84)
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-history-link" href="http://wiki.timevale.cn:8081/pages/viewpreviousversions.action?pageId=*********" rel="nofollow" class="action-view-history" title="">
                <span>
                        页面历史
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-page-permissions-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-page-permissions" title="编辑限制">
                <span>
                        限制
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-secondary" class="section-secondary">
                                                    <li>

    
        
                                            
    
    
    <a id="view-page-info-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-view-info" title="">
                <span>
                        页面信息
        </span>    </a>
</li>
                                                <li>

    
            
                                            
    
    
    <a id="view-resolved-comments" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="" title="">
                <span>已解决评论 (0)</span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="view-in-hierarchy-link" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=PRODUCT&amp;openId=*********#selectedPageInHierarchy" rel="nofollow" class="" title="">
                <span>
                        以层级方式查看
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-source-link" href="http://wiki.timevale.cn:8081/plugins/viewsource/viewpagesrc.action?pageId=*********" rel="nofollow" class="action-view-source popup-link" title="">
                <span>
                        查看页面源代码
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-pdf-link" href="http://wiki.timevale.cn:8081/spaces/flyingpdf/pdfpageexport.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        导出为PDF
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="com-k15t-confluence-scroll-html-launcher" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        Export to HTML
        </span>    <section role="dialog" id="k15t-exp-html-no-export-dialog" class="aui-layer aui-dialog2 aui-dialog2-small aui-dialog2-warning" aria-hidden="true">    <header class="aui-dialog2-header">        <h2 class="aui-dialog2-header-main">Page Not Available</h2>        <a class="aui-dialog2-header-close">            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog">Close</span>        </a>    </header>    <div class="aui-dialog2-content">        <p>You cannot export this page, because it is not available in the current version, variant, or language.</p>    </div>    <footer class="aui-dialog2-footer">        <div class="aui-dialog2-footer-actions">            <button id="k15t-exp-html-no-export-dialog-close-button" class="aui-button aui-button-link">Close</button>        </div>        <div class="aui-dialog2-footer-hint">           <span style="  background: linear-gradient(to right, #1062fb 0, #1062fb 33.3%, #2eb785 33.3%, #2eb785 66.6%, #ffc420 66.6%);  height: .5em;  width: 2.625em;  display: inline-block;  position: relative;           "></span>         </div>    </footer></section></a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-word-link" href="http://wiki.timevale.cn:8081/exportword?pageId=*********" rel="nofollow" class="action-export-word" title="">
                <span>
                        导出为Word
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="import-word-doc" href="http://wiki.timevale.cn:8081/pages/worddav/uploadimport.action?pageId=*********" rel="nofollow" class="" title="">
                <span>
                        Doc文件导入
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-modify" class="section-modify">
                                                    <li>

    
        
                                            
    
    
    <a id="action-copy-page-link" href="http://wiki.timevale.cn:8081/pages/copypage.action?idOfPageToCopy=*********&amp;spaceKey=PRODUCT" rel="nofollow" class="action-copy" title="">
                <span>
                        复制
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-move-page-dialog-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="action-move" title="">
                <span>
                        移动
        </span>    </a>
</li>
                                        </ul>
                </div>
                    </div>
    </li>
            </ul>
    </div>

            
            <div id="title-heading" class="pagetitle with-breadcrumbs">
                
                                    <div id="breadcrumb-section">
                        
    
    
    <ol id="breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT&amp;src=breadcrumbs-collector">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056&amp;src=breadcrumbs-expanded">技术中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752553&amp;src=breadcrumbs-expanded">大前端</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184081815&amp;src=breadcrumbs-expanded">公有云plus</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199123641&amp;src=breadcrumbs-expanded">公有云前端详设汇总</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127405&amp;src=breadcrumbs-expanded">公有云前端迭代</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127410&amp;src=breadcrumbs-expanded">基础能力组详设</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221511894&amp;src=breadcrumbs-parent">签署-2025Q3</a></span>
                                                                    </li></ol>


                    </div>
                
                
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-end" class="assistive">跳到banner的尾部</a>
<div id="page-banner-start" class="assistive"></div>

                    
            <div id="page-metadata-banner" style="visibility: visible;"><ul class="banner"><li id="system-content-items" class="noprint"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="未限制" id="content-metadata-page-restrictions" class="aui-icon aui-icon-small aui-iconfont-unlocked system-metadata-restrictions" resolved=""></a><a href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********&amp;metadataLink=true" title="84 个附件" id="content-metadata-attachments" class="aui-icon aui-icon-small aui-iconfont-attachment"></a></li><li class="page-metadata-item noprinthas-button" id="content-metadata-jira-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="" id="content-metadata-jira" class="aui-button aui-button-subtle content-metadata-jira tipsy-disabled hidden"><span>JIRA 链接</span></a></li></ul></div>
            

<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-start" class="assistive">回到标题开始</a>
<div id="page-banner-end" class="assistive"></div>
    

                <h1 id="title-text" class="with-breadcrumbs" style="display: block;">
                                                <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">********签署迭代详设</a>
                                    </h1>
            </div>
        </div><!-- \#main-header -->
        
        

        <div id="sidebar-container">
                                                </div><!-- \#sidebar-container -->

        
    

        




            
    

                                
    

    
    
        
    
    
                    
    

    

    
            
        

    
    

    
            
        



    
<div id="content" class="page view">
    


<div id="action-messages">
                        </div>



            <script type="text/x-template" title="searchResultsGrid">
    <table class="aui">
        <thead>
            <tr class="header">
                <th class="search-result-title">页面标题</th>
                <th class="search-result-space">空间</th>
                <th class="search-result-date">更新于</th>
            </tr>
        </thead>
    </table>
</script>
<script type="text/x-template" title="searchResultsGridCount">
    <p class="search-result-count">{0}</p>
</script>
<script type="text/x-template" title="searchResultsGridRow">
    <tr class="search-result">
        <td class="search-result-title"><a href="{1}" class="content-type-{2}"><span>{0}</span></a></td>
        <td class="search-result-space"><a class="space" href="/display/{4}/" title="{3}">{3}</a></td>
        <td class="search-result-date"><span class="date" title="{6}">{5}</span></td>
    </tr>
</script>
        
    
            

        
                            
    

                    

        
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-end" class="assistive">转至元数据结尾</a>
<div id="page-metadata-start" class="assistive"></div>

    <div class="page-metadata">
        <ul>
            <li class="page-metadata-modification-info">
                
        
    
        
    
        
            
            由<span class="author">     <a href="http://wiki.timevale.cn:8081/display/~guanyi" class="url fn confluence-userlink userlink-0" data-username="guanyi" title="" data-user-hover-bound="true">观弈</a></span>创建, 最后修改于<a class="last-modified" title="查看变更" href="http://wiki.timevale.cn:8081/pages/diffpagesbyversion.action?pageId=*********&amp;selectedPageVersions=35&amp;selectedPageVersions=36">大约3小时以前</a>
                </li>
        </ul>
    </div>


<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-start" class="assistive">转至元数据起始</a>
<div id="page-metadata-end" class="assistive"></div>

        
                                            
        <div id="main-content" class="wiki-content">
                           
        <h1 id="id-********签署迭代详设-"><div class="toc-macro client-side-toc-macro  conf-macro output-block hidden-outline" data-headerelements="H1,H2,H3,H4,H5,H6,H7" data-hasbody="false" data-macro-name="toc"><ul style=""><li><span class="toc-item-body" data-outline="1"><span class="toc-outline">1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-" class="toc-link"> </a></span></li><li><span class="toc-item-body" data-outline="2"><span class="toc-outline">2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-%E4%B8%80%E3%80%81%E5%90%8D%E7%A7%B0" class="toc-link">一、名称</a></span></li><li><span class="toc-item-body" data-outline="3"><span class="toc-outline">3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-%E4%BA%8C%E3%80%81%E7%9B%AE%E6%A0%87" class="toc-link">二、目标</a></span></li><li><span class="toc-item-body" data-outline="4"><span class="toc-outline">4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-%E4%B8%89%E3%80%81%E8%AF%A6%E8%AE%BE%E5%89%8D%E9%9C%80%E7%A1%AE%E8%AE%A4%E7%9A%84%E7%82%B9" class="toc-link">三、详设前需确认的点</a></span></li><li><span class="toc-item-body" data-outline="5"><span class="toc-outline">5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-%E5%9B%9B%E3%80%81%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88" class="toc-link">四、技术方案</a></span><ul style=""><li><span class="toc-item-body" data-outline="5.1"><span class="toc-outline">5.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.1%E6%B3%95%E5%AE%9A%E4%BB%A3%E8%A1%A8%E4%BA%BA%E6%8E%88%E6%9D%83%E6%B5%81%E7%A8%8B%E4%BC%98%E5%8C%96%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E8%A7%82%E5%BC%88%EF%BC%89" class="toc-link">4.1&nbsp; 法定代表人授权流程优化— 开发人员（观弈）</a></span></li><li><span class="toc-item-body" data-outline="5.2"><span class="toc-outline">5.2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.2%E5%BC%BA%E5%88%B6%E9%98%85%E8%AF%BB%E5%88%B0%E5%BA%95%E4%BA%8C%E6%AC%A1%E7%A1%AE%E8%AE%A4%E6%96%87%E6%A1%88%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E8%A7%82%E5%BC%88%EF%BC%89" class="toc-link">4.2&nbsp; 强制阅读到底二次确认文案— 开发人员（观弈）</a></span></li><li><span class="toc-item-body" data-outline="5.3"><span class="toc-outline">5.3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.3%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6%E9%98%85%E8%AF%BB%E6%97%B6%E9%95%BF%E6%8E%A7%E5%88%B6%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E8%A7%82%E5%BC%88%EF%BC%89" class="toc-link">4.3&nbsp; 合同文件阅读时长控制— 开发人员（观弈）</a></span></li><li><span class="toc-item-body" data-outline="5.4"><span class="toc-outline">5.4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.4bugfix-.title%E6%8A%A5%E9%94%99%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E8%A7%82%E5%BC%88%EF%BC%89" class="toc-link">4.4&nbsp; bugfix-.title报错— 开发人员（观弈）</a></span></li><li><span class="toc-item-body" data-outline="5.5"><span class="toc-outline">5.5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.5%E5%8D%B0%E7%AB%A0%E6%8E%88%E6%9D%83bugfix-%E5%8D%B0%E7%AB%A0%E4%BD%BF%E7%94%A8%E5%91%98%E4%BC%A0%E4%BA%86%E4%BB%85%E5%AE%A1%E6%89%B9%E4%B8%8D%E7%94%A8%E5%8D%B0%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E9%95%BF%E6%AD%8C%EF%BC%89" class="toc-link">4.5&nbsp;印章授权bugfix- 印章使用员传了仅审批不用印 — 开发人员（长歌）</a></span></li><li><span class="toc-item-body" data-outline="5.6"><span class="toc-outline">5.6</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.6saas%E5%8D%B0%E7%AB%A0%E5%9B%BE%E7%89%87%E4%B8%8A%E4%BC%A0%E5%8E%9F%E5%9B%BE%E6%88%AA%E5%9B%BE%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E9%95%BF%E6%AD%8C%EF%BC%89" class="toc-link">4.6&nbsp; saas印章图片上传原图截图— 开发人员（长歌）</a></span></li><li><span class="toc-item-body" data-outline="5.7"><span class="toc-outline">5.7</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.7%E6%89%B9%E9%87%8F%E7%AD%BE%E6%8E%A5%E5%8F%A3%E6%94%AF%E6%8C%81%E5%8F%AF%E4%B8%8D%E4%BC%A0%E5%85%A5%E5%A7%93%E5%90%8D%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E5%B0%8F%E9%BA%A6%EF%BC%89" class="toc-link">4.7&nbsp;批量签接口支持可不传入姓名 — 开发人员（小麦）</a></span></li><li><span class="toc-item-body" data-outline="5.8"><span class="toc-outline">5.8</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-4.8SaaS%E5%8D%B0%E7%AB%A0%E4%BD%BF%E7%94%A8%E6%83%85%E5%86%B5%E5%9F%8B%E7%82%B9%E2%80%94%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%EF%BC%88%E5%B0%8F%E9%BA%A6%EF%BC%89" class="toc-link">4.8&nbsp; SaaS印章使用情况埋点— 开发人员（小麦）</a></span></li></ul></li><li><span class="toc-item-body" data-outline="6"><span class="toc-outline">6</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-********%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3%E8%AF%A6%E8%AE%BE-%E4%BA%94.%E5%85%B6%E4%BB%96%E7%A1%AE%E8%AE%A4%E7%82%B9" class="toc-link">五.其他确认点</a></span></li></ul></div></h1><h1 id="id-********签署迭代详设-一、名称">一、名称</h1><p style="margin-left: 30.0px;">签署0807迭代</p><h1 id="id-********签署迭代详设-二、目标">二、目标</h1><ol><li><p>法定代表人授权流程优化-观弈</p></li><li>强制阅读到底二次确认文案-观弈</li><li>合同文件阅读时长控制 - 观弈&nbsp; &nbsp;--（延后）</li><li><p>bugfix-.title报错 - 观弈</p></li><li>印章授权bugfix- 印章使用员传了仅审批不用印 - 长歌</li><li>saas印章图片上传原图截图 - 长歌</li><li>&nbsp;批量签接口支持可不传入姓名 - 小麦</li><li><p>SaaS印章使用情况埋点 - 小麦</p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 836.85px; letter-spacing: 0px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 136.288px;"><col style="width: 405.812px;"><col style="width: 293.95px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 473px; z-index: 3; width: 836px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="标题: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>标题</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="文档: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>文档</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="负责人: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>负责人</p></div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="标题: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>标题</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="文档: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>文档</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="负责人: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>负责人</p></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">需求文档</td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(165,173,186);">xxx</span></p></td></tr><tr role="row"><td class="confluenceTd">设计稿</td><td class="confluenceTd"><p><br></p></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(165,173,186);">xxx</span></p></td></tr><tr role="row"><td class="confluenceTd">后端详设</td><td class="confluenceTd"><p><br></p></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">xxx</span></td></tr><tr role="row"><td colspan="1" class="confluenceTd">冒烟用例</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">xxx</span></td></tr></tbody></table></div></li></ol><h1 id="id-********签署迭代详设-三、详设前需确认的点"><span style="color: rgb(38,38,38);">三、详设前需确认的点</span></h1><ol><li>需求已明确，对于不明确的需求，需要提前与产品对齐，对于【一句话需求】，一定要拆分并明确功能点，可以先将功能点梳理出来再与产品对齐</li><li>UI稿和交互已明确，对于不明确的点，或者有疑惑的交互，需要提前与UI对齐，特别是一些边界值，比如过长的文案是换行还是以...结尾，页面的最大宽度、最小宽度，弹窗的最大高度等</li><li>UI稿都是按最新的edw来画的，确认本次改动是否需要升级组件，还是基于现有elementUI来实现功能，如果是在现有基础上做，确保产品和设计同学充分了解现状，特别注意：尤其是产品说不出UI稿或者前置UI没有介入的需求，也需要对齐拉通，避免后期二次调整</li></ol><h1 id="id-********签署迭代详设-四、技术方案"><span style="color: rgb(38,38,38);">四、技术方案</span></h1><h2 id="id-********签署迭代详设-4.1法定代表人授权流程优化—开发人员（观弈）">4.1&nbsp; 法定代表人授权流程优化— 开发人员（观弈）</h2><p>应用名：<span style="color: rgb(48,49,51);">saas-microfe-seal-manage-front</span></p><p>设计稿：<a href="https://www.figma.com/design/zGUMz97x6fKN2zfCkTioNr/SaaS%E8%BF%AD%E4%BB%A3?node-id=12455-11353&amp;p=f&amp;m=dev" class="external-link" rel="nofollow">https://www.figma.com/design/zGUMz97x6fKN2zfCkTioNr/SaaS%E8%BF%AD%E4%BB%A3?node-id=12455-11353&amp;p=f&amp;m=dev</a></p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 100.0%;" resolved=""><colgroup><col style="width: 0.182151%;"><col style="width: 2.75023%;"><col style="width: 93.8606%;"><col style="width: 3.19842%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><p><strong>需求</strong></p><p>0710迭代针对上传法人授权书的流程进行了优化，本期迭代需要将整个法人授权流程进行优化</p><p><strong>交互</strong></p><p>1 表单验证报错提示，由原本的弹窗加输入框底部提示转变为全部为输入框底部提示，同时需要保证，如果输入的信息全部有误，则需要一条条提示，而非一下子全部提示出来</p><p>2 <span>个人印章&amp;证书授权书 点击后唤起pdf预览组件，而非原生的浏览器打开新页面，避免用户进行下载</span></p><p><span>3 法定代表人证件号不一致时新增弹窗，引导用户上传纸质版。</span></p><p><span>4 等待页面如果是纸质版的情况下，点击查看材料 新增弹窗，弹窗可以进行预览pdf和图片，下方为身份证的预览，点击可以进入放大，pdf点击进入pdf预览组件</span></p><p><br></p></td><td class="confluenceTd"><div class="content-wrapper"><p><strong>现状</strong></p><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label=": No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><br></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="当前: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">当前</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="设计稿: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">设计稿</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修改点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">修改点</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">线上授权</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="244" src="./********签署迭代前端详设_files/image2025-7-22_19-1-25.png" data-image-src="/download/attachments/*********/image2025-7-22_19-1-25.png?version=1&amp;modificationDate=1753182085000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521464" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-1-25.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="279" src="./********签署迭代前端详设_files/image2025-7-22_19-0-38.png" data-image-src="/download/attachments/*********/image2025-7-22_19-0-38.png?version=1&amp;modificationDate=1753182038000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521462" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-0-38.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td colspan="1" class="confluenceTd"><p>1 步骤条又两个边为三个</p><p>2 tips提示颜色变化，文本变化，此外点击查看授权书从原生行为边为pdf预览</p><p>3 表单整体变宽，按钮也变宽</p><p><br></p><p><br></p></td></tr><tr role="row"><td class="confluenceTd">表单验证</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="242" src="./********签署迭代前端详设_files/image2025-7-22_19-3-7.png" data-image-src="/download/attachments/*********/image2025-7-22_19-3-7.png?version=1&amp;modificationDate=1753182188000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521465" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-3-7.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="229" src="./********签署迭代前端详设_files/image2025-7-22_19-3-22.png" data-image-src="/download/attachments/*********/image2025-7-22_19-3-22.png?version=1&amp;modificationDate=1753182203000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521466" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-3-22.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td colspan="1" class="confluenceTd"><p>1 表单验证方式 由顶部message形式边为输入框底部展示</p><p>2 error文本变化</p><p>3 验证方式，由全部一下子验证边为，<span style="letter-spacing: 0.0px;">先验证身</span><span style="letter-spacing: 0.0px;">份证号是否存在</span><span style="letter-spacing: 0.0px;">再</span><span style="letter-spacing: 0.0px;">验证手机号是否存在</span><span style="letter-spacing: 0.0px;">验证身份</span><span style="letter-spacing: 0.0px;">证号是否与实际信</span><span style="letter-spacing: 0.0px;">息一致（服务器验</span><span style="letter-spacing: 0.0px;">证）</span><span style="letter-spacing: 0.0px;">最后验证手机</span><span style="letter-spacing: 0.0px;">号格式是否正确，是否一致</span></p><p><span style="letter-spacing: 0.0px;">4 证件号不一致的时候，出现的文本点击后可以引导用户进行纸质版授权</span></p></td></tr><tr role="row"><td class="confluenceTd">线上授权</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="272" src="./********签署迭代前端详设_files/image2025-7-22_19-7-3.png" data-image-src="/download/attachments/*********/image2025-7-22_19-7-3.png?version=1&amp;modificationDate=1753182424000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521467" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-7-3.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="./********签署迭代前端详设_files/image2025-7-22_19-7-20.png" data-image-src="/download/attachments/*********/image2025-7-22_19-7-20.png?version=1&amp;modificationDate=1753182440000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521468" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-7-20.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td colspan="1" class="confluenceTd"><p>1 顶部步骤条由2变3</p><p>2 表单整体左对齐，变宽</p><p>3 底部上传授权书左对齐</p><p>4 表单验证规则新增对手机号的验证</p></td></tr><tr role="row"><td class="confluenceTd">授权等待与结果页</td><td class="confluenceTd"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="212" src="./********签署迭代前端详设_files/image2025-7-22_19-9-26.png" data-image-src="/download/attachments/*********/image2025-7-22_19-9-26.png?version=1&amp;modificationDate=1753182566000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521471" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-9-26.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="237" src="./********签署迭代前端详设_files/image2025-7-22_19-10-33.png" data-image-src="/download/attachments/*********/image2025-7-22_19-10-33.png?version=1&amp;modificationDate=1753182633000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521474" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-10-33.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><br></p></td><td class="confluenceTd"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="220" src="./********签署迭代前端详设_files/image2025-7-22_19-9-35.png" data-image-src="/download/attachments/*********/image2025-7-22_19-9-35.png?version=1&amp;modificationDate=1753182575000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521473" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-9-35.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="303" src="./********签署迭代前端详设_files/image2025-7-22_19-10-46.png" data-image-src="/download/attachments/*********/image2025-7-22_19-10-46.png?version=1&amp;modificationDate=1753182647000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521477" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-10-46.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="378" src="./********签署迭代前端详设_files/image2025-7-22_19-12-1.png" data-image-src="/download/attachments/*********/image2025-7-22_19-12-1.png?version=1&amp;modificationDate=1753182721000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521478" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-12-1.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p></td><td colspan="1" class="confluenceTd"><p>1 顶部步骤条</p><p>2 tips移除</p><p>3 icon变化</p><p>4 文本变化</p><p>5 底部按钮间距变化</p><p>6 个人印章文字点击后由原本浏览器的默认打开，转变为pdf的预览行为</p><p><br></p><p><br></p><p>7 线上授权除上述外，新增查看材料，点击查看材料出现弹窗</p><p>8 审核失败与上述变化一致</p></td></tr><tr role="row"><td class="confluenceTd">纸质版授权查看材料按钮弹窗</td><td class="confluenceTd">新增</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-22_19-12-30.png" data-image-src="/download/attachments/*********/image2025-7-22_19-12-30.png?version=1&amp;modificationDate=1753182750000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521480" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-22_19-12-30.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></td><td colspan="1" class="confluenceTd"><p>1 新增弹窗，顶部为pdf预览或者图片缩略图，点击后如果是pdf则唤起pdf的预览组件，</p><p>2 底部为身份证正反缩略图，点击后唤起图片预览</p></td></tr></tbody></table></div><p><strong><br></strong></p><p><strong>相关入口</strong></p><p><strong>sass首页--印章---申请法人印章--在线授权/纸质版授权---授权完成进入等待页面</strong></p><p><strong>修改文件</strong></p><p><strong>src\views\Seal\LegalAuth\Paper.vue 上传纸质版</strong></p><p><strong>src\views\Seal\LegalAuth\Online.vue 在线授权</strong></p><p><strong>src\views\Seal\components\TopTips.vue 顶部tips提示</strong></p><p><strong>src\views\Seal\components\Steps.vue 顶部步骤条</strong></p><p><strong>src\views\Seal\LegalAuth\components\LegalRepr.vue 表单组件</strong></p><p><strong>src\views\Seal\LegalAuth\Result.vue 等待结果页</strong></p><p><strong>新增：</strong></p><p><strong>src\views\Seal\LegalAuth\Dialog.vue 弹窗，展示授权书预览与身份证预览<br></strong></p><p><strong>涉及接口</strong></p><p><strong>后端详设地址：</strong></p><p><br></p><p><strong>关键逻辑</strong></p><p><strong><strong>1.&nbsp;src\views\Seal\LegalAuth\Online.vue 在线授权</strong></strong></p><p><strong><strong>在现授权改动点主要在于其中引入的表单组件，当前父子组件数据使用混乱，打破了单向数据流，组件责任划分不明确，针对这两个组件，需要修改的地方比较多，在不改变原有逻辑的情况下对结构和部分逻辑进行调整</strong></strong></p><p><strong><strong>首先移除底部的tips</strong></strong></p><p><strong><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="231" src="./********签署迭代前端详设_files/image2025-7-23_11-18-28.png" data-image-src="/download/attachments/*********/image2025-7-23_11-18-28.png?version=1&amp;modificationDate=1753240708000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521729" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-18-28.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></strong></strong></p><p><strong><strong>其次，法定代表人手机号这个输入框写在了onLine这个组件内，预测之前的做法是因为纸质版授权并不需要手机号输入框，所以写在了onLine这个组件内，但我认为既然都归属于表单组件，不妨都写在表单组件内部，责任单一化，在表单组件内部判断是线上授权还是纸质授权。</strong></strong></p><p><strong><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-23_11-20-36.png" data-image-src="/download/attachments/*********/image2025-7-23_11-20-36.png?version=1&amp;modificationDate=1753240835000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521731" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-20-36.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></strong></strong></p><p><strong>在其次，新增pdf预览组件和授权信息不一致时，查看帮助的弹窗组件的使用。</strong></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-23_11-22-45.png" data-image-src="/download/attachments/*********/image2025-7-23_11-22-45.png?version=1&amp;modificationDate=1753240964000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521732" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-22-45.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="53" src="./********签署迭代前端详设_files/image2025-7-23_11-23-10.png" data-image-src="/download/attachments/*********/image2025-7-23_11-23-10.png?version=1&amp;modificationDate=1753240990000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521733" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_11-23-10.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></strong></p><p><strong>最后，表单验证规则。</strong></p><p><strong>先验证身份证号是否为空---在验证手机号是否为空---在验证身份证号是否一致—在验证手机号是否一致，该错误提示是一个个展示出来，并非全部一下子展示出来，并且是在用户点击了一次提交之后才会显示</strong></p><p><strong>如果用户什么都没填写，点到输入框再失焦或者输入内容再失焦，是不会触发表单验证的。</strong></p><p><strong>所以对表单提交时候的验证规则进行了顺序调整</strong></p><p><strong>当前为，如果什么都没填点击提交会以顶部message消息通知的形式保存，如果聚焦输入框再移除，什么也不填则底部给出提示文字。如果身份证号码和手机号都填写，则先回校验手机号是否正确，在校验身份证号是否正确。</strong></p><p><strong>设计修改的表单提交的验证方法</strong></p><p><strong>ps:<span style="color: rgb(255,0,0);">需要后端支持法定代表人手机号一致性校验接口</span></strong></p><p><strong><span> </span><span class="nx">async</span><span> </span><span class="nx">onSendRequestClick</span></strong></p><p><strong><strong>2. src\views\Seal\LegalAuth\Paper.vue 上传纸质版</strong></strong></p><p><strong><strong>纸质版上传除样式外，新增了证件号格式的正则校验匹配，移除原本的一致性校验</strong></strong></p><p><strong><strong><br></strong></strong></p><p><strong><strong>3. <strong>src\views\Seal\LegalAuth\Result.vue 等待结果页</strong></strong></strong></p><p><strong><strong><strong>当前等待结果页移除原本顶部的tips提示，下方文案进行调整，文案中的个人印章&amp;法定授权书 点击后唤起pdf的预览组件，纸质版上传移除原本的文件下载的a标签，转而替换为一个查看材料的按钮，点击按钮后唤起弹窗</strong></strong></strong></p><p><strong><strong><strong><br></strong></strong></strong></p><p><strong><strong><strong>4.<strong>src\views\Seal\components\TopTips.vue 顶部tips提示</strong></strong></strong></strong></p><p><strong><strong><strong><strong>这里需要对原有的tips组件进行结构和功能上的修改，之前这里是传入的a标签，而a标签天然支持点击跳转的功能，但是这里功能变成了点击后唤起pdf的预览组件，所以之前的方式无法满足需要，而v-html又容易引发漏洞，于是将原本传入的一段文字拆成了三段，前后均为普通字符串，中间需要字符串配合外部传来的方法进行触发唤起预览组件（涉及范围：在线授权，纸质版收钱，结果等待页）</strong></strong></strong></strong></p><p><strong><strong><strong><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="176" src="./********签署迭代前端详设_files/image2025-7-23_10-6-20.png" data-image-src="/download/attachments/*********/image2025-7-23_10-6-20.png?version=1&amp;modificationDate=1753236379000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521646" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_10-6-20.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></strong></strong></strong></strong></p><p><strong><strong><strong><strong><br></strong></strong></strong></strong></p><p><strong><strong><strong><br></strong></strong></strong></p><p><strong><strong><strong>5.<strong>src\views\Seal\components\Steps.vue 顶部步骤条</strong></strong></strong></strong></p><p><strong><strong><strong><strong>顶部步骤条由原本的两条步骤，变成了三条步骤，此外，在线上授权和纸质版授权的步骤条的第一条步骤 文案不一样。所以需要新增属性isOnline来判断是否为线上授权，这样只需要改步骤条默认配置即可，如果采用传入整个steps，那么线上线下都需要写一个静态的三个步骤 数据作为配置项传入，不如新增一个布尔配置项在组件内部处理。</strong></strong></strong></strong></p><p><strong><strong><strong><strong>此外，样式上由于原本的步骤条只有当前激活的步骤与设计稿一致，已经走过的和没有走的在样式上存在一定差异，需要补全原本组件这一块样式上无法设置的问题</strong></strong></strong></strong></p><p><strong><strong><strong><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-23_10-54-46.png" data-image-src="/download/attachments/*********/image2025-7-23_10-54-46.png?version=1&amp;modificationDate=1753239286000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221521701" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-23_10-54-46.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></strong></strong></strong></strong></p><p><strong><strong><strong><br></strong></strong></strong></p><p><strong><strong><strong>6.<strong>src\views\Seal\LegalAuth\components\LegalRepr.vue 表单组件</strong></strong></strong></strong></p><p><strong><strong><strong><strong>除样式外 当信息齐全的时候，证件号类型不允许切换，禁用，<span>证件号齐全但未带有证件类型信息时，默认为护照。新增手机号校验是否一致。此外更改了校验的顺序，新增<span>checkLegalContact方法，表单校验逻辑部分在这里，主要是校验手机号和邮箱是否为空和格式是否正确的，并且如果证件号有误的情况下，应该是不校验手机号的</span></span></strong></strong></strong></strong></p><p><strong><strong><strong><strong><span><span>手机号不校验一致性</span></span></strong></strong></strong></strong></p><p><strong><strong><strong><strong><br></strong></strong></strong></strong></p><p><strong><strong><strong><strong><br></strong></strong></strong></strong></p><p><strong><strong><strong><strong>7.<strong>src\views\Seal\LegalAuth\Dialog.vue 弹窗，展示授权书预览与身份证预览</strong></strong></strong></strong></strong></p><p><strong>新增<strong><strong>Dialog弹窗，用于展示纸质版授权，点击查看材料的弹窗，这里上半部分为pdf/图片的缩略图，<span style="color: rgb(255,0,0);">其中pdf的缩略图希望后端在上传授权书的时候，在现有基础上，额外返还给我pdf首页的url和isPdf这个属性用来判断是否为pdf（不再需要后端支持，pdf的预览图改为固定的图片展示）</span>。点击唤起pdf或者图片的预览，下面的为身份证正反的预览。</strong></strong></strong></p><p><span style="color: rgb(255,0,0);"><strong><strong><strong>ps:pdf预览不需要后端支持，当前pdf返还的url在 SaasFilePreview这个组件可以直接使用，不需要转图片。只有pdf的缩略图需要支持</strong></strong></strong></span></p><p><span style="color: rgb(255,0,0);"><strong>pdf缩略图也不需要了，展示固定图片，那么这里的逻辑边为，如果是给的pdf的下载地址，那么image无法正确展示，展示错误的时候替换为固定的pdf图即可，点击调用pdf预览即可，如果传入的是图片，那么可以正常展示调用正常的图片预览</strong></span></p><p><strong><strong><strong>其中pdf预览：</strong></strong></strong></p><p>import { SaasFilePreview } from '@esign-saas-component/esign-design-saas'</p><p>图片预览为 edw的image组件</p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：</p><p>saas-申请法人章整个流程</p><p>回归范围：</p></td></tr></tbody></table></div><div id="floating-scrollbar" style="position: fixed; bottom: 0px; height: 30px; overflow: auto hidden; display: none; left: 433px; width: 1427px;"><div style="border: 1px solid rgb(255, 255, 255); opacity: 0.01; width: 1779px;"></div></div><h2 id="id-********签署迭代详设-4.2强制阅读到底二次确认文案—开发人员（观弈）">4.2&nbsp; 强制阅读到底二次确认文案— 开发人员（观弈）</h2><p>应用名：<a rel="nofollow" class="external-link" href="http://devops.timevale.cn/app/new/1768">tsign-openservice-pc-3-normal</a>、<a rel="nofollow" href="http://devops.timevale.cn/app/new/1764" class="external-link">tsign-openservice-h5-3-normal</a></p><p>设计稿：</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 69.6581%;" resolved=""><colgroup><col style="width: 23.2494%;"><col style="width: 18.9506%;"><col style="width: 39.8313%;"><col style="width: 17.9874%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p>当前这个二次确认只针对贝壳做了使用，现在要放到签署pc和h5中</p><p><strong>交互</strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="./********签署迭代前端详设_files/image2025-7-10_16-54-0.png" data-image-src="/download/attachments/*********/image2025-7-10_16-54-0.png?version=1&amp;modificationDate=1753258025000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221522203" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-10_16-54-0.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-10_16-54-15.png" data-image-src="/download/attachments/*********/image2025-7-10_16-54-15.png?version=1&amp;modificationDate=1753258025000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221522204" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-10_16-54-15.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p><br></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p><strong>现状</strong></p><p>点击提交签署就直接进入签署逻辑，增加如果配置了二次确认的appid，那么再提交前先触发二次确认的弹窗。</p><p><span style="color: rgb(122,134,154);"><br></span></p><p><strong>相关入口</strong></p><p>签署pc、h5：批量签+单流程，点击提交签署</p><p><strong>修改文件</strong></p><p>签署pc单流程：plugins\MainOperationExt\sign.ts</p><p>签署h5单流程：src\pages\sign-v4\Sign.tsx</p><p><strong>涉及接口</strong></p><p><strong><br></strong></p><p><strong>关键逻辑</strong></p><p><strong>当前触发弹窗是根据</strong><span style="color: rgb(239,176,128);">contextInfo</span>.<span style="color: rgb(170,155,245);">forceReadConfirm字段进行判断是否展示二次确认的弹窗，后续会在<span style="color: rgb(31,31,31);">getAppConfigByAppId这个接口返回新的判断逻辑forcedReadTwiceConfirm文本，优先级上，会先判断<span style="color: rgb(239,176,128);">contextInfo</span><span>.</span><span style="color: rgb(170,155,245);">forceReadConfirm，在判断<span style="color: rgb(31,31,31);">getAppConfigByAppId返回的<span style="color: rgb(31,31,31);">forcedReadTwiceConfirm文本字段控制，如果为空则不展示，如果存在文本，则展示提示的文本信息变为<span style="color: rgb(31,31,31);">forcedReadTwiceConfirm字段给的</span></span></span></span></span></span></p><p><span style="color: rgb(31,31,31);">PC：</span></p><p><s><span style="color: rgb(31,31,31);">批量签：</span></s></p><p><span style="color: rgb(31,31,31);">src\store\modules\customSignPrivate.ts中新增字段和配套的方法，<span style="color: rgb(31,31,31);">forcedReadTwiceConfirm，随后再src\mixins\customInit.js中再</span></span><span style="color: rgb(235,200,141);">getAppConfig方法中拿到<span style="color: rgb(31,31,31);">forcedReadTwiceConfirm，之后再判断即可</span></span></p><p><span style="color: rgb(235,200,141);"><span style="color: rgb(31,31,31);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="457" src="./********签署迭代前端详设_files/image2025-7-24_15-35-45.png" data-image-src="/download/attachments/*********/image2025-7-24_15-35-45.png?version=1&amp;modificationDate=1753342543000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221522997" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_15-35-45.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></span></p><p><span style="color: rgb(31,31,31);">单流程：</span></p><p><span style="color: rgb(31,31,31);">src\store\modules\signPrivate.ts 新增属性<span style="color: rgb(31,31,31);">forcedReadTwiceConfirm以及配套方法，再src\mixins\mainInit\signInit.js中</span></span><span style="color: rgb(239,176,128);">signPageInit方法中，<span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="619" src="./********签署迭代前端详设_files/image2025-7-24_15-59-16.png" data-image-src="/download/attachments/*********/image2025-7-24_15-59-16.png?version=1&amp;modificationDate=1753343954000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523034" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_15-59-16.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p><span style="color: rgb(31,31,31);">然后再sigin中进行字段判断</span></p><p><span style="color: rgb(31,31,31);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-24_16-0-17.png" data-image-src="/download/attachments/*********/image2025-7-24_16-0-17.png?version=1&amp;modificationDate=1753344015000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523035" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_16-0-17.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></p><p>h5：</p><p>单流程，src\pages\sign-v4\Sign.tsx 中混入了src\mixins\auth.ts，可以直接在<span>auth.ts中新增属性</span></p><p><span style="color: rgb(170,155,245);">forcedReadTwiceConfirm，并且再</span><span style="color: rgb(239,176,128);">getAppProperty中拿到appid返回的文本字段，再<span>Sign.tsx中直接使用即可</span></span></p><p><span style="color: rgb(239,176,128);"><span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-24_16-46-36.png" data-image-src="/download/attachments/*********/image2025-7-24_16-46-36.png?version=1&amp;modificationDate=1753346795000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523171" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_16-46-36.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></span></p><p><span style="color: rgb(239,176,128);"><span>同时需要更换展示字段</span></span></p><p><span style="color: rgb(239,176,128);"><span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="491" src="./********签署迭代前端详设_files/image2025-7-24_16-47-37.png" data-image-src="/download/attachments/*********/image2025-7-24_16-47-37.png?version=1&amp;modificationDate=1753346856000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523175" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_16-47-37.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></span></p><p><s>批量签：</s></p><p><s>src\pages\v3MultiSign\custom.tsx 中混入了src\mixins\customInit.ts，需要在<span>customInit.ts中新增属性<span style="color: rgb(170,155,245);">forcedReadTwiceConfirm，后续在<span>custom.tsx中直接使用判断即可，</span></span></span></s></p><p><span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="610" src="./********签署迭代前端详设_files/image2025-7-24_17-11-15.png" data-image-src="/download/attachments/*********/image2025-7-24_17-11-15.png?version=1&amp;modificationDate=1753348273000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523203" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_17-11-15.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p>对应的二次确认模板</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-24_17-11-48.png" data-image-src="/download/attachments/*********/image2025-7-24_17-11-48.png?version=1&amp;modificationDate=1753348307000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523205" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_17-11-48.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：</p><p>批量签、单流程</p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.3合同文件阅读时长控制—开发人员（观弈）">4.3&nbsp; 合同文件阅读时长控制— 开发人员（观弈）</h2><p>应用名：<a class="external-link" href="http://devops.timevale.cn/app/new/1768" rel="nofollow">tsign-openservice-pc-3-normal</a>、<a href="http://devops.timevale.cn/app/new/1764" class="external-link" rel="nofollow">tsign-openservice-h5-3-normal</a></p><p>设计稿：<a href="https://www.figma.com/design/oykOwSyW4h1UcNhrq3LogG/%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3?node-id=7727-4846&amp;p=f&amp;m=dev" class="external-link" rel="nofollow">https://www.figma.com/design/oykOwSyW4h1UcNhrq3LogG/%E7%AD%BE%E7%BD%B2%E8%BF%AD%E4%BB%A3?node-id=7727-4846&amp;p=f&amp;m=dev</a></p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 85.2259%;" resolved=""><colgroup><col style="width: 18.9995%;"><col style="width: 2.43666%;"><col style="width: 63.8818%;"><col style="width: 14.6994%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p>合同文件阅读时长控制，要支持设置每份文件都需要阅读一定时长，目前是所有文件总共阅读的时长；</p><p><strong>交互</strong></p><p><br></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p><strong>现状</strong></p><p><span style="color: rgb(122,134,154);">目前是所有文件总共阅读的时长；</span></p><p><strong>相关入口</strong></p><p>pc签署页</p><p>h5签署页</p><p><strong><br></strong></p><p><strong>修改文件</strong></p><p>src\pages\sign-v4\Sign.tsx</p><p>src\pages\sign-v4\SignBar.tsx</p><p><strong>涉及接口</strong></p><p><strong><br></strong></p><p><strong>关键逻辑</strong></p><p><strong><a href="http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file" class="external-link" rel="nofollow">http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file</a> 接口中，signConfig中存在参数<span style="color: rgb(163,21,21);">forcedReadingTime，会被转换为前端</span></strong></p><p><span style="color: rgb(142,0,75);">countdown字段，</span></p><p><span style="color: rgb(142,0,75);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="./********签署迭代前端详设_files/image2025-7-24_21-15-41.png" data-image-src="/download/attachments/*********/image2025-7-24_21-15-41.png?version=1&amp;modificationDate=1753362941000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523370" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_21-15-41.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span>为整个页面强制阅读的时长，现在需要在<strong>signConfig中增加新的参数，</strong></span><span style="color: rgb(255,0,0);">fileForcedReadingTime，他与<strong><span style="color: rgb(163,21,21);">forcedReadingTime不可同时存在。</span></strong></span></p><p><span style="color: rgb(163,21,21);"><strong>当前要求每份文档阅读指定时间后，才可以切换下一份文档</strong></span></p><p><span style="color: rgb(255,0,0);"><strong><span style="color: rgb(163,21,21);">h5：</span></strong></span></p><p><span style="color: rgb(255,0,0);"><strong><span style="color: rgb(163,21,21);">需要再</span></strong></span></p><p><span style="color: rgb(255,0,0);"><strong><span style="color: rgb(163,21,21);">src\pages\sign-v4\Sign.tsx 文件中新增属性</span></strong></span></p><p><span style="color: rgb(255,0,0);"><strong><span style="color: rgb(163,21,21);"><br></span></strong></span></p><p><span style="color: rgb(0,0,0);">&nbsp;currentDocReadTimeCompleted: false, // 当前文档是否已完成阅读时间</span></p><p><span style="color: rgb(0,0,0);">currentDocReadingTimer: null, // 计时器id</span></p><p><span style="color: rgb(0,0,0);">requiredReadTime: ？？, </span><span style="color: rgb(255,255,255);"><span style="color: rgb(0,0,0);">// 需要阅读的时长，</span></span></p><p><br></p><p><span style="color: rgb(0,0,0);">confirm方法中，新增一道拦截，判断所有文档是否已经阅读完成，全部阅读完才允许进行提交</span></p><p><span style="color: rgb(0,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-25_15-43-29.png" data-image-src="/download/attachments/*********/image2025-7-25_15-43-29.png?version=1&amp;modificationDate=1753429409000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523764" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_15-43-29.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p><span style="color: rgb(0,0,0);">再手动切换文件的时候，如果已经满足阅读时长，则允许切换，同时重置阅读倒计时及其状态</span></p><p><span style="color: rgb(0,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-25_15-44-26.png" data-image-src="/download/attachments/*********/image2025-7-25_15-44-26.png?version=1&amp;modificationDate=1753429465000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523769" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_15-44-26.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></span></p><p><br></p><p><span style="color: rgb(239,176,128);">preventSwitchDoc（）阻止切换文件的逻辑，</span></p><p><span style="color: rgb(239,176,128);">{</span></p><p><span style="color: rgb(0,0,0);">// 全部文档阅读完毕后才允许提交，如果当前文档阅读时长不满足，则不允许切换未读的</span></p><p><span style="color: rgb(0,0,0);">&nbsp; &nbsp; &nbsp; // 如果切换之前已经阅读完成的文档，则允许切换</span></p><p>&nbsp; &nbsp;<span style="color: rgb(0,0,0);">// 如果当前文件阅读时长满足的情况下允许切换其他文件</span><br><span style="color: rgb(0,0,0);">&nbsp; &nbsp; &nbsp; // 如果当前文件已经阅读完了则不需要记时，</span></p><p><span style="color: rgb(0,0,0);"> // 如果都不满足，再进行切换的时候弹窗提醒</span></p><p><span style="color: rgb(239,176,128);"><span style="color: rgb(0,0,0);">}</span></span></p><p><br></p><p><span style="color: rgb(239,176,128);">startDocReadingTimer（）倒计时方法</span></p><p><br></p><p><span style="color: rgb(239,176,128);">clearDocReadingTimer</span>() 清除倒计时方法</p><p>&nbsp;<span style="color: rgb(239,176,128);">resetDocReadingTimer</span>()重置倒计时及其状态</p><p><span style="color: rgb(239,176,128);">updateAllDocsReadStatus</span>()更新所有文档阅读状态</p><p>&nbsp;<span style="color: rgb(239,176,128);">checkAllDocsRead</span>()判断所有的文档是否已经完成阅读</p><p><span style="color: rgb(239,176,128);">在</span><span style="color: rgb(135,195,255);">DocListHeader组件中，需要先判断是否允许切换文件，再触发后续切换文件的逻辑</span></p><p><br></p><p><span style="color: rgb(239,176,128);">create生命周期中 需要</span></p><p>&nbsp;<span style="color: rgb(193,128,138);">this</span>.<span style="color: rgb(170,155,245);">docReadStatusMap</span> <span style="color: rgb(214,214,221);">=</span> {}记录每个文件是否阅读完成的映射，</p><p><span style="color: rgb(239,176,128);">startDocReadingTimer，开启倒计时</span></p><p><span style="color: rgb(239,176,128);">src\pages\sign-v4\SignBar.tsx 再底部提交按钮的地方，需要将倒计时信息传递给底部按钮，同时需要对文案进行一定的替换，submitText</span></p><p><span style="color: rgb(239,176,128);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="650" src="./********签署迭代前端详设_files/image2025-7-25_15-49-9.png" data-image-src="/download/attachments/*********/image2025-7-25_15-49-9.png?version=1&amp;modificationDate=1753429748000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523781" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_15-49-9.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></p><p><br></p><p><span style="color: rgb(239,176,128);"><br></span></p><p><span style="color: rgb(255,255,255);">阅读的秒数</span></p><p><span style="color: rgb(255,0,0);"><strong><span style="color: rgb(163,21,21);"><br></span></strong></span></p><p><br></p><p><br></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：</p><p><br></p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.4bugfix-.title报错—开发人员（观弈）">4.4&nbsp; bugfix-.title报错— 开发人员（观弈）</h2><p>应用名：<a href="http://devops.timevale.cn/app/new/1768" class="external-link" rel="nofollow">tsign-openservice-pc-3-normal</a>、</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 1141.12px;" resolved=""><colgroup><col style="width: 265.085px;"><col style="width: 216.065px;"><col style="width: 454.162px;"><col style="width: 204.901px;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p>再签署人信息身份不符合时，出现了控制台报错无法从undefined身上获取title</p><p><strong>交互</strong></p><p><br></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p><strong>现状</strong></p><p><span style="color: rgb(122,134,154);"><span>再签署人信息身份不符合时，出现了控制台报错无法从undefined身上获取title</span><br></span></p><p><strong>相关入口</strong></p><p><strong>发起签署--签署人信息不匹配</strong></p><p><strong><br></strong></p><p><strong>修改文件</strong></p><p>src/pages/infoErrorPage.tsx</p><p><br></p><p><strong>涉及接口</strong></p><p><strong>关键逻辑</strong></p><p><strong>增加兜底逻辑，如果出现的情况不再枚举的范围之内，则展示默认值</strong></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="654" src="./********签署迭代前端详设_files/image2025-7-24_17-13-58.png" data-image-src="/download/attachments/*********/image2025-7-24_17-13-58.png?version=1&amp;modificationDate=1753348437000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523208" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-24_17-13-58.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></strong></p><p><br></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：</p><p><br></p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.5印章授权bugfix-印章使用员传了仅审批不用印—开发人员（长歌）">4.5&nbsp;印章授权bugfix- 印章使用员传了仅审批不用印 — 开发人员（长歌）</h2><p>应用名：</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 93.5938%;" resolved=""><colgroup><col style="width: 32.9179%;"><col style="width: 2.4226%;"><col style="width: 60.5722%;"><col style="width: 4.09464%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>问题</strong></p><p>新增授权，使用印章时，onlyApprovalNotUse不能传true给后端</p><p>复现方式：先选中【用印审批】，将【直接使用印章】取消选中，再选中【用印审批】</p><p><br></p><p><strong>交互</strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="./********签署迭代前端详设_files/image2025-7-25_10-12-53.png" data-image-src="/download/attachments/*********/image2025-7-25_10-12-53.png?version=1&amp;modificationDate=1753409574000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523467" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_10-12-53.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="./********签署迭代前端详设_files/image2025-7-25_14-23-40.png" data-image-src="/download/attachments/*********/image2025-7-25_14-23-40.png?version=1&amp;modificationDate=1753424621000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523664" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_14-23-40.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p>在授权权限的radio-group组件上新增change事件，当选中印章使用项时，将useSealSetting的值设置为初始值true，这样给后端的值就是false了</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="700" src="./********签署迭代前端详设_files/image2025-7-25_10-27-21.png" data-image-src="/download/attachments/*********/image2025-7-25_10-27-21.png?version=1&amp;modificationDate=1753410442000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523478" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_10-27-21.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><br></p><p>@change="roleKeyChange"</p><p><br></p><p><span>roleKeyChange(item) {</span></p><p><span> if (item === 印章使用) {</span></p><p><span> <span>useSealSetting = true</span></span></p><p><span> }</span></p><p><span>}</span></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：新增授权-使用印章</p><p><br></p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.6saas印章图片上传原图截图—开发人员（长歌）">4.6&nbsp; saas印章图片上传原图截图— 开发人员（长歌）</h2><p>应用名：</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 90.2634%;" resolved=""><colgroup><col style="width: 23.4482%;"><col style="width: 2.83085%;"><col style="width: 55.5668%;"><col style="width: 18.1403%;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p>印章图片上传全部改成原图裁剪，目前只有开启了印章淡化并且使用后端抠图才会使用原图裁剪</p><p><strong>交互</strong></p><p><br></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p>修改组件：</p><p>将原先条件: <span style="color: rgb(255,0,0);">开启了印章淡化 &amp;&amp; 使用后端抠图 &amp;&amp; 图片不大于1M </span></p><p><span style="color: rgb(255,0,0);">改成 仅满足图片不大于1M &amp;&amp; 需要使用前端裁剪</span></p><p><br></p><p><br></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="700" src="./********签署迭代前端详设_files/image2025-7-25_10-46-1.png" data-image-src="/download/attachments/*********/image2025-7-25_10-46-1.png?version=1&amp;modificationDate=1753411561000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523495" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_10-46-1.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><br></p><p><span style="color: rgb(0,0,0);"><span>&lt;</span>cropperDialog&gt;组件新增<span style="color: rgb(0,0,0);">useOriginal控制是否使用前端裁剪</span></span></p><p><br></p><p><span style="color: rgb(0,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="700" src="./********签署迭代前端详设_files/image2025-7-25_10-49-2.png" data-image-src="/download/attachments/*********/image2025-7-25_10-49-2.png?version=1&amp;modificationDate=1753411742000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221523500" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-25_10-49-2.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：个人章、企业章、法人章</p><p><br></p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.7批量签接口支持可不传入姓名—开发人员（小麦）">4.7&nbsp;批量签接口支持可不传入姓名 — 开发人员（小麦）</h2><p>应用名：签署PC 签署H5</p><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 1815.3px;" resolved=""><colgroup><col style="width: 425.434px;"><col style="width: 51.3542px;"><col style="width: 1008.21px;"><col style="width: 329.184px;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p><span style="color: rgb(51,51,51);">用户流程：配置放开，基于文件发起时仅传入经办人手机号，不传入姓名，用户进入签署页登录后进入实名页面（免登链接则直接进入实名页面），实名后可完成签署</span></p><p><strong>交互</strong></p><p><strong>1 对于仅传入手机号的未实名用户 展示此页面<br></strong></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="./********签署迭代前端详设_files/image2025-7-28_14-50-40.png" data-image-src="/download/attachments/*********/image2025-7-28_14-50-40.png?version=1&amp;modificationDate=1753685441000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524954" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_14-50-40.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></strong></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="./********签署迭代前端详设_files/image2025-7-28_15-15-52.png" data-image-src="/download/attachments/*********/image2025-7-28_15-15-52.png?version=1&amp;modificationDate=1753686953000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524989" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-15-52.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br>2 这个case下用户无法提交签署 一定需要强制实名<br><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="./********签署迭代前端详设_files/image2025-7-28_14-52-1.png" data-image-src="/download/attachments/*********/image2025-7-28_14-52-1.png?version=1&amp;modificationDate=1753685522000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524960" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_14-52-1.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></strong></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="./********签署迭代前端详设_files/image2025-7-28_15-16-8.png" data-image-src="/download/attachments/*********/image2025-7-28_15-16-8.png?version=1&amp;modificationDate=1753686969000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524990" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-16-8.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p>签署PC相关页面</p><p>src\pages\batch-sign-v3\seals\index.tsx</p><p><span style="color: rgb(0,0,0);"><br></span></p><p><span style="color: rgb(0,0,0);">1 <strong>企业未实名现状</strong><br><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_14-56-17.png" data-image-src="/download/attachments/*********/image2025-7-28_14-56-17.png?version=1&amp;modificationDate=1753685778000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524969" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_14-56-17.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></p><p><span style="color: rgb(0,0,0);">修改点：<br></span></p><p><span style="color: rgb(0,0,0);">实名状态下 需要隐藏选章提示<br></span></p><p>展示逻辑：<span class="name" style="color: rgb(142,0,75);" title="data.subjects[0].needRealName">needRealName为true</span></p><p>接口：<span style="color: rgb(31,31,31);">getBatchSignSeals 其中 isOrgan 代表是否为企业</span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_14-58-6.png" data-image-src="/download/attachments/*********/image2025-7-28_14-58-6.png?version=1&amp;modificationDate=1753685887000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524972" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_14-58-6.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>对应代码：<strong>建议抽成组件 时间成本大概在0.5d-1d</strong><br><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_15-0-11.png" data-image-src="/download/attachments/*********/image2025-7-28_15-0-11.png?version=1&amp;modificationDate=1753686012000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524976" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-0-11.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>2 <strong>实名相关逻辑</strong></p><p>企业实名接口：{flowId}/organ/auth/{orgId}/identifyUrl</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_15-6-6.png" data-image-src="/download/attachments/*********/image2025-7-28_15-6-6.png?version=1&amp;modificationDate=*************&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="*********" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-6-6.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>个人实名接口：本次新增</p><p>{flowId}/individual/realNameWill/{accountId}/identifyUrl</p><p>两个接口本次都需要额外增加一个批量签的id 用于后端获取缓存</p><p>需要的参数</p><p>参考页面：src\pages\batch-sign-v3\mixins\will.ts</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_15-22-35.png" data-image-src="/download/attachments/*********/image2025-7-28_15-22-35.png?version=1&amp;modificationDate=*************&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="*********" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-22-35.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><br></p><p><strong>3 强制个人实名</strong></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="./********签署迭代前端详设_files/image2025-7-28_15-10-8.png" data-image-src="/download/attachments/*********/image2025-7-28_15-10-8.png?version=1&amp;modificationDate=1753686608000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524987" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-10-8.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></strong></p><p><strong>在提交的时候进行数据校验与提示，点击引导《去实名》后 先切换到对应的主体后，自动唤起实名页面进行实名</strong></p><p><br></p><p><strong>注意点：对于企业与个人都未实名的情况下，企业实名后个人也会实名，为了避免企业实名后个人又需要重复实名一次，因此需要在企业实名后，更新一次个人的数据信息<br></strong></p><p><strong>解决方案：在企业实名后，判断所有主体中是否存在个人主体并且个人主体未实名(realName),则更新一个个人主体信息，确保数据同步</strong></p><p><strong>但是如果用户已经选了个人印章的情况下是否还需要刷新呢？？</strong></p></div></td><td colspan="1" class="confluenceTd"><p>测试范围：</p><p>签署PC：钉签 saas 开放<br>签署H5：开放H5 app e签宝小程序/用户对接小程序</p><p><br></p><p>回归范围：</p></td></tr></tbody></table></div><h2 id="id-********签署迭代详设-4.8SaaS印章使用情况埋点—开发人员（小麦）">4.8&nbsp; SaaS印章使用情况埋点— 开发人员（小麦）</h2><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 1815.3px;" resolved=""><colgroup><col style="width: 425.434px;"><col style="width: 51.3542px;"><col style="width: 1008.21px;"><col style="width: 329.184px;"></colgroup><tbody><tr><th colspan="2" class="confluenceTh">需求与交互/模块概览</th><th class="confluenceTh">具体实现（<span style="color: rgb(255,0,0);">要求：描述时添加文字总结，不要纯贴图</span>）</th><th colspan="1" class="confluenceTh">测试注意点</th></tr><tr><td colspan="2" class="confluenceTd"><div class="content-wrapper"><p><strong>需求</strong></p><p>新增埋点信息</p><p><strong>交互</strong></p><p><br></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p>需求明细</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="600" src="./********签署迭代前端详设_files/image2025-7-28_15-26-34.png" data-image-src="/download/attachments/*********/image2025-7-28_15-26-34.png?version=1&amp;modificationDate=1753687594000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221524997" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-26-34.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>SAAS印章管理</p><p><span>git :</span><a href="http://git.timevale.cn:8081/saas-micro-frontend/seal-manage-front" class="external-link" rel="nofollow">saas-micro-frontend / seal-manage-front · GitLab</a></p><p>增加文件：src\views\Seal\EditSealNew.vue</p><p>具体增加位置：</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_15-48-40.png" data-image-src="/download/attachments/*********/image2025-7-28_15-48-40.png?version=1&amp;modificationDate=1753688920000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525152" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_15-48-40.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>判断端来源于 使用 <span style="color: rgb(206,145,120);">@esign/esign-client-jssdk</span></p><p><span style="color: rgb(206,145,120);"><br></span></p><p><span style="color: rgb(0,0,0);">SAAS个人中心</span></p><p><span style="color: rgb(206,145,120);"><span style="color: rgb(0,0,0);">git:</span> <a href="http://git.timevale.cn:8081/saas-micro-frontend/user-front" class="external-link" rel="nofollow">saas-micro-frontend / user-front · GitLab</a></span></p><p><span style="color: rgb(206,145,120);"><span style="color: rgb(0,0,0);">对应文件：src\views\user\Seal\AddSealNew.vue</span></span></p><p><span style="color: rgb(206,145,120);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-37-41.png" data-image-src="/download/attachments/*********/image2025-7-28_16-37-41.png?version=1&amp;modificationDate=1753691862000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525215" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-37-41.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-38-27.png" data-image-src="/download/attachments/*********/image2025-7-28_16-38-27.png?version=1&amp;modificationDate=1753691908000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525217" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-38-27.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><br></span></p><p><span style="color: rgb(0,51,102);">开放印章页面</span></p><p><span style="color: rgb(0,51,102);">git: <a href="http://git.timevale.cn:8081/saas-front/open-seal-micro-front" class="external-link" rel="nofollow">saas-front / open-seal-micro-front · GitLab</a></span></p><p><br></p><p><span style="color: rgb(0,51,102);">个人新增：</span></p><p><span style="color: rgb(0,51,102);">对应文件：src\m\PersonSeal\SealCreate\index.vue（移动端）src\views\PersonSeal\SealCreate\index.vue（PC端）</span></p><p><span style="color: rgb(0,51,102);">具体增加位置：</span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-1-18.png" data-image-src="/download/attachments/*********/image2025-7-28_16-1-18.png?version=1&amp;modificationDate=1753689679000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525163" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-1-18.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>企业新增：</p><p>对应文件：<span>src\m\OrgSeal\SealCreate\index.vue（移动端）</span><span>src\views\OrgSeal\SealCreate（PC端）</span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-5-25.png" data-image-src="/download/attachments/*********/image2025-7-28_16-5-25.png?version=1&amp;modificationDate=1753689926000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525173" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-5-25.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>图片印章需要审核 所有算新增成功吗？ 图片创建印章埋点可能需要后端支持</p><p><br></p><p>saas-h5</p><p>git : <a href="http://git.timevale.cn:8081/saas-front/esign-corpmng-h5" class="external-link" rel="nofollow">saas-front / esign-corpmng-h5 · GitLab</a></p><p>个人新增</p><p>对应文件：src\apps\revision\views\sealManage\personSeal\index.tsx</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-30-25.png" data-image-src="/download/attachments/*********/image2025-7-28_16-30-25.png?version=1&amp;modificationDate=1753691425000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525207" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-30-25.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p>企业新增：</p><p>对应文件：src\apps\revision\views\sealManage\legalSeal\index.tsx（法定代表人） src\apps\revision\views\sealManage\orgSeal\index.tsx（企业印章）</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-31-50.png" data-image-src="/download/attachments/*********/image2025-7-28_16-31-50.png?version=1&amp;modificationDate=1753691511000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525208" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-31-50.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="./********签署迭代前端详设_files/image2025-7-28_16-32-58.png" data-image-src="/download/attachments/*********/image2025-7-28_16-32-58.png?version=1&amp;modificationDate=1753691578000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="221525209" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-7-28_16-32-58.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="36"></span></p><p><span style="color: rgb(0,0,0);"><br><span style="color: rgb(255,0,0);"><strong>注意：需要审核的都由后端来埋点，例如 图片印章（企业），本地上传（个人）</strong></span></span></p></div></td><td colspan="1" class="confluenceTd"><p><br></p></td></tr></tbody></table></div><h1 id="id-********签署迭代详设-五.其他确认点">五.其他确认点</h1><p>以下确认点，如果有涉及到，在需要一栏打钩，同时在方案描述中进行详细说明，若不需要，在不需要一栏中打钩</p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 1301.54px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 152.538px;"><col style="width: 52.875px;"><col style="width: 85.425px;"><col style="width: 729.188px;"><col style="width: 280.712px;"></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="确认点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>确认点</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需要: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>需要</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="不需要: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>不需要</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="方案描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>方案描述</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="备注: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>备注</p></div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="确认点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>确认点</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需要: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>需要</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="不需要: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>不需要</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="方案描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>方案描述</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="备注: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>备注</p></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td colspan="1" class="confluenceTd">组件设计</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="33">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="34">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">业务逻辑是否下沉到后端</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="35">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="36">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">包括 接口聚合、多端复用逻辑、复杂逻辑、场景透出判断、功能开关 等。</span></td></tr><tr role="row"><td colspan="1" class="confluenceTd">通用方案设计</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="37">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="38">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(193,199,208);">考虑是否需要沉淀到utils工具库，若utils工具库里已有现成方法，必须使用</span></p><p><strong><span style="color: rgb(255,0,0);">utils工具库地址：<a class="external-link" href="https://design.tsign.cn/vue-gov-utils/is" rel="nofollow"><span style="color: rgb(255,0,0);">https://design.tsign.cn/vue-gov-utils/is</span></a></span></strong></p></td></tr><tr role="row"><td colspan="1" class="confluenceTd"><p>灰度</p><p><br></p></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="19">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="20">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">灰度方案说明、灰度key</span></td></tr><tr role="row"><td colspan="1" class="confluenceTd"><p>国际化</p><p><br></p></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="21">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="22">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(193,199,208);">运营支撑平台配置</span></p><p><span style="color: rgb(193,199,208);">项目本地配置</span></p></td></tr><tr role="row"><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">分发内容大小评估（成本规范）</span></p></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="23">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="24">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=203679352" rel="nofollow">关于内容分发的标准规范</a></td></tr><tr role="row"><td class="confluenceTd">是否有需要记录沉淀的内容</td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="39">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="40">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">特殊业务逻辑、临时方案</span></td></tr><tr role="row"><td class="confluenceTd"><p>兼容性</p></td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="15">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="16">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(193,199,208);">三方平台api兼容性（比如低版本客户端api不支持如何处理）</span></p><p><span style="color: rgb(193,199,208);">浏览器兼容性（比如低版本浏览器不兼容js api如何处理）</span></p><p><span style="color: rgb(193,199,208);">操作系统兼容性（比如iOS设备需要特殊处理刘海屏）</span></p></td></tr><tr role="row"><td class="confluenceTd"><p>性能</p><p><br></p></td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="17">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="18">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">现有方案是否存在性能问题, 可以考虑换方案, 例如沉到后端</span></td></tr><tr role="row"><td colspan="1" class="confluenceTd">配置</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="43">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="44">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(193,199,208);">发布平台是否需要新增、调整配置</span></p><p><span style="color: rgb(193,199,208);">运维侧是否需要新增、调整配置</span></p></td></tr><tr role="row"><td colspan="1" class="confluenceTd">如果出问题，影响的客户规模是否超1000</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="49">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="50">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd">涉及人员名单：</td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">通用埋点是否正常</td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="51">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="52">&nbsp;</li></ul></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">测试、模拟环境验证</span></td></tr></tbody></table></div><p><br></p><div class="table-wrap"><table class="confluenceTable" resolved=""><colgroup><col><col><col><col><col><col></colgroup></table></div><div><span class="name" style="color: rgb(142,0,75);font-family: consolas , &quot;lucida console&quot; , &quot;courier new&quot; , monospace;font-size: 12.0px;" title="data.subjects[0].needRealName"><br></span></div>

                
        
    
        </div>

        <!--
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:dc="http://purl.org/dc/elements/1.1/"
         xmlns:trackback="http://madskills.com/public/xml/rss/module/trackback/">
         <rdf:Description
    rdf:about="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:identifier="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:title="********签署迭代详设"
    trackback:ping="http://wiki.timevale.cn:8081/rpc/trackback/*********"/>
</rdf:RDF>
-->

                        
    



<div id="likes-and-labels-container"><div id="likes-section" class="no-print"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" class="like-button"><span class="aui-icon aui-icon-small aui-iconfont-like"></span><span class="like-button-text">赞</span></a><span class="like-summary like-summary-margin-left">成为第一个赞同者</span></div><div id="labels-section" class="pageSection group">
    <div class="labels-section-content content-column" entityid="*********" entitytype="page">
	<div class="labels-content">
		
    <ul class="label-list label-list-right  has-pen">
            <li class="no-labels-message">
            无标签
        </li>
                <li class="labels-edit-container">
            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="show-labels-editor" title="编辑标签 (l)">
                <span class="aui-icon aui-icon-small aui-iconfont-devtools-tag-small">编辑标签</span>
            </a>
        </li>
        </ul>

    </div>
</div>
</div></div>
        
		
            




            
        








                        
    
<div id="comments-section" class="pageSection group">
        
    


    <div class="bottom-comment-panels comment-panels">
                
                    
    


        
        
    
    <div class="quick-comment-container comment add"><p class="comment-user-logo"><a class="userLogoLink userlink-1" data-username="taolang" href="http://wiki.timevale.cn:8081/display/~taolang" title="" data-user-hover-bound="true"><img class="userLogo logo" src="./********签署迭代前端详设_files/user-avatar" alt="用户图标: taolang" title=""></a></p><div class="quick-comment-body"><div class="quick-comment-loading-container" style="display:none;"></div><div id="editor-messages"></div><div id="all-messages"></div><form style="display:block;" class="quick-comment-form aui" method="post" name="inlinecommentform" action="http://wiki.timevale.cn:8081/pages/doaddcomment.action?pageId=*********"><div title="添加评论 (m)" class="quick-comment-prompt"><span>编写评论...</span></div></form></div></div>

            </div>

            <div id="comments-actions" class="aui-toolbar noprint" style="display: none;">
            <p class="toolbar-group">
                <span class="toolbar-item"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;showComments=true&amp;showCommentArea=true#addcomment" id="add-comment-rte" accesskey="m" class="toolbar-trigger">添加评论</a></span>
            </p>
        </div>
    </div>
        


                
    
                <div id="watermark" style="pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAAEYCAYAAACHjumMAAAAAXNSR0IArs4c6QAAIABJREFUeF7tfQl3G8eVde/ggqUBNEhbpm15j51lMjP//yd8iyeTxIpjW7YpWSKxNQAuQG9zbqeKU2yBEiihpWLx4hyf5IiNQtV9hYuqt9xnW3wRASJABGpCwK5pXA5LBIgAEbBIMNwERIAI1IYACaY2aDkwESACJBjuASJABGpDgARTG7QcmAgQARIM9wARIAK1IUCCqQ1aDkwEiAAJhnuACBCB2hAgwdQGLQcmAkSABMM9QASIQG0IkGBqg5YDEwEiQILhHiACRKA2BEgwtUHLgYkAESDBcA8QASJQGwIkmNqg5cBEgAiQYLgHiAARqA0BEkxt0HJgIkAESDDcA0SACNSGAAmmNmg5MBEgAiQY7gEiQARqQ4AEUxu0HJgIEAESDPcAESACtSFAgqkNWg5MBIgACYZ7gAgQgdoQIMHUBi0HJgJEgATDPUAEiEBtCJBgaoOWAxMBIkCC4R4gAkSgNgRIMLVBy4GJABEgwXAPEAEiUBsCJJjaoOXARIAIkGC4B4gAEagNARJMbdByYCJABEgw3ANEgAjUhgAJpjZoOTARIAIkGO4BIkAEakOABFMbtByYCBABEgz3ABEgArUhQIKpDVoOTASIAAmGe4AIEIHaECDB1AYtByYCRIAEwz1ABIhAbQiQYGqDlgMTASJAguEeIAJEoDYESDC1QcuBiQARIMFwDxABIlAbAiSY2qDlwESACJBguAeIABGoDQESTG3QcmAiQARIMNwDRIAI1IYACaY2aDkwESACJBjuASJABGpDgARTG7QcmAgQARIM9wARIAK1IUCCqQ1aDkwEiAAJhnuACBCB2hAgwdQGLQcmAkSABMM9QASIQG0IkGBqg5YDEwEiQILhHiACRKA2BEgwtUHLgU1F4PDwcD9Jkm6WZX5RFPFsNosty8pMXe+brIsE8ybo8b33DoF+v/9Bmqbv+b4/T9N0ZVlWaNt2OplMHlmWldw7QF6xYBIMdwQR2BCBbrfbyfP8c8/zno1Goyd429HR0e58Pv/G87zz0Wj09w2HujePkWDujam50A0RcNrtdhgEwc5yuVzN5/OpZVkp3tvr9T60bXt/NBp9p47VarX6juM8tG37h+l0iuf5EgiQYLgV7iUCYRiGeZ47s9lsLAGAb2W1Wn1aFIXred5Fmqa7lmUVYRg+evz48WUYhh+7rrtbJZgHDx7snZ2dfe37/mI4HOKqxBcJhnvgPiPQ6XQ+9X3/Yjgc/iZwcMIw/GNRFIs4jh/DaSuJw7KsaRzHPzSbzYHruh8FQfDo9PR0IfHr9XpHlmV1sizbieP4/9Ph+787iyeY+/wtu8dr73Q6n/m+fy4JZjAYNFer1VdxHP+3ZVlLCU0URe+naXo4nU5BHC5ICH/zPO+H4XC46vV6B1mWHezv7/8dpxjXdb8fj8ezewzttaWTYLgT7iUC7Xb780ajsTg9PX0GANrtds+27U/iOP5W+lzw7xWCsXCqubi4+CzP8wB/dxxn5XneTzjRhGH456Iofo7jeHIvQV2zaBIMd4LJCPhHR0fe8fHxRXWR7Xb7i52dndnJyclz/A3EcX5+/mWz2XykPG/3er1vsiy7iOP4R3WMhw8f7qRpah8fH1/CT4O/dTqd/3Qc55+TyQR5MXxZlkWC4TYwDQGn0+l0iqLo27bducnxGkXRl5eXl9PFYnFS8UeWZIGX9Lng+vP06dNzBSh7MBjsV/ww7SzLvojj+C+WZSE/hi8SDPfAXUWg1+u10zQ9mM1m/8Q+jqKomaZpryiKCGtCIlxRFA7+ti4/JYqiry4vL8eLxeL0BgwanU7nD77v/zYcDp+qzyACVRTFZ7Ztj3zfHy6Xy8BxnI/gDJ5Op3AQ81VhbQJCBO4UAjhd7Ozs9BAWRn4KHK2u6156njc8OTmBDwQO2CPHcfbXhY77/f7vVqvVcD6fD9ctHCecPM/98Xj8N3kFUp9rNpsHnuc9QEhbEBqICBGpqxPQnQK0psnyilQTsBy2XgTwBd/Z2QmHw+E/4A9ZrVZO5RpjIW8FztjZbPZ9dTb9fv/r1Wp1Mp/PR9W/yYzdSkTIPzg46OV5jsjTXPmB9kWJAIlljclJMPV+Dzh6TQgcHBwcXl5etsQVae2ndDqdTxDoQQ5L9YFer/f7NE1/UxPt5DN4n+u6e+Px+Lt2u92xLAv+nLZt21mWZcc3nXpqWuqdHpYEc6fNZ/TkS78Kck3UvBS54sFg8N5yuWy+jGAQigYpxHH80zqCybLs6bqQMsgHSXPKe6bwt0ynU+S35EajvuXFkWC2DCiHezMEcN1ZLBa9NE0HRVF4QRA8kbkq6sjIT0mSZG/d6UQ+Bz9KmqbL6XT685prELJ2f11XOwRi8n3fWy6Xo8ViAX9OWYvE1+0RIMHcHjO+Y/sI2M1mM2o0GlGapnuQP/A877TZbI5RA7Tu46IoepAkCVLzr+WnqM/CkVsUxdl4PP61OkYYhv9m2/bjG3JWEH3iSWULdibBbAFEDvHGCCB35d8RWrYsC9EY1Pm81GkKXRYIPr0sLIyrjm3bUymtoM4SWbe2bf/EpLg3tt1LByDB1IsvR7+OgN9qtTqNRiNIkgTZsZA2KIkEmbWO4yQVwsD+XEs0CEHnee69jGBwSvE870QpaLyaDQjGcZwfWTdU7xYlwdSLL0cXCLRarchxnI/F9ecySZKm53lno9HoH7iOwGmbJAmKCr+FbEKWZR8kSdLC847jjMbjMQSersgGuS+QW1jnX5Ggg0SCIPhNlgOoxkBav+/7/1BCzrRVDQiQYGoAlUNeR0DRS3k6HA5RXFhIwsmy7Fek64NULi8vf5fn+c8gIsdxTvM8n7uuC7I5tCxrrEaDut3uR7ZtF+v8K/LTQSJZlv2yLlsXIlHz+RwOXPpaatywJJgawb2HQzfEmhFavjpt4HSCqNBkMkGdztUL6fpJkqwEcdidTuc/8MeqMhxyXpbL5ZFaE4QkOsdx8pcRDCQYTk9Pz17lz7mHdrppyTcWh74uRiSY10WO77tCQKjsP5S5I0jZ933/8fPnz/HlLjNqkbIv0u6rBJPISBA0WkAGayJDJfmop5EwDB86jpOOx+NjmmI7CIiSC1/g77ZarW6j0ehDhGudo3yTTyXBbIISn3kZAlCC+xNEr3d2dn7N89xeLpcfgWzCMPwOYWacQJIkwQkGYk7yFXQ6nT9WrjA3OnXhT/E877l02CLb1vO81etufJrU8judTlN1tAs7HeR5jh+GLpIUbdseB0Ewkj8Wt8WNBHNbxO738y/kh0hJgziO/5/iz3C73e7XjuNkopL5hffhtGLbdnM6neLadOUHQZW04ziF6nyV/hpVqlJIXl5WK53vt3k2X72st4LdDg8Pd5MkQZ0VTiso3kR0Dz4v6NrANvgRab9OJjMJZnOb3Ncn3V6v936e5xE2n+M4kJP8VeaPIFwMoqgKYd9QMFhi2Ol0upZlfbpGXtLu9/tfpWm6D2dvq9U6Xy6XXfQhsixrol6dQFBBEJyty/K9r4a6zbqlgp+U+kRED4WceZ4P4jj+PxgLPiwhgQEZDLeqRbzJ55FgNkHp/j7j9Hq9r6GrgsJA3/dXtm2j8diedLiKcHFfaNZeQ6rb7f4hz3Pku6jFhl4Yhn8AYdwQYsbR/UMc0TEYwtRZlj1ZU2AIh/I1Z/L9NdMLK8f3GpKeV9rC1Sdw8oTcBNICLMsClkulx9OzPM9xogmQ/IiSCdG+5dbdK0kw3JU3IiD7/VRU2vwwDL9xHGeMCI5yRbomlo1BpU5LHMf/V0ZyRAbuYDqd4vmyxgcb++LiojcajaAuJ7sj4lqF/1gHdMs9Ctwty2pVnerqMCi1yLKsLyJ7XqvVQi8olGrsw0nvuu7o9PQULV2kOl8p3nXbjgkkmFsa7z49flOjMRQDAgdUMstfvaIofqpKH0iCarVaf5M6t8iuFeT0DPoqaZpGcAhDPDsIgh9f15l4n+xSWesL/q0oilpJknyp4l7FB3VaIJNut/vXOI4/R2EpfC+QGlWEz68pBTYajeN1SYsvw54Ec4935quWrpw20LLj6iVIAtm1ZYgYwti2bec3NSRT8le8TqeDIsMMd3oZpfA8b6zq275qXvz7vxCQCYxxHP+XPPmB8CFEjsie67rDG6JspR0whpAEhegWTir49z/B15LnOfo8we/mFUURQ9943Y/Iq2xBgnkVQmb8Xdr5VqprcPJBb1Y9mUjn4M7OznfytCGdttUEOSVSIa9PMpmO+ipb2Fcy+xmdDDzP28WVRzR/++8oivpIbpxOpyCfa3aXNnRd9wS+FuWZMnUAU4PTV8pVtNttiG2hpcutm8qRYLZgaJ2GwJfddd0H7Xb7h4uLCzdJki4iQJZlPd5C32T4X36/xkFr49qEZLpGo/G9IB4viqIycQ6ylgpGlELY0oaRBIPhcMXEiSVJEugE/9Ruty/m8/k3CDfDnzIej1GpXhINfjiSJInCMHw2mUx+r9RkwWaDZrM5UWQy7G63W1alv05SIwlmS8bWZRgckReLxVdSjLooipnv+26apsiYfUE68jbzRu6Jbdst4aCtRhTcfr//hXASXiACgSvQ7u7uD1Wt3Nt8Jp+9GQGFYH6UynzQGgYZICER/x8RP4wAW6Bo1Pf9sfgBKIkevhj0fVIjevDhIGHSdV3kwKDj5QfVhnSb2oUEsylS+j2HhKirL7lagayo6yMKkMg2G7fYJPbh4eGe6nCV153qNQinaSXSY4dh2MFxfbVaLWezGRK2WEy4hb0D+z5//hziW1c2V+x6leSIHwHXdVc4bUAY3XXdD+GjgUyG7/toc7urJkWuS5QUNWJNddrIf0KeTBzHv9wmskeC2YLx38UQcKwiP8V13fHOzk7ZqlQcia9+zUSN0FWG5k2VxXL+MlwsnXsqIWHTKdedMrMTEQeUGr1OAta7wEzXz0RlOCrH1+gDNwaDARINozzPG0g+VPOBUOMFMXK1iBQlFMignkwmIALkFP1Jdc7CMVw5UUqH79W+ETiBG/woihrL5bIRBEEjTdOGKEzd2JdHgtF11/3vvJAwta5TYKPZbLZRjIZrCXJVer3eF3meL3zfX8pNiTs4wsJIoHUcZ68a6cEmajabXeRA4NdNde4pv1Sl4hzCmPh1lCnluH5ZljXiSeXNNhFkJRTyuCoyhGaO9K2sVqv9IAhcpccTTouIBE2DILg4Pz8vwjCcz2azD4RwV6lDLFIK7HWtW+SskRVdFAWeQRO7rb5IMFuFc2uDeYeHhw3f93OcSpCrAKebCEEi8Uwmo8kPLDMxpawB2qUul0s0FcMVBUdqr9frvQddFTkW3oiKZJxCxCYevUQD1+10On/Ge6rXr62t+P4OVGKLrgWiE2VZZAh/SVEUnSRJTqCXgxot0Zq2jMjJXBc4aG3b3i+Kog2BLsCo5qvICJ8ayq5CrVy1rsLd2zIHCWZbSG5xHCWN+7/gwcc1CEV9SL1PkuS5FFCCX2QymeBeLlPCyyOxiAqciWtMD2n38pdwOBzK7MwCmy8IgmSTHBRouvi+P6PD9laGhiMV1wn8V/bM7na7F6qQObooIJKDUXEixJVH/ghUCjrLE4ui0OdAKF3prW3JsRC2VrSGy9OnFPa6YfZlRXwdGsUkmFvtl+0/jF8ijFqRbiyJAsWAaAAmxJr+KoSxpcyjDT8M8h6UE8sI2rau6zZEZma5bz3PQ9UxQsUglZs0V7a/uPs9IgihTHbL89wtiqKHqwsKRVUdYKTso8c2InMPHz70QDZxHJenlKosqCgBaI/H47+ug1Zx6qK+6KrE4iY9nsoYtaQPkGDe8ZcADj7XdXfl3RpJUEhsE3kl6XQ6/Q1N2PELJCMC6hUJ16bz83PUkOzgni2TqPI8fywkIb1ut/uVbduTLMug8/Gpek16x8s38uNFb6cuclLEtfK57/sT13ULSFHIsgncOHE9kr2flMRESFisRGsWRH3K9ALkr6xWq69usp9oGIdIz7VGc5uUDtRlCBJMXchuOK7cNPjVOjo6cpDDgvt3lmVo3fEpsiejKPo8yzLklUAicu2vl/i4shG78JeoUYFGGIboEeR5nveMIk0bGmezx67SBUAs8/n8IZzuSEb0ff8CJOP7/lNRneynafpEXnGlz0xG65SQcVkcih8fOF/VHBVck3zff16VqZC+lhuEzMvTbp7nT7aQbLkZKuIpEsyt4Krl4fJujeMzrjvIM7EsC3KQIJkDhBhFOPpoOp2CXKoOXjmpMg0fIWNkaaKlRyUq0Oj3+59g8+NK5TjO9OTkBE7gG0v6a1mtWYPK1HpE107H4/E58k3m8/m83W43EZmD4xWOcdGm9qzVapWRv+FwiIZxebfbbSraOh86joPTbJn5jFNsEATnqqgW9Hfwp0qldJlhjQhiHZGgNzEZCeZN0HvD9+I64zgOfqXKk4dwzs2Q5LZcLuFLQXuPbDab/SijOCg8Q+h5Npvhf9Vs2nKz4/g8m80CEXGQUQGU42Pjj+TxHbkuCF/zNPNGRkQaPaI7AxQDorYHCW74wYDtUMvj+/4uCAFJanC2K2kAcLa/YD/VGdvtdv+YJMkztSuCvO6oUSHkvuDzX/ED9EYLfd03k2BeF7ktvA+JcOfn52iybqNVB47Ssq0H7ufQ6HAc5yE2E0SY4KtBGjg2NI7QQiyonIlMGxfH7QwORohE7e7unidJ8hnyV0aj0aNKZu21bOAtLOk+DOENBoOdNZE35CvhhZwl//DwEM3lyiRHhJ1RD4SEyMVigYQ5pyptgYgRCEgWFEoiqfpbZAV1RYohQEKcjj2eSDAafCWkcw9TwS+f4zi/DIfDs263i3szclx+RIuOPM8/FwSSR1G0r24oJd+hvL8jxwWpLjgdCb/LU7bv2MjYTqvV6okcIhmJuZa5jJB/tQWLEMdyHjx4EFxcXHwKfxcEsyFFgU/NsgxFp6XmLVT61CpnJacFPzC/Kdcjp1Ioiuv0x0KzZes5Kxuhc8uHSDC3BKyOx2USFX7p4ATc2dnBZkTBIJLgEs/zhicnJyc4eksnIQjFtu2P5CkGIUohgVhqtyBc7fu+12g0fmbuyq2s1kDUDieHs7MzT9WkheMWpDGdTnE9vapMluSBv00mkyedTge5LBeLxaILCQWQDXxqrutOGo1GhuRJVY8YujtQoBMnzEKeRmXdF661s9kMfjWUZthFUfy8pqzgVot8Ww+TYN4W0i/5HOGLaRVFEWFT+b6fIhyJULPrui3Z/6eiMFfmykg5S2zSoihCJcqEIkTc8TeuG9EAinc+BXmakKJYIsdIatK+IN+J6Axqf9I0hbM8FCeTXKr5iTqgOAxD2Les3cLYSPGv9NWWeSjoyID8pkuZ3i8S6GDfyRrf2zvH7GUTIMFoYB6Qg+u66XK5RAVr2XhM5sPgb6LI7Ef5y4b7N8rpz87OvlaEnxoPHjxweVp5pUGx5/FlVh2s1zRpMYKi9FZG2YRzHFnRLaUeCH+6IgaR8v+DCAWXOS7IzoVODq5Vq9VquFgsJlEUIbr0oNLqpZy4vNrq6LB9JbJrHiDBvA5qW34PogB5ns9c14WU5GdqFTNyIZADI8OPUktVfAnmw+EQBWqURNjMJkG/34cz1ZLXEZn9Kut/RNYtdGnRusNDIWij0UAi4x4U9hF2vql6HHYE4cjEOFHZ3Nzf3//n8fFxLiuj4YcRUpRVHWNo4L6fpun5285X2Qy+2z9Fgrk9ZnW8A2X5/unp6TmiP1mWHYuy/DJHRk3Owo8rcik6nc65khFax5xMGtPGlcV13SPXdZej0Qg5KOXJRAh0NaRfRYZ8kVMiQs+XwgdWauuIUoylkEO4hpGSifttFEWQsIRn/nNcs1AZjYJGIXg+j6LoS1yrKtckkzAv10KC0cykaulANXVcs6lqOx1UBzcajQSCWegK6Xne+zg1pGn6VOSU3OiXQotaEekZNpvNE0niyLiWzlwsXPpaKiCUPwi+7z9LkuQ9z/N+SNMUtV84kECfBSdNt91ul3krr6PSry3oN0yMBKOZxdTSAUzt4OAgvG2rCM2W9FanI9PtZX0PrpRJkqRIVnzVVVLVuBX6LDhp9BAJEukCOMUg+xnSodLXcm19yLSFLAbSDZBt2+v1Pqh2voR/rdfrnatV1W8VpLf4YSSYtwj2ph+FTToej0+Zxr8pYuVzjXa7/SFOBqLQE604LBCO7/uHk8kEFcrlS1Xum06nf5PlF6g09zzPL4piAZLAs2s6G/oQ9sqybLlO47jaYWFda5FbreqOP0yCueMG5PT/hQCuNp7nXQyHQ1QSqwqAV5KQjUYjSNMUrT2QEZ0i03mxWICIMkkEOJns7e2tEKFTclWcdrsdogUUCAwRIYia36BxjJINiGSrFc2qbvG9MhkJ5l6Z++4vVp4+0jTFF/zqSyx0bV38G8hiuVwiWbE3mUz+1ul0yqxmkAr0WbIsQ5btNS1bhIcRThZFhIWUPkAFu8hPSj3PO0W6v2xspvh07j6wNa2ABFMTsBx2qwhc0w0GUSDMq+aRSN+VqFzecV23bJ0idHRk+F+q75fZupVwsz8YDBqyxkhKKSDy43neaDgcQj7jyjkMEfQ0TS9VKYWtrtiQwUgwhhjSlGWgbGI8HkMGFNecoN1uf4RriTx97O7ujo+PjzNUjsssZrl2VB8jA7bdbh8j+oMkRejVIlcI4X+1+Rycv6hwXhduFuOV1enVnBdEqBzH+QDSGqgRuysp++9qf5Bg3hXy/Ny1CIAkUHksNFDg+0DEpqP0t8b7Su0btX0t/hFJaiLTttRTURysf+l2u+8hiU5eqxC+Rl7MDeHmcm435LwgDwnSF4gmvVA6QLNeR4AEwx3xrhBAij3+u/YlBUlAq0ZUK8M5ii/6wyAIrlUWi5YdP6sZr1L0WpxsUjhm0VMZIes0TS+KovhEFIfiqnNTP6ArPKRez8tI6F2Bd1c+lwRzVyxl0DxF0trH8GmMx+O/q74NeeqQ2a8in2UCn4l6JYETFh0W1EZkgAjtUiEzKSJFZfuPvb294fHx8Qq1QeqpRwigWy9pqetUZTEMMsNbWQoJ5q3AzA+Rfg1xVYFg1iSO418VCdAAf4P6PjJpEQq2LOuXyWSC5m6FqNE5bDabj+BfWXeCEQQD/wiq0h/L90r04XeBVKjUs32Nlro05C0RIMHcEjA+vjECvrj+FKLe5xByBZCMLIriqdq6Q4yINqWfQGbStm1o3XSE1AHa4uJKA78L/DHNMAwfob1H1QeDcVBeIa5CpS6O+kIoGmFnxbFb9gNSar82Xhwf3AwBEsxmOPGp2yEgW82WURY4bvHFFkp988pQ2IPXaoOkfCSeUwSzQTSSZKCtAodt2T+oMl7pW1nXyVDNlVHeA8VAnJaom3M7G2/0NAlmI5j40CYICFW9PVQIC5Kw4d/A9QZiWKPRCP6W8gU/jFSLq2qfiJ5Q+3men0HGQij1QSISxYI2/o5EOaFhfFLRdinJrRJ1Kj8Tp5s0Tf2q32aTtfGZ10OABPN6uPFdaxBQdIG/7Xa7IAipIQzxpT+gawI6VcJHgiQ42Sq12ssHLXKRxi8Fz8Xp4iraJK5K6IgJ+UipOQyiQUuXkmAqoti01ztCgATzjoA35GPto6OjHUWXRvVpjOCIhU4tOiHYto0WqWhIhtc4juMnuJpAcjJN02eKyn4QhuE3CA2LzghXjcrEe8srkChonA4GgwGkEUA0om3IEzSqGw6HP7NY9N3vMhLMu7eB9jPA1QKTlA3C1GpkkbIPh2opQQk/BxyxlmXNZEUyvvhJklwgsc1xnCGuS5PJBNelDNcdXINkE3fknvi+fzAajb7DWKgPqlyt3lutVigmvPpMnFoODg4GkBx9SchZe5xNnCAJxkSrbnlNolUGhJuegRyQJu953rnruuPlcnmkyiOoNUFCCQ7OWThRy5MHKpQty/oQ/hX4alDTI7pMPhfTDnq93uF4PP51TS/msoZI6OWiDQtfmiNAgtHcQO94elBf68gWqKgHQkVxs9kcS7EkITGZq0V/It1/KHv8SGFspN4HQbAIgiAWcggngrCeVBqRXUWW1NIBnHbQfG4ymaCFLnWI3/Hm2OTjSTCboHS/nkGj9BakDkSbDcu27SEaiKkVxbIFbTU6BKhQZJjnebcoimMkzomOhX8ZDAYYs41mYvJ0gudvaNheoi4S7A7gk4miCLq2xZquivfLQndotSSYO2SsLU0V9T+o8ZGiTNCR7TSbzSWctVJlX0R4hnEco8mYPC1Ilf0+HLbIUZFN4SpSBq0kSb4U80WzsnYQBL/Ztr24vLz8nZRZAHmgfQcU/aFje3p6iutUNa+l0ev1DsbjMa5EaquRLcHBYepEgARTJ7oajo0vNU4nvu8/lr2TMU2lW2ApU4CQsnDqXmubimfhixGyCRc3LLGsds6y7BeIbEMC1LKsDsSclCroZRiGqC+aeJ63StMUmb5oqzqkxoqGG+c1p0SCeU3g7ujb7G63CwcraoHka4pGb5UTyJer1Sp3XXcla4MgvPQvHiqiNQ3DZPOxq0FRSCh6MP8sCxiR/HZ+fv4l1PbzPA/RCnU6nf5FnEzsw8PDPQyAbgB3FF9Ou4IACcbQLYHITxAElycnJ6NWq4U2Hv0kSZKiKBz0rB6NRt+3Wq2u4zgfr1HIRx1RDqnJSrvSsl2tqGqei6ZkfZQBVJq0oxvCIfRvhQSlhepny7LiPM8j1P7gc0WtUdkcni8zESDBmGnXUrYAHSGRp4IlirT6cRRFII3FaDRCohu++FCQgxzkJlEZp9frQQkO4+I6g+zaqdo/SMIpezOLDokl4axWq/dAMr7vD1erlU81OEM3n7IsEoyhNhbykdCljV3XRftZ+ENiSFAixd73/fPVanW5t7d3+fz5czhWb1RnU+uGQCzCZ4O2pxNIIMBJ22w2f1H7/ChN49CCFS9k6D6YTqfHVIIzdNOtWRYJxkxbl1cZqScLeUhw10ggAAAIXUlEQVRcY9AfCNck3/cvkG2LdqZy+SAJ+FjyPP9VzUmRjcwQVUJL59lsNsfYENMW2bd+u93GNQv5Mo+ggXtycoK6IK/f7yMjtzwp8XU/ESDBGGh3RUhJquhbohH7vkzRl6cKZM06jgPn6tM0Td3pdAoHK4oG5QsnFjhxr/4N/h0ULYoUfg8+HvhU8AbR0RDZumpvIgNR5pI2QYAEswlKd/MZOGpLUkDiW5qm74kK42Wn04EoE5yzHRACRLZPT0/hbN2IFOC3ybLsi6Io4jWK/zeFru8mipz1GyFAgnkj+LR/M5LoPkJo2XXd51DVF03EoFU73NnZGT99+vQcDlmhsP/ty1aE3s0yd0Y4eUeO44zH4zFEpCjYpP12ePsTJMG8fcy3+omoPkatUJZljaIo5o1G41TkkUAX5RPbtlvIkkUFMmQScFqBUD+qnaVsJbJ3gyAYjMdj1PhUXw21Abzii0GGLzNrt2pN8wYjwdxhm0JjVujcgkCWIIk0TfdEbQ8akD1sNptPcEqBxILUben1eki2Q5IbGr+vRD8h1Ag9EoJNnTiO0fenEPILLVESgGuU6p+5w+hx6m8DARLM20C5hs+QxYLVZDXRLGxnMplAr7a8tsh+QYqsgh1FUVkrBFJRihPn8goVhuF3Iuz8QpZuDcvhkIYiQIK5o4aVRYkykU3mqqAMAAlwe3t73+PkIpcnn1dqjNCLuY/M3jRND0R+C5TnRtPpFCHpTRLv7ih6nPbbQoAE87aQ3vLnRFH0IEmS95GhKzVuFVmF5eHhoV+t6RkMBmX71OVyuWo0GhGuU2gjglYhi8UClcxshbplO9334Ugwd3QHyFwXKMuhw+FsNoPPpDx1oO5H+EykShwcvlehaTQ1c1132Gw2J2r27R2FgtPWGAESjMbGecXUIKOAfkOZmjwnK5eVqxBEm75K03QXAtwQjqJg0901+l2bOQnmrllMma9sE4I+zqvV6jQIggYEnBBKns1m/1RyU9BcDNEf+lXusL3v4tRJMHfRasqcoyhqrVYrpPs3kZW7Wq2GbCx2x41q0PRJMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIARKMQcbkUoiAbgiQYHSzCOdDBAxCgARjkDG5FCKgGwIkGN0swvkQAYMQIMEYZEwuhQjohgAJRjeLcD5EwCAESDAGGZNLIQK6IUCC0c0inA8RMAgBEoxBxuRSiIBuCJBgdLMI50MEDEKABGOQMbkUIqAbAiQY3SzC+RABgxAgwRhkTC6FCOiGAAlGN4twPkTAIARIMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIARKMQcbkUoiAbgiQYHSzCOdDBAxCgARjkDG5FCKgGwIkGN0swvkQAYMQIMEYZEwuhQjohgAJRjeLcD5EwCAESDAGGZNLIQK6IUCC0c0inA8RMAgBEoxBxuRSiIBuCJBgdLMI50MEDEKABGOQMbkUIqAbAiQY3SzC+RABgxAgwRhkTC6FCOiGAAlGN4twPkTAIARIMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIARKMQcbkUoiAbgiQYHSzCOdDBAxCgARjkDG5FCKgGwIkGN0swvkQAYMQIMEYZEwuhQjohgAJRjeLcD5EwCAESDAGGZNLIQK6IUCC0c0inA8RMAgBEoxBxuRSiIBuCJBgdLMI50MEDEKABGOQMbkUIqAbAiQY3SzC+RABgxAgwRhkTC6FCOiGAAlGN4twPkTAIARIMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIARKMQcbkUoiAbgiQYHSzCOdDBAxCgARjkDG5FCKgGwIkGN0swvkQAYMQIMEYZEwuhQjohgAJRjeLcD5EwCAESDAGGZNLIQK6IUCC0c0inA8RMAgBEoxBxuRSiIBuCJBgdLMI50MEDEKABGOQMbkUIqAbAiQY3SzC+RABgxAgwRhkTC6FCOiGAAlGN4twPkTAIARIMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIARKMQcbkUoiAbgiQYHSzCOdDBAxCgARjkDG5FCKgGwIkGN0swvkQAYMQIMEYZEwuhQjohgAJRjeLcD5EwCAESDAGGZNLIQK6IUCC0c0inA8RMAgBEoxBxuRSiIBuCJBgdLMI50MEDEKABGOQMbkUIqAbAiQY3SzC+RABgxAgwRhkTC6FCOiGAAlGN4twPkTAIARIMAYZk0shArohQILRzSKcDxEwCAESjEHG5FKIgG4IkGB0swjnQwQMQoAEY5AxuRQioBsCJBjdLML5EAGDECDBGGRMLoUI6IYACUY3i3A+RMAgBEgwBhmTSyECuiFAgtHNIpwPETAIgf8BwPlyRbbMXCQAAAAASUVORK5CYII=&quot;) right top;">
     <canvas width="280" height="280" style="display: none;"></canvas></div>

        <script>
   function addWaterMarker(str){
	  var can = document.createElement('canvas');
	  var body = document.getElementById('watermark');

	  body.appendChild(can);

	 can.width=280;
	 can.height=280;
	 can.style.display='none';
	 var cans = can.getContext('2d');
	 cans.rotate(-20*Math.PI/180);
	 cans.font = "16px Microsoft JhengHei"; 
	 cans.fillStyle = "rgba(17, 17, 17, 0.20)";
	 cans.textAlign = 'left'; 
	 cans.textBaseline = 'Middle';
	 cans.fillText(str, can.width/3-40, can.height/2,200);
	 cans.fillText("e签宝版权所有请勿外传", can.width/3-40, can.height/2+40,200);
	 body.style = "pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background:url("+can.toDataURL("image/png")+") right top;"
   
   }
   </script>
  
  　    <script>
            var myDate = new Date();
　　     addWaterMarker("桃浪"+"-"+myDate.toLocaleDateString())
　    </script>
  </div>

    

    




    
    

    
    
    


    
<div id="space-tools-web-items" class="hidden">
                <div data-label="概览" data-href="/spaces/viewspacesummary.action?key=PRODUCT">概览</div>
            <div data-label="内容工具" data-href="/pages/reorderpages.action?key=PRODUCT">内容工具</div>
    </div>
        



            </div><!-- \#main -->
            
    
    
        
            
            

<div id="footer" role="contentinfo" style="margin-left: 393px;">
    <section class="footer-body">

                                                    
        

        <ul id="poweredby">
            <li class="noprint">基于 <a href="http://www.atlassian.com/software/confluence" class="hover-footer-link" rel="nofollow">Atlassian Confluence</a> <span id="footer-build-information">6.13.4</span> 技术构建</li>
            <li class="print-only">由 Atlassian 合流6.13.4 打印</li>
            <li class="noprint"><a href="https://support.atlassian.com/help/confluence" class="hover-footer-link" rel="nofollow">报告缺陷</a></li>
            <li class="noprint"><a href="http://www.atlassian.com/about/connected.jsp?s_kwcid=Confluence-stayintouch" class="hover-footer-link" rel="nofollow">Atlassian 新闻</a></li>
        </ul>

        

        <div id="footer-logo"><a href="http://www.atlassian.com/" rel="nofollow">Atlassian</a></div>

                    
        
    </section>
</div>

    
</div>

</div><!-- \#full-height-container -->
</div><!-- \#page -->

    <span style="display:none;" id="confluence-server-performance">{"serverDuration": 651, "requestCorrelationId": "aa49aebf7e506e64"}</span>


<script type="text/javascript">
    AJS.BigPipe = AJS.BigPipe || {};
    AJS.BigPipe.metrics = AJS.BigPipe.metrics || {};
    AJS.BigPipe.metrics.pageEnd = typeof window.performance !== "undefined" && typeof window.performance.now === "function"
                                    ? Math.ceil(window.performance.now()) : 0;
    AJS.BigPipe.metrics.isBigPipeEnabled = 'false' === 'true';
</script>


    
<div id="editor-preload-container" style="display: none;">
 

<div class="hidden">
        


<content tag="breadcrumbs">
    
    
    <ol id="quickedit-breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/display/PRODUCT" target="_blank">2.技术中心</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=PRODUCT" target="_blank">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=983056" target="_blank">技术中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=132752553" target="_blank">大前端</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184081815" target="_blank">公有云plus</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199123641" target="_blank">公有云前端详设汇总</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127405" target="_blank">公有云前端迭代</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199127410" target="_blank">基础能力组详设</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221511894" target="_blank">签署-2025Q3</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class="edited-page-title"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" target="_blank">********签署迭代详设</a></span>
                                                                    </li></ol>

</content>
</div>


        
    

                                                                                        

<script type="text/x-template" title="editor-css" id="editor-css-resources">
    <link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.0/_/download/batch/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-font/net.customware.confluence.plugin.vault:secenc-font.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-font" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-editor/net.customware.confluence.plugin.vault:secenc-editor.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-editor" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secure-macro-browser-override/net.customware.confluence.plugin.vault:secure-macro-browser-override.css" data-wrm-key="net.customware.confluence.plugin.vault:secure-macro-browser-override" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-typography/com.atlassian.auiplugin:aui-page-typography.css" data-wrm-key="com.atlassian.auiplugin:aui-page-typography" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-link/com.atlassian.auiplugin:aui-link.css" data-wrm-key="com.atlassian.auiplugin:aui-link" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-avatars/com.atlassian.auiplugin:aui-avatars.css" data-wrm-key="com.atlassian.auiplugin:aui-avatars" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/47a8861ddeabe407d6a865c6dde5d487-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-layout/com.atlassian.auiplugin:aui-page-layout.css" data-wrm-key="com.atlassian.auiplugin:aui-page-layout" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:editor-content-styles/com.atlassian.confluence.editor:editor-content-styles.css" data-wrm-key="com.atlassian.confluence.editor:editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-editor-content-styles/com.atlassian.confluence.editor:table-resizable-editor-content-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-styles/com.atlassian.confluence.editor:table-resizable-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-lozenge/com.atlassian.auiplugin:aui-lozenge.css" data-wrm-key="com.atlassian.auiplugin:aui-lozenge" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:view_content_status/com.atlassian.confluence.plugins.status-macro:view_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:view_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:editor_content_status/com.atlassian.confluence.plugins.status-macro:editor_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:editor_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/9daf7afbce8f12b08a5c226d5aed29c7-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.14/_/download/batch/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/1bf6e69c18341244d990250bf5aa3ce0-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.4.0/_/download/batch/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/a0e894d2f295b40fda5171460781b200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.0.3/_/download/batch/confluence.extra.attachments:attachments-css/confluence.extra.attachments:attachments-css.css" data-wrm-key="confluence.extra.attachments:attachments-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/14.2.1/_/download/batch/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:panel-styles/confluence.web.resources:panel-styles.css" data-wrm-key="confluence.web.resources:panel-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/17017df768625b4c50edd34ec564513c-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:content-styles/confluence.web.resources:content-styles.css" data-wrm-key="confluence.web.resources:content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css?conditionalComment=lte+IE+9" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="/s/5baeb58a5608e28c5f241eee74f064b2-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/4.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/5.1.4/_/download/batch/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources" data-wrm-batch-type="resource" media="all">

</script>













        

<div class="editor-container">

        
            

<div id="link-browser-tab-items" class="hidden">
                <div title="搜索" data-weight="10">search</div>
            <div title="最近浏览过" data-weight="20">recentlyviewed</div>
            <div title="文件" data-weight="30">attachments</div>
            <div title="Web链接" data-weight="40">weblink</div>
            <div title="高级" data-weight="50">advanced</div>
    </div>
            <div id="image-properties-tab-items" class="hidden">
                <div title="效果" data-weight="10">image-effects</div>
            <div title="标题" data-weight="20">image-attributes</div>
    </div>
            

 










<div id="toolbar">
    <div id="rte-toolbar" class="aui-toolbar aui-toolbar2">

        <div class="aui-toolbar2-primary toolbar-primary">
            <ul class="aui-buttons rte-toolbar-group-formatting">
                            <li id="format-dropdown" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="format-dropdown-display" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-control-id="formatselect">
                            <span class="dropdown-text">正文</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <ul id="format-dropdown-display-menu" class="aui-dropdown hidden">
                            <li class="dropdown-item format-p" data-format="p" data-tooltip="正文 (Ctrl+0)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">正文</a>
</li>
                                <li class="dropdown-item format-h1" data-format="h1" data-tooltip="标题 1 (Ctrl+1)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 1</a>
</li>
                                <li class="dropdown-item format-h2" data-format="h2" data-tooltip="标题 2 (Ctrl+2)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 2</a>
</li>
                                <li class="dropdown-item format-h3" data-format="h3" data-tooltip="标题 3 (Ctrl+3)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 3</a>
</li>
                                <li class="dropdown-item format-h4" data-format="h4" data-tooltip="标题 4 (Ctrl+4)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 4</a>
</li>
                                <li class="dropdown-item format-h5" data-format="h5" data-tooltip="标题 5 (Ctrl+5)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 5</a>
</li>
                                <li class="dropdown-item format-h6" data-format="h6" data-tooltip="标题 6 (Ctrl+6)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 6</a>
</li>
                                <li class="dropdown-item format-pre" data-format="pre" data-tooltip="预格式化 (Ctrl+7)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">预格式化</a>
</li>
                                <li class="dropdown-item format-blockquote" data-format="blockquote" data-tooltip="引用 (Ctrl+8)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">引用</a>
</li>
                        </ul>
                    </div>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-style">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bold" data-tooltip="粗体 (Ctrl+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bold">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-bold ">粗体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-italic" data-tooltip="斜体 (Ctrl+I)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="italic">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-italic ">斜体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-underline" data-tooltip="下划线 (Ctrl+U)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="underline">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-underline ">下划线</span>
    </a>
</li>
                            <li id="color-picker-control" class="toolbar-item toolbar-splitbutton">
                    <a class="toolbar-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color" data-color="003366" data-tooltip="颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-editor-color ">颜色选取器</span><span class="selected-color"></span></a><div class="aui-dd-parent"><a class="toolbar-trigger aui-dd-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color-selector" data-control-id="colorSelector" data-tooltip="更多颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-dropdown ">更多颜色</span></a><div class="color-picker-container"><div class="color-picker aui-dropdown hidden"><ul><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黑色" data-tooltip="黑色" style="background-color: #000000" data-color="000000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橙黄色" data-tooltip="深橙黄色" style="background-color: #993300" data-color="993300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橄榄绿色" data-tooltip="深橄榄绿色" style="background-color: #333300" data-color="333300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深绿色" data-tooltip="深绿色" style="background-color: #003300" data-color="003300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="藏青色" data-tooltip="藏青色" style="background-color: #003366" data-color="003366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海蓝色" data-tooltip="海蓝色" style="background-color: #000080" data-color="000080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="靛蓝色" data-tooltip="靛蓝色" style="background-color: #333399" data-color="333399">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深灰色" data-tooltip="深灰色" style="background-color: #333333" data-color="333333">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="褐红色" data-tooltip="褐红色" style="background-color: #800000" data-color="800000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橙色" data-tooltip="橙色" style="background-color: #FF6600" data-color="FF6600">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橄榄绿色" data-tooltip="橄榄绿色" style="background-color: #808000" data-color="808000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿色" data-tooltip="绿色" style="background-color: #008000" data-color="008000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝绿色" data-tooltip="蓝绿色" style="background-color: #008080" data-color="008080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝色" data-tooltip="蓝色" style="background-color: #0000FF" data-color="0000FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰蓝色" data-tooltip="灰蓝色" style="background-color: #666699" data-color="666699">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰色" data-tooltip="灰色" style="background-color: #7A869A" data-color="7A869A">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红色" data-tooltip="红色" style="background-color: #FF0000" data-color="FF0000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="琥珀色" data-tooltip="琥珀色" style="background-color: #FF9900" data-color="FF9900">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄绿色" data-tooltip="黄绿色" style="background-color: #99CC00" data-color="99CC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海绿色" data-tooltip="海绿色" style="background-color: #339966" data-color="339966">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="青绿色" data-tooltip="青绿色" style="background-color: #33CCCC" data-color="33CCCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="宝蓝色" data-tooltip="宝蓝色" style="background-color: #3366FF" data-color="3366FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="紫色" data-tooltip="紫色" style="background-color: #800080" data-color="800080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="中灰色" data-tooltip="中灰色" style="background-color: #A5ADBA" data-color="A5ADBA">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="洋红色" data-tooltip="洋红色" style="background-color: #FF00FF" data-color="FF00FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="金色" data-tooltip="金色" style="background-color: #FFCC00" data-color="FFCC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄色" data-tooltip="黄色" style="background-color: #FFFF00" data-color="FFFF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿黄色" data-tooltip="绿黄色" style="background-color: #00FF00" data-color="00FF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="湖绿色" data-tooltip="湖绿色" style="background-color: #00FFFF" data-color="00FFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="天蓝色" data-tooltip="天蓝色" style="background-color: #00CCFF" data-color="00CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红紫色" data-tooltip="红紫色" style="background-color: #993366" data-color="993366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅灰色" data-tooltip="浅灰色" style="background-color: #C1C7D0" data-color="C1C7D0">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="粉色" data-tooltip="粉色" style="background-color: #FF99CC" data-color="FF99CC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="桃红色" data-tooltip="桃红色" style="background-color: #FFCC99" data-color="FFCC99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅黄色" data-tooltip="浅黄色" style="background-color: #FFFF99" data-color="FFFF99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅绿色" data-tooltip="浅绿色" style="background-color: #CCFFCC" data-color="CCFFCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅蓝绿色" data-tooltip="浅蓝绿色" style="background-color: #CCFFFF" data-color="CCFFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅天蓝色" data-tooltip="浅天蓝色" style="background-color: #99CCFF" data-color="99CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绛紫色" data-tooltip="绛紫色" style="background-color: #CC99FF" data-color="CC99FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="白色" data-tooltip="白色" style="background-color: #FFFFFF" data-color="FFFFFF">&nbsp;</a></li></ul></div></div></div>
                </li>
                <li id="more-menu" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="rte-button-more" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-tooltip="更多">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-styles ">格式</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <div id="rte-button-more-menu" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <ul>
                                                        <li class="dropdown-item more-menu-trigger" data-control-id="strikethrough" data-tooltip="删除线 (Ctrl+Shift+S)">
    <a id="rte-strikethrough" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
删除线
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sub" data-tooltip="">
    <a id="rte-sub" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
下标
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sup" data-tooltip="">
    <a id="rte-sup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
上标
    </a>
</li>
                                                    <li class="dropdown-item more-menu-trigger" data-control-id="monospace" data-tooltip="用等宽字体格式化文本">
    <a id="rte-monospace" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
等宽
    </a>
</li>

                                                                                                                </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul>
                                    <li class="dropdown-item more-menu-trigger no-icon" data-format="removeformat" data-tooltip="当前选中文本清除格式">
<a id="rte-removeformat" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    清除格式
</a>
</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-lists">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bullist" data-tooltip="无序列表 (Ctrl+Shift+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bullist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-bullet ">无序列表</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-numlist" data-tooltip="有序列表 (Ctrl+Shift+N)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="numlist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-number ">有序列表</span>
    </a>
</li>
            </ul>
                            <ul class="aui-buttons rte-toolbar-group-task-lists">
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-tasklist" data-tooltip="任务列表 (tinymce.confluence.layout.three_col=三栏)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="tasklist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-task ">任务列表</span>
    </a>
</li>
                </ul>
            
            <ul class="aui-buttons rte-toolbar-group-indentation">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-outdent" data-tooltip="减小缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="outdent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-left ">减小缩进</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-indent" data-tooltip="增大缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="indent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-right ">增大缩进</span>
    </a>
</li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-justification">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyleft" data-tooltip="左对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyleft">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-left ">左对齐</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifycenter" data-tooltip="居中">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifycenter">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-center ">居中</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyright" data-tooltip="右对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyright">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-right ">右对齐</span>
    </a>
</li>
            </ul>

                            <ul class="aui-buttons hidden" id="page-layout-2-group">
                    <li id="page-layout-2" class="toolbar-item" data-tooltip="页面布局">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="aui-button aui-button-subtle toolbar-trigger" id="rte-button-pagelayout-2">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-layout ">页面布局</span>
                        </a>
                    </li>
                </ul>
            

            <ul class="aui-buttons rte-toolbar-group-files hidden"></ul>

            <ul class="aui-buttons rte-toolbar-group-link no-separator">
                <li class="toolbar-item" data-tooltip="插入链接 (Ctrl+K)">
                    <a id="rte-button-link" class="toolbar-trigger aui-button aui-button-subtle" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="linkbrowserButton">
                                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
                        <span class="trigger-text">链接</span>
                    </a>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-table no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-table-dropdown">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert-table" data-tooltip="插入表格">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-table "></span>
                            <span class="dropdown-text">表格</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="table-picker-container" class="hidden aui-box-shadow">
                            <div class="table-picker-box" data-tooltip="按住 SHIFT键，创建无表头表格 。">
                                <div class="table-picker-background">
                                    <div class="picker picker-cell"></div>
                                    <div class="picker picker-heading heading"></div>
                                    <div class="picker picker-selected-cell"></div>
                                    <div class="picker picker-selected-heading heading"></div>
                                </div>
                                <p class="desc"></p>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                                    
            
            <ul class="aui-buttons rte-toolbar-group-insert no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-menu">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert" data-tooltip="插入更多内容">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-add "></span>
                            <span class="dropdown-text">插入</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="insert-menu-options" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <span class="assistive">插入内容</span>
                                <ul id="content-insert-list">
                                    
        
                <li class="dropdown-item content-image" data-command="mceConfimage" data-tooltip="插入文件和图片 (Ctrl+M)">
<a id="rte-insert-image" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-image "></span>
 文件和图片
</a>
</li>
                                            
        
                <li class="dropdown-item content-link" data-control-id="linkbrowserButton" data-tooltip="插入链接 (Ctrl+K)">
<a id="rte-insert-link" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
 链接
</a>
</li>
                                            
        
                <li class="dropdown-item content-wikimarkup" data-command="InsertWikiMarkup" data-tooltip="插入Wiki标记 (Ctrl+Shift+D)">
<a id="rte-insert-wikimarkup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-code "></span>
 Wiki标记
</a>
</li>
                                            
    
                <li class="dropdown-item content-hr" data-command="InsertHorizontalRule" data-tooltip="插入水平线(----)">
<a id="rte-insert-hr" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-horizontal-rule "></span>
 水平线
</a>
</li>
                                                                                
        
                <li class="dropdown-item content-tasklist" data-command="InsertInlineTaskListNoToggle" data-tooltip="插入任务列表 (tinymce.propertypanel.images.link.remove.tooltip=删除图片链接)">
<a id="rte-insert-tasklist" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-task "></span>
 任务列表
</a>
</li>
                                                                            
        
                <li class="dropdown-item content-date" data-command="confMenuInsertDate" data-tooltip="插入日期 (/ then /)">
<a id="rte-insert-date" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-calendar "></span>
 日期
</a>
</li>
                                            
    
                <li class="dropdown-item content-emoticon" data-command="mceEmotion" data-tooltip="插入表情">
<a id="rte-insert-emoticon" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-emoji "></span>
 表情符号
</a>
</li>
                                            
    
                <li class="dropdown-item content-symbol" data-command="confCharmap" data-tooltip="插入符号">
<a id="rte-insert-symbol" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-symbol "></span>
 符号
</a>
</li>
                                    </ul>
                                <span class="assistive">插入宏</span>
                                <ul id="macro-insert-list">
                                                                                                                                                                                                <li class="dropdown-item macro-insertmention-button" data-macro-name="insertmention-button" data-tooltip="插入&#39;用户提及&#39;宏">
<a id="insertmention-button" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-mention "></span>
 用户提及
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-jiralink" data-macro-name="jiralink" data-tooltip="插入&#39;Jira问题/过滤器&#39;宏">
<a id="jiralink" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-jira "></span>
 Jira问题/过滤器
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-info" data-macro-name="info" data-tooltip="插入&#39;信息&#39;宏">
<a id="info" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-info-filled "></span>
 信息
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-status" data-macro-name="status" data-tooltip="插入&#39;状态&#39;宏">
<a id="status" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                        
    
    <span class="icon confluence-icon-status-macro"></span>
 状态
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-gallery" data-macro-name="gallery" data-tooltip="插入&#39;画廊&#39;宏">
<a id="gallery" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-gallery "></span>
 画廊
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-toc" data-macro-name="toc" data-tooltip="插入&#39;目录&#39;宏">
<a id="toc" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-overview "></span>
 目录
</a>
</li>
                                                                    </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul id="more-macros-list">
                                    
        
                <li class="dropdown-item content-macro" data-command="mceConfMacroBrowser" data-tooltip="打开宏浏览器 (Ctrl+Shift+A)">
<a id="rte-insert-macro" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    其它宏
</a>
</li>
                                    </ul>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                            <ul class="aui-buttons rte-toolbar-group-page-layouts-section-types">
                    <li id="pagelayout-menu" class="toolbar-item toolbar-dropdown">
                        <div class="aui-dd-parent">
                            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-pagelayout" data-tooltip="页面布局">
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-default">页面布局</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                            </a>

                            <ul id="pagelayout-menu-options" class="aui-dropdown hidden">
                                <li class="dropdown-item" data-tooltip="无布局">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-none&quot;, &quot;columns&quot;: 0   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-none">无布局</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple">两栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-left">两栏 (简单，左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-right">两栏 (简单，右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-simple">三栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two">两栏</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-left">两栏 (左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-right">两栏 (右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三列">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three">三列</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (左边和右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-sidebars&quot;, &quot;columns&quot;: [&quot;sidebars&quot;, &quot;large&quot;, &quot;sidebars&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-sidebars">三栏 (左边和右侧栏)</span>
    </a>
</li>
                            </ul>
                        </div>
                    </li>
                </ul>
            
                        
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-undo">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-undo" data-tooltip="回退 (Ctrl+Z)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="undo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-undo ">回退</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-redo" data-tooltip="重做 (Ctrl+Y)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="redo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-redo ">重做</span>
    </a>
</li>
            </ul>
        </div>                                                    <div id="draft-status" style="display:none"></div>
                <div class="aui-toolbar2-secondary">
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-searchreplace">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-searchreplace" data-tooltip="查找/替换">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="searchreplace">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-search ">查找/替换</span>
    </a>
</li>
            </ul>
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-help">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-help" data-tooltip="帮助">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="help">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-help ">键盘快捷方式帮助</span>
    </a>
</li>
            </ul>
        </div>    </div></div>


                <div id="editor-notifications-container"><div id="all-messages"><div id="action-messages"></div></div></div><div id="editor-precursor"><div class="cell"><div class="aui-buttons aui-toolbar2"><button class="aui-button aui-button-subtle rte-button-labels" type="button" data-tooltip="标签" id="rte-button-labels" data-explicit-restrictions="" data-inherited-restrictions=""><span class="icon aui-icon aui-icon-small aui-iconfont-devtools-tag"></span></button><button class="aui-button aui-button-subtle rte-button-restrictions" type="button" data-tooltip="未限制" id="rte-button-restrictions" data-explicit-restrictions="false" data-inherited-restrictions="false"><span class="icon aui-icon aui-icon-small aui-iconfont-unlocked"></span></button></div><div id="content-title-div" class="collaborative"><input type="text" name="title" id="content-title" tabindex="1" class="text pagetitle" autocomplete="off" value="********签署迭代详设" placeholder="页面标题"></div></div></div>
    
<div id="wysiwyg">
    <div id="rte" class="cell editor-default collaborative">
        <textarea id="wysiwygTextarea" name="wysiwygContent" class="hidden tinymce-editor"></textarea>
    </div>
</div>
<div id="editor-html-source-container" class="hidden">
    <textarea id="editor-html-source" class="monospaceInput"></textarea>
</div>

<div id="preview">
    <div id="previewArea" class="cell">
    </div>
</div>

    <div id="savebar-container"><div id="rte-savebar" class="aui-toolbar aui-toolbar2"><span id="watermark_span" disabled="" style="float:right;display:none"> <input id="watermark_checkbox" onchange="sendCheckboxStatus(this)" type="checkbox"> <font id="add_watermark"> </font>  </span><div class="toolbar-split toolbar-split-row"><div class="toolbar-split toolbar-split-left"><div class="aui-buttons"></div></div><div class="toolbar-split toolbar-split-right"><div id="pluggable-status-container" class="toolbar-item rte-toolbar-pluggable-status"><div id="pluggable-status" class="synchrony"><div class="synchrony-status-indicator"><div class="status-indicator-icon aui-icon aui-icon-small aui-iconfont-devtools-task-in-progress" data-tooltip="自动保存所有修改到草稿中"></div><div class="status-indicator-message" data-tooltip="自动保存所有修改到草稿中">连接中...</div></div></div></div><div class="aui-buttons" id="rte-savebar-tinymce-plugin-point"></div><div class="aui-buttons"><span id="rte-spinner" class="toolbar-item shared-drafts">&nbsp;</span></div><div class="aui-buttons toolbar-group-edit assistive"><button id="rte-button-edit" class="aui-button" title="返回编辑模式" type="button"><span class="trigger-text">编辑</span></button></div><div class="aui-buttons toolbar-group-preview toolbar-group-preview-page toolbar-group-preview-shared-draft"></div><div class="save-button-container"><button class="aui-button aui-button-primary" type="submit" id="rte-button-publish" name="confirm" value="Save" title="保存"><span class="trigger-text">保存</span></button></div><div class="aui-buttons cancel-button-container-shared-draft"><button class="aui-button" type="submit" id="rte-button-cancel" name="cancel" value="cancel">取消</button></div><div class="aui-buttons toolbar-group-preview toolbar-group-ellipsis"><button class="aui-button toolbar-item aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" id="rte-button-ellipsis" type="button" resolved="" aria-controls="rte-ellipsis-menu" aria-expanded="false"><span class="aui-icon aui-icon-small aui-iconfont-more"></span></button></div><div id="rte-ellipsis-menu" data-aui-alignment="top auto" class="aui-style-default aui-dropdown2 aui-layer" resolved="" aria-hidden="true"><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-preview">预览</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-changes">查看更改</a></li></ul></div><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-revert">恢复到最新已发布版本</a></li></ul></div></div></div></div></div></div>

    <section role="dialog" id="quit-editor-dialog" class="aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true"><header class="aui-dialog2-header"></header><div class="aui-dialog2-content"></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button id="qed-publish-button" class="aui-button aui-button-primary update">更新</button><button id="qed-discard-button" title="丢弃所有未发布的变更" class="aui-button toolbar-item exit">恢复页面</button><button id="qed-save-exit-button" class="aui-button aui-button-primary exit">保留草稿</button><button id="qed-close-button" class="aui-button toolbar-item">取消</button></div></footer></section>

    
</div>



<script type="text/x-template" title="dynamic-editor-metadata" id="dynamic-editor-metadata-template">
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.108">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="221518426">
            <meta name="ajs-draft-share-id" content="52567c9b-818f-4cd3-9b30-4dffc93b2837">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTgwZDE1Y2FhMDE4MWY1OTc2YzE4MDA1MCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjIxNTE4NDI1IjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMjE1MTg0MjUtdGl0bGUiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE4MGQxNWNhYTAxODFmNTk3NmMxODAwNTAifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTUzMjQ5Mjc5XC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0YW9sYW5nIiwiZnVsbG5hbWUiOiLmoYPmtaoifSwiaXNzIjoiU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZiIsImV4cCI6MTc1Mzg2NzUwNywiaWF0IjoxNzUzNzgxMTA3fQ.XoNrvqwn0qGYpC_Eeu_7hmlBqLn0dWbwdAmwyZdPhEs">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="1753866607">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    </script>

<script type="text/x-template" title="tableForm" id="table-form-template">
    <form id="tinymce-table-form" class="aui">
        <div class="field-group">
            <label for="rows">行</label>
            <input id="rows" name="rows" type="text" size="3" autocomplete="off" value="{0}">
        </div>
        <div class="field-group">
            <label for="cols">列</label>
            <input id="cols" name="cols" type="text" size="3" autocomplete="off" value="{1}">
        </div>
        <div class="field-group hidden">
            <input id="width" type="hidden" name="width" value="">
            <label for="width">宽</label>
        </div>
        <div class="group">
            <div class="checkbox">
                <input id="table-heading-checkbox" class="checkbox" type="checkbox" name="heading" checked="checked" value="true">
                <label for="table-heading-checkbox">首行设为表头</label>
            </div>
        </div>
        <div class="group hidden">
            <div class="checkbox">
                <input id="table-equal-width-columns-checkbox" class="checkbox" type="checkbox" name="equal-width-columns" value="false">
                <label for="table-equal-width-columns-checkbox">等宽列</label>
            </div>
        </div>
    </form>
</script>
<input type="hidden" name="draftId" value="221518426" id="draftId"><input type="hidden" name="originalVersion" value="36" id="originalVersion">
<input type="hidden" name="syncRev" value="23.ThpyzfOVUHwzzLYQZw44E6c.15" id="syncRev">    <input type="hidden" name="atl_token" value="6b61646b9dbdaf0fc75a4e23b0584a541bf8bea5">
</div><div id="content-hover-0" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="content-hover-1" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div class="confluence-page-loading-errors"></div><div class="confluence-page-loading-blanket aui-blanket" aria-hidden="false" style="display: none;"><div class="confluence-loading-indicator"><aui-spinner filled="" size="small" style="color: rgb(240, 240, 240);" resolved=""><div class="aui-spinner spinner"><svg focusable="false" size="20" height="20" width="20" viewBox="0 0 20 20" style="top: 40px;"><circle cx="10" cy="10" r="9"></circle></svg></div></aui-spinner></div></div><div id="inline-dialog-selection-action-panel" class="aui-inline-dialog" style="left: 1309.81px; right: auto; top: 1593.31px; display: none;"><div class="aui-inline-dialog-contents contents" unselectable="on" style="width: auto; max-height: 726px;"><button data-key="com.atlassian.confluence.plugins.confluence-inline-comments:create-inline-comment" class="aui-button aui-button-compact aui-button-subtle" original-title="Add inline comment" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-comment"></span></button><button data-key="com.atlassian.confluence.plugins.confluence-jira-content:create-Jira-issue-summary" class="aui-button aui-button-compact aui-button-subtle" original-title="Create Jira issue" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-jira"></span></button></div><div id="arrow-selection-action-panel" class="aui-inline-dialog-arrow arrow aui-css-arrow aui-bottom-arrow" unselectable="on" style="position: absolute; left: 31.5px; right: auto; top: 34px;"></div></div><div id="action-dialog-target" style="top: 1635.31px; height: 17px; left: 1309.81px; width: 236.422px; display: none;" class=""></div></body><div id="pvtMessageDiv" class="pvt-message-div"></div></html>