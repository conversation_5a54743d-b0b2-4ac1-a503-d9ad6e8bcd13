# 【20250807签署迭代】测试用例

## 功能测试

### 法定代表人授权流程优化

#### TL-法人授权线上授权流程验证

##### PD-前置条件：用户已登录SaaS系统；具有法人授权权限；系统环境正常；

##### 步骤一：进入印章管理页面

##### 步骤二：点击申请法人印章

##### 步骤三：选择在线授权方式

##### 步骤四：填写法定代表人身份证号"110101199001011234"

##### 步骤五：填写法定代表人手机号"13812345678"

##### 步骤六：点击发起授权申请

##### 步骤七：验证授权书预览功能

##### ER-预期结果：1：成功进入法人授权页面；2：表单验证通过；3：授权申请发起成功；4：授权书可正常预览；5：页面步骤条显示为3步；

#### TL-法人授权纸质版授权流程验证

##### PD-前置条件：用户已登录SaaS系统；具有法人授权权限；准备好身份证正反面照片；

##### 步骤一：进入印章管理页面

##### 步骤二：点击申请法人印章

##### 步骤三：选择纸质版授权方式

##### 步骤四：填写法定代表人身份证号"110101199001011234"

##### 步骤五：上传授权书文件

##### 步骤六：上传身份证正面照片

##### 步骤七：上传身份证反面照片

##### 步骤八：点击提交申请

##### ER-预期结果：1：成功进入纸质版授权页面；2：文件上传成功；3：申请提交成功；4：进入等待审核状态；

#### TL-法人授权表单验证功能测试

##### PD-前置条件：用户已登录SaaS系统；进入法人授权页面；

##### 步骤一：不填写任何信息直接点击提交

##### 步骤二：填写错误格式的身份证号"123456"

##### 步骤三：填写错误格式的手机号"123"

##### 步骤四：填写正确身份证号但与系统不一致

##### 步骤五：验证错误提示显示方式

##### ER-预期结果：1：显示"请填写身份证号"错误提示；2：显示"身份证号格式错误"提示；3：显示"手机号格式错误"提示；4：显示证件号不一致引导弹窗；5：错误提示在输入框底部显示；

#### TL-法人授权等待页面查看材料功能

##### PD-前置条件：用户已提交纸质版法人授权申请；进入等待审核页面；

##### 步骤一：点击查看材料按钮

##### 步骤二：验证弹窗显示内容

##### 步骤三：点击授权书预览

##### 步骤四：点击身份证正面预览

##### 步骤五：点击身份证反面预览

##### ER-预期结果：1：弹窗正常打开；2：显示授权书和身份证缩略图；3：PDF文件可正常预览；4：身份证照片可正常放大查看；5：预览功能正常工作；

### SaaS印章图片上传原图截图

#### TL-印章图片上传原图功能验证

##### PD-前置条件：用户已登录SaaS系统；具有印章管理权限；准备好印章图片文件；

##### 步骤一：进入印章管理页面

##### 步骤二：点击上传印章图片

##### 步骤三：选择印章图片文件

##### 步骤四：确认上传

##### 步骤五：验证原图保存功能

##### 步骤六：验证截图功能

##### ER-预期结果：1：图片上传成功；2：原图正确保存；3：自动生成截图版本；4：图片质量符合要求；5：支持常见图片格式；

#### TL-印章图片格式兼容性测试

##### PD-前置条件：用户已登录SaaS系统；准备不同格式的图片文件；

##### 步骤一：上传PNG格式印章图片

##### 步骤二：上传JPG格式印章图片

##### 步骤三：上传GIF格式印章图片

##### 步骤四：上传超大尺寸图片

##### 步骤五：上传超小尺寸图片

##### ER-预期结果：1：PNG格式正常上传；2：JPG格式正常上传；3：GIF格式按规则处理；4：大尺寸图片自动压缩；5：小尺寸图片提示或拒绝；

### 强制阅读到底二次确认

#### TL-强制阅读二次确认弹窗显示

##### PD-前置条件：用户进入签署页面；配置了强制阅读二次确认；文档已阅读到底；

##### 步骤一：滚动文档到底部

##### 步骤二：点击提交签署按钮

##### 步骤三：验证二次确认弹窗显示

##### 步骤四：点击确认按钮

##### 步骤五：验证签署流程继续

##### ER-预期结果：1：文档可正常滚动；2：二次确认弹窗正常显示；3：弹窗文案内容正确；4：确认后签署流程正常进行；

#### TL-自定义二次确认文案功能

##### PD-前置条件：管理员已配置自定义确认文案；用户进入签署页面；

##### 步骤一：完成文档阅读

##### 步骤二：点击提交签署

##### 步骤三：验证弹窗显示自定义文案

##### 步骤四：验证文案内容准确性

##### ER-预期结果：1：弹窗显示配置的自定义文案；2：文案内容与配置一致；3：弹窗样式正常；4：功能逻辑正确；

### 批量签接口支持可不传入姓名

#### TL-批量签未实名用户不传姓名测试

##### PD-前置条件：存在未实名且未指定姓名的签署流程；用户具有批量签权限；

##### 步骤一：调用批量签接口

##### 步骤二：不传入签署人姓名参数

##### 步骤三：验证接口响应

##### 步骤四：验证签署链接生成

##### 步骤五：访问签署链接进行签署

##### ER-预期结果：1：接口调用成功；2：返回有效的签署链接；3：签署页面正常显示；4：可完成实名认证和签署；

#### TL-批量签混合场景测试

##### PD-前置条件：存在已实名和未实名的混合签署流程；

##### 步骤一：创建包含已实名用户的流程

##### 步骤二：创建包含未实名用户的流程

##### 步骤三：调用批量签接口

##### 步骤四：验证不同用户的处理逻辑

##### ER-预期结果：1：已实名用户直接进入签署；2：未实名用户先进行实名认证；3：批量签流程正常执行；4：所有用户最终完成签署；

## 边界测试

### 数据边界验证

#### TL-身份证号边界值测试

##### PD-前置条件：用户进入法人授权页面；

##### 步骤一：输入17位身份证号

##### 步骤二：输入19位身份证号

##### 步骤三：输入包含特殊字符的身份证号

##### 步骤四：输入全数字但不符合校验规则的身份证号

##### ER-预期结果：1：17位提示长度不足；2：19位提示长度超限；3：特殊字符提示格式错误；4：校验失败提示身份证号无效；

#### TL-手机号边界值测试

##### PD-前置条件：用户进入法人授权页面；

##### 步骤一：输入10位手机号

##### 步骤二：输入12位手机号

##### 步骤三：输入以非1开头的11位号码

##### 步骤四：输入包含字母的手机号

##### ER-预期结果：1：10位提示长度不足；2：12位提示长度超限；3：非1开头提示格式错误；4：包含字母提示格式错误；

### 文件上传边界测试

#### TL-印章图片文件大小边界测试

##### PD-前置条件：用户进入印章上传页面；

##### 步骤一：上传1KB的图片文件

##### 步骤二：上传10MB的图片文件

##### 步骤三：上传50MB的图片文件

##### 步骤四：上传空文件

##### ER-预期结果：1：1KB文件提示过小或正常处理；2：10MB文件正常上传；3：50MB文件提示超限或自动压缩；4：空文件提示错误；

## 异常测试

### 网络异常场景

#### TL-网络中断时的授权申请

##### PD-前置条件：用户正在进行法人授权申请；

##### 步骤一：填写完整的授权信息

##### 步骤二：断开网络连接

##### 步骤三：点击提交申请

##### 步骤四：恢复网络连接

##### 步骤五：重新提交申请

##### ER-预期结果：1：网络中断时显示连接错误；2：提供重试机制；3：网络恢复后可正常提交；4：数据不丢失；

#### TL-服务器异常时的印章上传

##### PD-前置条件：用户准备上传印章图片；服务器出现异常；

##### 步骤一：选择印章图片文件

##### 步骤二：点击上传

##### 步骤三：服务器返回500错误

##### 步骤四：验证错误处理机制

##### ER-预期结果：1：显示友好的错误提示；2：提供重试选项；3：不影响其他功能使用；4：错误信息记录到日志；

### 数据异常场景

#### TL-无效文件格式上传测试

##### PD-前置条件：用户进入文件上传页面；

##### 步骤一：尝试上传.exe文件

##### 步骤二：尝试上传.txt文件

##### 步骤三：尝试上传损坏的图片文件

##### 步骤四：验证系统处理方式

##### ER-预期结果：1：.exe文件被拒绝上传；2：.txt文件提示格式不支持；3：损坏文件提示文件无效；4：系统保持稳定运行；

## 性能测试

### 响应时间测试

#### TL-法人授权申请响应时间

##### PD-前置条件：系统正常运行；用户已准备好测试数据；

##### 步骤一：记录开始时间

##### 步骤二：提交法人授权申请

##### 步骤三：记录响应时间

##### 步骤四：验证响应时间是否在可接受范围

##### ER-预期结果：1：授权申请响应时间<3秒；2：页面加载流畅；3：用户体验良好；

#### TL-印章图片上传性能测试

##### PD-前置条件：准备不同大小的图片文件；

##### 步骤一：上传1MB图片文件

##### 步骤二：上传5MB图片文件

##### 步骤三：记录上传时间

##### 步骤四：验证处理速度

##### ER-预期结果：1：1MB图片上传时间<5秒；2：5MB图片上传时间<15秒；3：上传过程有进度提示；

### 并发性能测试

#### TL-多用户同时申请法人授权

##### PD-前置条件：准备10个测试账号；系统资源充足；

##### 步骤一：10个用户同时登录

##### 步骤二：同时发起法人授权申请

##### 步骤三：监控系统响应

##### 步骤四：验证所有申请处理结果

##### ER-预期结果：1：所有用户申请正常处理；2：系统响应时间稳定；3：无数据冲突；4：系统运行稳定；

## 安全测试

### 权限验证测试

#### TL-未授权用户访问法人授权功能

##### PD-前置条件：用户已登录但无法人授权权限；

##### 步骤一：直接访问法人授权页面URL

##### 步骤二：尝试调用法人授权接口

##### 步骤三：验证系统权限控制

##### ER-预期结果：1：页面访问被拒绝；2：接口调用返回权限错误；3：跳转到权限提示页面；

#### TL-文件上传安全验证

##### PD-前置条件：用户具有文件上传权限；

##### 步骤一：尝试上传包含脚本的图片文件

##### 步骤二：尝试上传超大文件

##### 步骤三：验证文件内容安全检查

##### ER-预期结果：1：恶意文件被拒绝；2：文件大小限制生效；3：安全检查机制正常；

### 数据安全测试

#### TL-敏感信息传输加密验证

##### PD-前置条件：用户进行法人授权操作；

##### 步骤一：填写身份证号和手机号

##### 步骤二：提交表单数据

##### 步骤三：抓包分析数据传输

##### 步骤四：验证数据加密情况

##### ER-预期结果：1：敏感数据已加密传输；2：使用HTTPS协议；3：无明文敏感信息泄露；

## 兼容性测试

### 多端兼容性测试

#### TL-PC端法人授权功能兼容性

##### PD-前置条件：准备不同浏览器环境；

##### 步骤一：在Chrome浏览器中测试法人授权

##### 步骤二：在Firefox浏览器中测试

##### 步骤三：在Safari浏览器中测试

##### 步骤四：在Edge浏览器中测试

##### ER-预期结果：1：Chrome浏览器功能正常；2：Firefox浏览器功能正常；3：Safari浏览器功能正常；4：Edge浏览器功能正常；

#### TL-移动端签署功能兼容性

##### PD-前置条件：准备不同移动设备；

##### 步骤一：在iOS设备上测试签署功能

##### 步骤二：在Android设备上测试

##### 步骤三：在不同屏幕尺寸设备上测试

##### ER-预期结果：1：iOS设备功能正常；2：Android设备功能正常；3：不同尺寸屏幕适配良好；

### 多设备兼容性测试

#### TL-不同分辨率设备显示测试

##### PD-前置条件：准备不同分辨率的测试环境；

##### 步骤一：在1920x1080分辨率下测试

##### 步骤二：在1366x768分辨率下测试

##### 步骤三：在移动设备分辨率下测试

##### ER-预期结果：1：高分辨率显示正常；2：低分辨率显示适配；3：移动端显示友好；

## 功能闭环场景测试

### 法人授权完整流程闭环

#### TL-新用户法人授权完整流程验证

##### PD-前置条件：新注册用户；未进行过法人授权；系统环境正常；

##### 步骤一：用户登录SaaS系统

##### 步骤二：进入印章管理模块

##### 步骤三：点击申请法人印章

##### 步骤四：选择在线授权方式

##### 步骤五：填写完整的法定代表人信息

##### 步骤六：提交授权申请

##### 步骤七：等待系统审核

##### 步骤八：审核通过后获得法人印章

##### 步骤九：使用法人印章进行签署

##### ER-预期结果：1：用户成功登录系统；2：正确进入印章管理页面；3：授权申请流程顺畅；4：信息填写和验证正确；5：申请提交成功；6：审核流程正常；7：印章生成成功；8：印章可正常使用；9：整个流程用户体验良好；

#### TL-老用户重新申请法人授权流程

##### PD-前置条件：已有法人授权但需重新申请；用户已登录系统；

##### 步骤一：进入印章管理页面

##### 步骤二：查看当前法人授权状态

##### 步骤三：点击重新申请法人授权

##### 步骤四：系统自动填充已有信息

##### 步骤五：确认或修改授权信息

##### 步骤六：提交重新申请

##### 步骤七：完成重新授权流程

##### ER-预期结果：1：正确显示当前授权状态；2：重新申请入口明确；3：历史信息正确回填；4：信息修改功能正常；5：重新申请提交成功；6：新授权覆盖旧授权；7：授权状态更新正确；

### 印章管理完整业务闭环

#### TL-印章从创建到使用的完整流程

##### PD-前置条件：用户具有印章管理权限；已完成法人授权；

##### 步骤一：创建新的印章

##### 步骤二：上传印章图片

##### 步骤三：设置印章使用权限

##### 步骤四：提交印章审核

##### 步骤五：印章审核通过

##### 步骤六：在签署流程中使用印章

##### 步骤七：验证印章签署效果

##### 步骤八：查看印章使用记录

##### ER-预期结果：1：印章创建成功；2：图片上传和处理正确；3：权限设置生效；4：审核流程正常；5：印章状态更新为可用；6：签署中可正常选择和使用；7：签署效果符合预期；8：使用记录准确记录；

### 签署流程与印章授权联动测试

#### TL-签署流程中印章权限验证闭环

##### PD-前置条件：存在需要印章签署的合同；用户具有相应印章权限；

##### 步骤一：发起合同签署流程

##### 步骤二：添加需要印章签署的签署人

##### 步骤三：签署人进入签署页面

##### 步骤四：选择使用印章签署

##### 步骤五：系统验证印章使用权限

##### 步骤六：完成印章签署

##### 步骤七：验证签署结果和印章效果

##### ER-预期结果：1：签署流程创建成功；2：签署人配置正确；3：签署页面正常显示；4：印章选择功能正常；5：权限验证准确；6：印章签署成功；7：签署文档显示正确的印章；

## 数据准确性验证测试

### 印章图片数据处理准确性

#### TL-印章图片原图与截图数据一致性验证

##### PD-前置条件：用户上传了印章原图；系统生成了截图版本；

##### 步骤一：上传高清印章图片"seal_original.png"

##### 步骤二：系统自动生成截图版本

##### 步骤三：对比原图和截图的关键信息

##### 步骤四：验证图片尺寸处理准确性

##### 步骤五：验证图片质量保持情况

##### 步骤六：验证图片格式转换准确性

##### 步骤七：在签署中使用截图版本

##### 步骤八：验证签署效果与原图一致性

##### ER-预期结果：1：原图上传成功且完整保存；2：截图版本生成成功；3：截图保持原图关键特征；4：尺寸按规则正确处理；5：图片质量在可接受范围内；6：格式转换无损关键信息；7：签署中截图显示正常；8：签署效果与原图视觉一致；

#### TL-法人授权信息数据传输准确性

##### PD-前置条件：用户填写完整的法人授权信息；

##### 步骤一：填写法定代表人姓名"张三"

##### 步骤二：填写身份证号"110101199001011234"

##### 步骤三：填写手机号"13812345678"

##### 步骤四：提交授权申请

##### 步骤五：验证后端接收数据准确性

##### 步骤六：验证数据库存储准确性

##### 步骤七：验证数据回显准确性

##### ER-预期结果：1：姓名"张三"准确传输和存储；2：身份证号"110101199001011234"完整无误；3：手机号"13812345678"格式和内容正确；4：提交过程无数据丢失；5：后端接收数据与前端一致；6：数据库存储字段正确；7：页面回显数据准确；

### 签署数据完整性验证

#### TL-批量签署数据处理准确性测试

##### PD-前置条件：创建包含多个签署人的批量签署流程；

##### 步骤一：创建包含5个签署人的流程

##### 步骤二：设置不同签署人的签署顺序

##### 步骤三：部分签署人完成签署

##### 步骤四：验证签署状态数据准确性

##### 步骤五：验证签署时间记录准确性

##### 步骤六：验证签署位置数据准确性

##### 步骤七：导出签署记录验证数据完整性

##### ER-预期结果：1：5个签署人信息准确记录；2：签署顺序逻辑正确；3：已签署状态准确更新；4：签署时间精确到秒；5：签署位置坐标准确；6：未签署状态正确显示；7：导出数据与实际一致；

## 异常数据处理测试

### 无效数据输入处理

#### TL-身份证号异常数据处理验证

##### PD-前置条件：用户进入法人授权信息填写页面；

##### 步骤一：输入空的身份证号

##### 步骤二：输入包含中文字符的身份证号"11010119900101张三"

##### 步骤三：输入包含特殊符号的身份证号"110101-1990-0101-1234"

##### 步骤四：输入超长身份证号"1101011990010112345678"

##### 步骤五：输入全零身份证号"000000000000000000"

##### 步骤六：验证系统对每种异常输入的处理

##### ER-预期结果：1：空输入显示"身份证号不能为空"；2：中文字符显示"身份证号只能包含数字和字母"；3：特殊符号显示"身份证号格式不正确"；4：超长输入显示"身份证号长度不能超过18位"；5：全零输入显示"请输入有效的身份证号"；6：系统保持稳定不崩溃；

#### TL-文件上传异常数据处理

##### PD-前置条件：用户进入印章图片上传页面；

##### 步骤一：上传0字节的空文件

##### 步骤二：上传文件名包含特殊字符的文件"印章<>?.jpg"

##### 步骤三：上传超大文件100MB的图片

##### 步骤四：上传病毒文件（测试文件）

##### 步骤五：上传损坏的图片文件

##### 步骤六：同时上传多个文件

##### ER-预期结果：1：空文件提示"文件不能为空"；2：特殊字符文件名被正确处理或提示；3：超大文件提示"文件大小超出限制"；4：病毒文件被安全机制拦截；5：损坏文件提示"文件格式错误或已损坏"；6：多文件上传按规则处理；

### 并发异常处理

#### TL-同一用户多次提交授权申请异常处理

##### PD-前置条件：用户已填写完整的法人授权信息；

##### 步骤一：快速连续点击提交按钮5次

##### 步骤二：验证系统防重复提交机制

##### 步骤三：检查后端是否产生重复数据

##### 步骤四：验证用户界面反馈

##### ER-预期结果：1：只有第一次提交生效；2：后续点击被忽略或提示；3：后端无重复数据产生；4：界面显示正确的处理状态；

#### TL-网络超时异常处理验证

##### PD-前置条件：用户正在上传大文件；网络环境不稳定；

##### 步骤一：开始上传10MB的印章图片

##### 步骤二：上传过程中模拟网络超时

##### 步骤三：验证系统超时处理机制

##### 步骤四：验证重试机制

##### 步骤五：验证数据完整性

##### ER-预期结果：1：系统检测到网络超时；2：显示友好的超时提示；3：提供重试选项；4：重试后可正常完成上传；5：上传的文件数据完整；

### 合同文件阅读时长控制

#### TL-单文档阅读时长控制验证

##### PD-前置条件：签署流程配置了单文档阅读时长30秒；用户进入签署页面；

##### 步骤一：打开第一份合同文档

##### 步骤二：开始阅读文档内容

##### 步骤三：在20秒时尝试切换到下一份文档

##### 步骤四：等待30秒后再次尝试切换

##### 步骤五：验证切换限制和提示

##### ER-预期结果：1：文档正常打开显示；2：阅读计时开始；3：20秒时切换被阻止并提示剩余时间；4：30秒后允许切换到下一份文档；5：时长控制准确无误；

#### TL-多文档阅读时长累计控制

##### PD-前置条件：签署流程包含3份文档；每份文档要求阅读20秒；

##### 步骤一：依次阅读第一份文档20秒

##### 步骤二：切换到第二份文档阅读20秒

##### 步骤三：切换到第三份文档阅读20秒

##### 步骤四：尝试提交签署

##### 步骤五：验证总阅读时长控制

##### ER-预期结果：1：每份文档阅读时长独立计算；2：文档间切换正常；3：所有文档阅读完成后允许签署；4：总计阅读时长60秒；5：签署提交成功；

#### TL-阅读时长不足时的签署限制

##### PD-前置条件：文档要求阅读60秒；用户只阅读了30秒；

##### 步骤一：打开签署文档

##### 步骤二：快速浏览文档30秒

##### 步骤三：尝试点击签署按钮

##### 步骤四：验证系统阻止签署

##### 步骤五：继续阅读至60秒后再次尝试签署

##### ER-预期结果：1：文档正常显示；2：阅读时间正确计算；3：30秒时签署按钮不可用或提示时间不足；4：系统准确阻止提前签署；5：60秒后签署功能正常开启；

### SaaS印章使用情况埋点

#### TL-印章创建埋点数据验证

##### PD-前置条件：用户具有印章管理权限；埋点系统正常运行；

##### 步骤一：创建新的企业印章

##### 步骤二：填写印章基本信息

##### 步骤三：上传印章图片

##### 步骤四：提交印章创建申请

##### 步骤五：验证埋点数据记录

##### ER-预期结果：1：印章创建成功；2：埋点记录印章创建事件；3：记录用户ID、印章类型、创建时间；4：记录印章图片大小和格式；5：埋点数据准确完整；

#### TL-印章使用埋点数据验证

##### PD-前置条件：用户在签署流程中使用印章；埋点系统正常；

##### 步骤一：进入签署页面

##### 步骤二：选择使用企业印章签署

##### 步骤三：完成印章签署操作

##### 步骤四：验证使用埋点数据

##### 步骤五：检查埋点数据完整性

##### ER-预期结果：1：签署操作成功完成；2：埋点记录印章使用事件；3：记录印章ID、使用时间、签署文档ID；4：记录用户行为路径；5：埋点数据实时上报；

#### TL-印章管理操作埋点验证

##### PD-前置条件：用户对印章进行各种管理操作；

##### 步骤一：查看印章列表

##### 步骤二：编辑印章信息

##### 步骤三：删除印章

##### 步骤四：恢复印章

##### 步骤五：验证各操作的埋点数据

##### ER-预期结果：1：每个操作都正常执行；2：查看操作记录访问埋点；3：编辑操作记录修改内容；4：删除操作记录删除时间和原因；5：恢复操作记录恢复时间；

### 印章授权bugfix测试

#### TL-印章使用员仅审批不用印场景修复验证

##### PD-前置条件：印章使用员配置为仅审批不用印；存在需要印章的签署流程；

##### 步骤一：创建需要印章签署的流程

##### 步骤二：指定仅审批不用印的使用员

##### 步骤三：使用员进入审批页面

##### 步骤四：完成审批操作

##### 步骤五：验证印章使用逻辑

##### ER-预期结果：1：签署流程创建成功；2：使用员收到审批通知；3：审批页面不显示印章使用选项；4：审批完成后流程正常流转；5：印章使用权限控制正确；

#### TL-印章权限异常处理修复验证

##### PD-前置条件：用户印章权限发生变更；正在进行的签署流程；

##### 步骤一：用户开始印章签署流程

##### 步骤二：管理员修改用户印章权限

##### 步骤三：用户继续完成签署操作

##### 步骤四：验证权限变更处理

##### ER-预期结果：1：签署流程正常开始；2：权限变更及时生效；3：系统正确处理权限冲突；4：用户收到权限变更提示；5：签署流程按新权限执行；

### 其他bugfix测试

#### TL-title报错问题修复验证

##### PD-前置条件：页面存在.title相关的报错问题；

##### 步骤一：访问可能出现title报错的页面

##### 步骤二：执行各种页面操作

##### 步骤三：检查浏览器控制台错误

##### 步骤四：验证页面功能正常性

##### ER-预期结果：1：页面正常加载显示；2：无.title相关的JavaScript错误；3：页面功能完全正常；4：用户体验无异常；

#### TL-系统稳定性回归验证

##### PD-前置条件：所有bugfix已部署；系统环境稳定；

##### 步骤一：执行核心业务流程

##### 步骤二：进行高频操作测试

##### 步骤三：模拟用户真实使用场景

##### 步骤四：监控系统性能指标

##### 步骤五：验证修复效果

##### ER-预期结果：1：核心流程稳定运行；2：高频操作无异常；3：用户场景体验良好；4：系统性能指标正常；5：所有已知问题得到修复；

## 冒烟测试用例

### 核心功能冒烟测试

#### MYTL-法人授权线上流程基本验证

##### PD-前置条件：用户已登录SaaS系统；具有法人授权权限；

##### 步骤一：进入印章管理页面

##### 步骤二：点击申请法人印章选择在线授权

##### 步骤三：填写法定代表人基本信息

##### 步骤四：提交授权申请

##### ER-预期结果：1：页面正常访问；2：表单功能正常；3：申请提交成功；4：进入等待审核状态；

#### MYTL-印章图片上传基本功能验证

##### PD-前置条件：用户具有印章管理权限；准备好标准印章图片；

##### 步骤一：进入印章上传页面

##### 步骤二：选择并上传印章图片

##### 步骤三：确认上传操作

##### ER-预期结果：1：上传页面正常显示；2：图片上传成功；3：原图和截图正确生成；

#### MYTL-强制阅读二次确认基本验证

##### PD-前置条件：签署流程配置了二次确认；用户进入签署页面；

##### 步骤一：完成文档阅读

##### 步骤二：点击提交签署

##### 步骤三：确认二次确认弹窗

##### ER-预期结果：1：文档阅读正常；2：二次确认弹窗显示；3：确认后签署继续；

#### MYTL-批量签不传姓名基本功能

##### PD-前置条件：存在未实名用户的签署流程；

##### 步骤一：调用批量签接口不传姓名

##### 步骤二：访问生成的签署链接

##### 步骤三：完成实名认证和签署

##### ER-预期结果：1：接口调用成功；2：签署链接有效；3：实名认证和签署正常；

#### MYTL-合同阅读时长控制基本验证

##### PD-前置条件：文档配置了阅读时长要求；

##### 步骤一：打开签署文档

##### 步骤二：阅读文档至要求时长

##### 步骤三：尝试提交签署

##### ER-预期结果：1：文档正常显示；2：时长控制生效；3：达到时长后可正常签署；

#### MYTL-印章使用埋点基本验证

##### PD-前置条件：用户使用印章进行签署；埋点系统正常；

##### 步骤一：选择印章进行签署

##### 步骤二：完成签署操作

##### 步骤三：验证埋点数据记录

##### ER-预期结果：1：印章签署成功；2：埋点数据正确记录；3：数据上报正常；

### 关键业务流程冒烟测试

#### MYTL-新用户完整授权流程冒烟

##### PD-前置条件：新用户账号；系统环境正常；

##### 步骤一：用户登录系统

##### 步骤二：申请法人授权

##### 步骤三：完成授权流程

##### 步骤四：获得印章使用权限

##### ER-预期结果：1：登录成功；2：授权申请顺利；3：流程完整无阻断；4：权限正确获得；

#### MYTL-印章从创建到使用冒烟测试

##### PD-前置条件：用户具有完整印章权限；

##### 步骤一：创建新印章

##### 步骤二：上传印章图片

##### 步骤三：在签署中使用印章

##### ER-预期结果：1：印章创建成功；2：图片处理正确；3：签署中正常使用；

### 系统稳定性冒烟测试

#### MYTL-核心页面访问稳定性验证

##### PD-前置条件：系统正常运行；用户具有相应权限；

##### 步骤一：访问印章管理主页

##### 步骤二：访问法人授权页面

##### 步骤三：访问签署页面

##### ER-预期结果：1：所有页面正常加载；2：无JavaScript错误；3：功能入口可用；

#### MYTL-基本接口响应性能验证

##### PD-前置条件：系统网络环境正常；

##### 步骤一：调用法人授权接口

##### 步骤二：调用印章上传接口

##### 步骤三：调用签署相关接口

##### ER-预期结果：1：接口响应时间<5秒；2：返回数据正确；3：无超时错误；

## 线上验证用例

### 核心业务验证

#### PATL-法人授权完整流程线上验证

##### PD-前置条件：线上环境正常；真实用户账号；

##### 步骤一：登录线上SaaS系统

##### 步骤二：进入印章管理申请法人授权

##### 步骤三：完成在线授权流程

##### 步骤四：验证授权结果和印章可用性

##### ER-预期结果：1：线上系统访问正常；2：授权流程完整可用；3：印章生成和使用正常；4：用户体验良好；

#### PATL-印章图片上传原图截图功能线上验证

##### PD-前置条件：线上环境部署最新版本；用户具有印章权限；

##### 步骤一：上传真实印章图片

##### 步骤二：验证原图保存功能

##### 步骤三：验证截图生成功能

##### 步骤四：在签署中使用截图版本

##### ER-预期结果：1：图片上传成功；2：原图完整保存；3：截图正确生成；4：签署中显示正常；

#### PATL-强制阅读二次确认线上功能验证

##### PD-前置条件：线上配置了二次确认的应用；真实签署流程；

##### 步骤一：进入配置了二次确认的签署页面

##### 步骤二：完成文档阅读

##### 步骤三：触发二次确认弹窗

##### 步骤四：完成签署流程

##### ER-预期结果：1：二次确认功能正常启用；2：弹窗显示配置的文案；3：签署流程完整；4：功能符合预期；

#### PATL-批量签不传姓名功能线上验证

##### PD-前置条件：线上环境；包含未实名用户的批量签署流程；

##### 步骤一：创建批量签署流程

##### 步骤二：调用批量签接口不传入姓名

##### 步骤三：用户访问签署链接完成签署

##### ER-预期结果：1：批量签接口调用成功；2：未实名用户可正常签署；3：实名认证流程正常；4：签署完成无异常；

#### PATL-合同阅读时长控制线上验证

##### PD-前置条件：线上签署流程配置了阅读时长；

##### 步骤一：访问配置了时长控制的签署页面

##### 步骤二：验证阅读时长限制生效

##### 步骤三：达到要求时长后完成签署

##### ER-预期结果：1：时长控制功能正常；2：限制机制准确生效；3：签署流程顺畅；

#### PATL-SaaS印章使用埋点线上验证

##### PD-前置条件：线上环境埋点系统正常；用户进行印章操作；

##### 步骤一：执行印章创建操作

##### 步骤二：执行印章使用操作

##### 步骤三：验证埋点数据上报

##### ER-预期结果：1：印章操作正常完成；2：埋点数据准确记录；3：数据实时上报到统计系统；

### 系统集成验证

#### PATL-多端兼容性线上验证

##### PD-前置条件：线上环境；不同终端设备；

##### 步骤一：PC端访问法人授权功能

##### 步骤二：移动端访问签署功能

##### 步骤三：验证功能一致性

##### ER-预期结果：1：PC端功能完整正常；2：移动端适配良好；3：跨端体验一致；

#### PATL-线上性能稳定性验证

##### PD-前置条件：线上生产环境；正常业务负载；

##### 步骤一：执行核心业务流程

##### 步骤二：监控系统响应时间

##### 步骤三：验证系统稳定性

##### ER-预期结果：1：业务流程响应及时；2：系统运行稳定；3：无性能异常；

### 数据安全验证

#### PATL-敏感数据处理线上验证

##### PD-前置条件：线上环境；真实用户数据；

##### 步骤一：提交包含身份证号的授权申请

##### 步骤二：验证数据传输加密

##### 步骤三：验证数据存储安全

##### ER-预期结果：1：敏感数据加密传输；2：存储符合安全规范；3：无数据泄露风险；

#### PATL-权限控制线上验证

##### PD-前置条件：不同权限级别的用户账号；

##### 步骤一：普通用户访问管理功能

##### 步骤二：管理员执行管理操作

##### 步骤三：验证权限控制准确性

##### ER-预期结果：1：普通用户权限受限；2：管理员权限正常；3：权限控制准确无误；
