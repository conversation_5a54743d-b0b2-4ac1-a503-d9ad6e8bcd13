# 批量签接口支持未传入姓名的签署流程-测试用例

## 功能测试

### 批量签链接获取

#### TL-企业签署方未传姓名获取批量签链接成功验证

##### PD-前置条件：已创建签署流程；企业签署方仅使用企业章；签署方未指定经办人姓名；

##### 步骤一：调用获取批量签链接接口，传入flowId和orgId

##### 步骤二：接口返回批量签链接信息

##### 步骤三：访问返回的批量签链接

##### ER-预期结果：1：接口调用成功，返回200状态码；2：返回realNameFlowId、url、shortLink等字段；3：成功进入批量签页面；4：页面展示实名认证入口；

#### TL-个人签署方未传姓名获取批量签链接校验拦截

##### PD-前置条件：已创建签署流程；包含个人签署方；个人签署方未指定姓名；

##### 步骤一：调用获取批量签链接接口，传入flowId和accountId

##### 步骤二：检查接口返回结果

##### ER-预期结果：1：接口调用失败；2：返回错误码和错误信息；3：提示个人签署方必须指定姓名；

#### TL-混合签署场景企业方未传姓名获取链接验证

##### PD-前置条件：已创建签署流程；包含企业签署方和个人签署方；企业签署方未指定姓名；个人签署方已指定姓名；

##### 步骤一：调用获取批量签链接接口，传入相关参数

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### ER-预期结果：1：接口调用成功；2：返回批量签链接信息；3：成功进入批量签页面；4：企业签署方可进行实名认证；

### 实名认证流程

#### TL-企业未实名状态批量签页面实名认证流程

##### PD-前置条件：企业签署方未传姓名；企业未完成实名认证；已获取批量签链接；

##### 步骤一：访问批量签页面链接

##### 步骤二：页面展示企业实名认证入口

##### 步骤三：点击企业实名认证入口

##### 步骤四：完成企业实名认证流程

##### 步骤五：返回批量签页面

##### ER-预期结果：1：页面正常展示实名认证入口；2：实名认证流程正常执行；3：实名完成后页面自动刷新；4：企业选章页面正常展示；5：可正常选择企业印章；

#### TL-个人未实名状态批量签页面实名认证流程

##### PD-前置条件：个人签署方已指定姓名；个人未完成实名认证；已获取批量签链接；

##### 步骤一：访问批量签页面链接

##### 步骤二：页面展示个人实名认证入口

##### 步骤三：点击个人实名认证入口

##### 步骤四：完成个人实名认证流程

##### 步骤五：返回批量签页面

##### ER-预期结果：1：页面正常展示个人实名认证入口；2：个人实名认证流程正常执行；3：实名完成后个人选章页面刷新；4：可正常选择个人印章；

### 签署执行流程

#### TL-企业方完成实名后批量签署执行验证

##### PD-前置条件：企业签署方未传姓名；企业已完成实名认证；已进入批量签页面；

##### 步骤一：在批量签页面选择企业印章

##### 步骤二：提交批量签署请求

##### 步骤三：检查签署执行结果

##### ER-预期结果：1：企业印章选择页面正常展示；2：可正常选择企业印章；3：批量签署提交成功；4：签署流程状态更新为已签署；

#### TL-混合场景批量签署执行验证

##### PD-前置条件：包含企业签署方和个人签署方；企业方未传姓名；个人方已指定姓名；双方均已实名；

##### 步骤一：在批量签页面分别选择企业印章和个人印章

##### 步骤二：提交批量签署请求

##### 步骤三：检查签署执行结果

##### ER-预期结果：1：企业和个人印章选择页面均正常展示；2：可分别选择对应印章；3：批量签署提交成功；4：所有签署流程状态正确更新；

## 接口测试

### 获取企业批量签链接接口

#### TL-POST接口flowId-organ-auth-orgId-identifyUrl正常调用验证

##### PD-前置条件：已创建有效签署流程；企业签署方未传姓名；具有接口调用权限；

##### 步骤一：构造POST请求，URL为{flowId}/organ/auth/{orgId}/identifyUrl

##### 步骤二：设置请求头Content-Type为application/json

##### 步骤三：传入batchSerialId参数（可选）

##### 步骤四：发送请求并获取响应

##### ER-预期结果：1：HTTP状态码为200；2：响应包含realNameFlowId字段；3：响应包含url字段；4：响应包含shortLink字段；5：所有字段值非空且格式正确；

#### TL-batchSerialId参数为空时接口调用验证

##### PD-前置条件：已创建有效签署流程；企业签署方未传姓名；

##### 步骤一：调用获取企业批量签链接接口

##### 步骤二：不传入batchSerialId参数

##### 步骤三：检查接口响应

##### ER-预期结果：1：接口调用成功；2：返回正常的批量签链接信息；3：功能不受影响；

### 获取个人批量签链接接口

#### TL-POST接口flowId-individual-realNameWill-accountId-identifyUrl调用验证

##### PD-前置条件：已创建有效签署流程；个人签署方已指定姓名；具有接口调用权限；

##### 步骤一：构造POST请求，URL为{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：设置请求头Content-Type为application/json

##### 步骤三：传入batchSerialId参数（可选）

##### 步骤四：发送请求并获取响应

##### ER-预期结果：1：HTTP状态码为200；2：响应包含type字段；3：响应包含bizId字段；4：响应包含realNameFlowId字段；5：响应包含url和shortUrl字段；

## 异常测试

### 参数校验异常

#### TL-flowId参数无效时接口异常处理验证

##### PD-前置条件：具有接口调用权限；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：传入无效的flowId参数

##### 步骤三：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回明确的错误信息；3：错误信息包含flowId无效的提示；

#### TL-orgId参数无效时接口异常处理验证

##### PD-前置条件：已创建有效签署流程；

##### 步骤一：调用获取企业批量签链接接口

##### 步骤二：传入无效的orgId参数

##### 步骤三：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回明确的错误信息；3：错误信息包含orgId无效的提示；

### 业务逻辑异常

#### TL-企业签署方使用个人章时未传姓名校验拦截

##### PD-前置条件：已创建签署流程；企业签署方使用个人章；企业签署方未指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### ER-预期结果：1：接口调用失败；2：返回业务错误码；3：提示企业签署方使用个人章时必须指定姓名；

#### TL-签署流程不存在时接口异常处理

##### PD-前置条件：具有接口调用权限；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：传入不存在的flowId

##### 步骤三：检查接口响应

##### ER-预期结果：1：接口返回404或相应错误状态码；2：返回签署流程不存在的错误信息；

## 兼容性测试

### 原有功能兼容性

#### TL-原有指定姓名流程兼容性验证

##### PD-前置条件：已创建签署流程；企业签署方已指定经办人姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：执行完整的批量签署流程

##### ER-预期结果：1：接口调用成功；2：批量签页面正常展示；3：签署流程正常执行；4：功能与原有流程保持一致；

#### TL-多种签署类型混合场景兼容性验证

##### PD-前置条件：签署流程包含企业签、个人签、企业经办人签等多种类型；部分签署方未指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：分别处理不同类型的签署方

##### 步骤四：执行批量签署

##### ER-预期结果：1：接口正常返回；2：页面正确展示各类型签署方；3：未指定姓名的企业签署方可正常处理；4：已指定姓名的签署方功能不受影响；5：批量签署正常执行；

## 性能测试

### 批量处理性能

#### TL-大量签署流程批量签链接获取性能验证

##### PD-前置条件：已创建100个签署流程；企业签署方均未传姓名；

##### 步骤一：并发调用获取批量签链接接口

##### 步骤二：记录响应时间和成功率

##### 步骤三：分析性能指标

##### ER-预期结果：1：接口响应时间小于3秒；2：成功率达到99%以上；3：系统资源使用正常；4：无内存泄漏现象；

#### TL-高并发场景批量签页面访问性能验证

##### PD-前置条件：已获取批量签链接；准备100个并发用户；

##### 步骤一：100个用户同时访问批量签页面

##### 步骤二：记录页面加载时间

##### 步骤三：检查页面功能正常性

##### ER-预期结果：1：页面加载时间小于5秒；2：所有用户均能正常访问；3：页面功能完全正常；4：服务器无异常错误；

## 安全测试

### 权限校验

#### TL-无权限用户获取批量签链接安全校验

##### PD-前置条件：已创建签署流程；准备无权限的用户账号；

##### 步骤一：使用无权限用户调用获取批量签链接接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回401或403错误；2：返回权限不足的错误信息；3：不返回任何敏感信息；

#### TL-跨企业访问批量签链接安全校验

##### PD-前置条件：企业A创建的签署流程；企业B的用户账号；

##### 步骤一：使用企业B用户调用企业A的批量签链接接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回403权限错误；2：不允许跨企业访问；3：记录安全审计日志；

### 数据安全

#### TL-批量签链接数据传输加密验证

##### PD-前置条件：已创建签署流程；配置HTTPS环境；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查数据传输过程

##### 步骤三：验证返回的链接安全性

##### ER-预期结果：1：数据传输使用HTTPS加密；2：返回的链接包含安全token；3：敏感信息不在URL中明文传输；4：链接具有时效性；

## 冒烟测试用例

### 核心功能验证

#### MYTL-企业签署方未传姓名获取批量签链接基本流程验证

##### PD-前置条件：已创建签署流程；企业签署方仅使用企业章；签署方未指定经办人姓名；

##### 步骤一：调用获取批量签链接接口，传入flowId和orgId

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成企业实名认证

##### 步骤四：选择企业印章并提交签署

##### ER-预期结果：1：成功获取批量签链接；2：正常进入批量签页面；3：实名认证流程正常；4：签署执行成功；

#### MYTL-个人签署方未传姓名校验拦截验证

##### PD-前置条件：已创建签署流程；包含个人签署方；个人签署方未指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### ER-预期结果：1：接口调用失败；2：返回明确的错误信息；3：提示个人签署方必须指定姓名；

#### MYTL-POST接口flowId-organ-auth-orgId-identifyUrl基本调用验证

##### PD-前置条件：已创建有效签署流程；企业签署方未传姓名；

##### 步骤一：构造POST请求调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：HTTP状态码为200；2：返回realNameFlowId、url、shortLink字段；3：字段值格式正确；

#### MYTL-企业未实名状态批量签页面实名认证基本流程

##### PD-前置条件：企业签署方未传姓名；企业未完成实名认证；

##### 步骤一：访问批量签页面链接

##### 步骤二：完成企业实名认证

##### 步骤三：选择企业印章

##### ER-预期结果：1：页面展示实名认证入口；2：实名认证流程正常；3：可正常选择企业印章；

#### MYTL-原有指定姓名流程兼容性基本验证

##### PD-前置条件：已创建签署流程；企业签署方已指定经办人姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：执行完整批量签署流程

##### ER-预期结果：1：接口调用成功；2：批量签流程正常执行；3：功能与原有流程一致；

#### MYTL-flowId参数无效异常处理基本验证

##### PD-前置条件：具有接口调用权限；

##### 步骤一：传入无效flowId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：返回4xx错误状态码；2：返回明确的错误信息；

## 线上验证用例

### 核心业务流程验证

#### PATL-企业签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；已创建真实签署流程；企业签署方仅使用企业章；未指定经办人姓名；

##### 步骤一：调用线上获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成企业实名认证流程

##### 步骤四：选择企业印章并提交批量签署

##### 步骤五：验证签署结果和流程状态

##### ER-预期结果：1：接口调用成功返回有效链接；2：批量签页面正常展示；3：企业实名认证流程完整可用；4：印章选择功能正常；5：签署执行成功且状态正确更新；

#### PATL-个人签署方姓名校验机制线上验证

##### PD-前置条件：线上环境；已创建包含个人签署方的流程；个人签署方未指定姓名；

##### 步骤一：调用线上获取批量签链接接口

##### 步骤二：验证校验拦截机制

##### ER-预期结果：1：接口正确拦截并返回错误；2：错误信息准确提示个人签署方必须指定姓名；3：不影响其他正常流程；

#### PATL-混合签署场景线上完整流程验证

##### PD-前置条件：线上环境；签署流程包含企业签署方和个人签署方；企业方未传姓名；个人方已指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面并完成实名认证

##### 步骤三：分别选择企业印章和个人印章

##### 步骤四：提交批量签署并验证结果

##### ER-预期结果：1：接口正常返回批量签链接；2：页面正确处理不同类型签署方；3：实名认证流程完整；4：印章选择功能正常；5：批量签署成功执行；

#### PATL-接口兼容性线上验证

##### PD-前置条件：线上环境；原有指定姓名的签署流程；

##### 步骤一：使用原有流程调用批量签接口

##### 步骤二：执行完整的批量签署流程

##### 步骤三：对比新旧功能表现

##### ER-预期结果：1：原有功能完全正常；2：新功能不影响原有流程；3：接口响应格式保持一致；4：业务逻辑兼容性良好；

#### PATL-权限安全机制线上验证

##### PD-前置条件：线上环境；不同权限级别的用户账号；

##### 步骤一：使用无权限用户尝试获取批量签链接

##### 步骤二：使用跨企业用户尝试访问其他企业流程

##### 步骤三：验证安全拦截机制

##### ER-预期结果：1：无权限访问被正确拦截；2：跨企业访问被阻止；3：返回适当的权限错误信息；4：安全日志正确记录；
