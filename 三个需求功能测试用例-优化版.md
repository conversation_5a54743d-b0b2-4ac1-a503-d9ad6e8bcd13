# 【您负责的三个需求】功能测试用例

## 需求1：SaaS印章上传前端裁剪改成原图裁剪

### 印章图片上传处理

#### 功能测试

##### TL-小于1M图片勾选前端裁剪触发原图裁剪

###### PD-前置条件
准备800KB的PNG格式印章图片文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传800KB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统触发前端原图裁剪逻辑；2：原图文件保存到服务器；3：生成裁剪后的图片版本；4：页面显示"前端裁剪处理成功"提示信息

##### TL-小于1M图片未勾选前端裁剪使用原有逻辑

###### PD-前置条件
准备800KB的PNG格式印章图片文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传800KB的PNG格式印章图片文件，不勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统不触发前端原图裁剪逻辑；2：按原有后端处理逻辑执行；3：印章创建成功

##### TL-大于1M图片勾选前端裁剪不触发原图裁剪

###### PD-前置条件
准备1.2MB的PNG格式印章图片文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传1.2MB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统不触发前端原图裁剪逻辑；2：按原有后端处理逻辑执行；3：印章创建成功

##### TL-1MB边界值图片勾选前端裁剪验证

###### PD-前置条件
准备正好1MB的PNG格式印章图片文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传正好1MB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统不触发前端原图裁剪逻辑；2：按原有后端处理逻辑执行；3：印章创建成功

##### TL-非图片格式文件勾选前端裁剪验证

###### PD-前置条件
准备800KB的PDF格式文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
选择800KB的PDF格式文件进行上传，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统提示"文件格式不支持，请上传图片格式文件"；2：不触发前端裁剪逻辑；3：上传操作失败

#### 异常测试

##### TL-前端裁剪过程异常处理验证

###### PD-前置条件
准备800KB的PNG格式印章图片文件；模拟前端JavaScript异常环境

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传800KB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统捕获前端裁剪异常；2：显示"前端裁剪失败，已自动切换到后端处理"提示；3：印章按原有逻辑正常创建完成

##### TL-网络中断时前端裁剪异常处理

###### PD-前置条件
准备800KB的PNG格式印章图片文件；模拟网络中断环境

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传800KB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮（此时网络中断）

###### ER-预期结果
1：系统检测到网络异常；2：显示"网络连接异常，请检查网络后重试"提示；3：提供重试按钮；4：已填写信息不丢失

##### TL-服务器存储空间不足异常处理

###### PD-前置条件
准备800KB的PNG格式印章图片文件；模拟服务器存储空间不足

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
上传800KB的PNG格式印章图片文件，勾选"使用前端裁剪"选项

###### 步骤三
点击"确认上传"按钮

###### ER-预期结果
1：系统检测到存储空间不足；2：显示"服务器存储空间不足，请稍后重试"提示；3：上传操作失败；4：系统记录异常日志

#### 性能测试

##### TL-前端裁剪并发处理性能验证

###### PD-前置条件
准备50个800KB的PNG格式印章图片文件

###### 步骤一
同时打开50个浏览器标签页，进入印章管理页面

###### 步骤二
在每个标签页中上传800KB图片文件，勾选"使用前端裁剪"选项

###### 步骤三
同时点击所有标签页的"确认上传"按钮

###### ER-预期结果
1：50个并发请求的成功率大于95%；2：单个前端裁剪处理时间小于10秒；3：系统CPU和内存使用率在合理范围内；4：无系统崩溃或异常

#### 兼容性测试

##### TL-浏览器兼容性测试

###### PD-前置条件
准备Chrome、Firefox、Safari、Edge浏览器；准备800KB的PNG格式印章图片文件

###### 步骤一
在Chrome浏览器中上传800KB图片，勾选前端裁剪，验证处理结果

###### 步骤二
在Firefox浏览器中重复相同测试

###### 步骤三
在Safari浏览器中重复相同测试

###### 步骤四
在Edge浏览器中重复相同测试

###### ER-预期结果
1：所有主流浏览器都支持前端裁剪功能；2：处理结果一致；3：处理时间差异小于2秒；4：无浏览器特有的兼容性问题

## 需求2：非标API支持法人授权静默签

### 非标API接口参数处理

#### 功能测试

##### TL-原有参数调用接口兼容性验证

###### PD-前置条件
准备企业授权书文件

###### 步骤一
调用非标API企业线下授权接口，传入原有参数组合（不包含authType和legalRepAccountId）

###### 步骤二
上传企业授权书文件，提交授权申请

###### ER-预期结果
1：接口调用成功返回200状态码；2：系统按企业授权逻辑处理；3：授权书文件上传成功；4：授权申请提交成功

##### TL-authType为ENTERPRISE不传legalRepAccountId验证

###### PD-前置条件
准备企业授权书文件

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"ENTERPRISE"，不传入legalRepAccountId参数

###### 步骤二
上传企业授权书文件，提交授权申请

###### ER-预期结果
1：接口调用成功返回200状态码；2：系统识别为企业授权类型；3：授权书文件上传成功；4：按企业授权流程处理

##### TL-authType为LEGAL_PERSON传入有效legalRepAccountId验证

###### PD-前置条件
准备法人授权书文件；准备有效的法人个人账号ID

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，设置legalRepAccountId参数为有效的法人个人账号ID

###### 步骤二
上传法人线下授权书文件，提交授权申请

###### ER-预期结果
1：接口调用成功返回200状态码；2：系统识别为法人授权类型；3：法人账号ID正确关联；4：授权书文件上传成功；5：进入法人授权审核流程

### 法人授权静默签处理

#### 功能测试

##### TL-法人授权审核通过后静默签验证

###### PD-前置条件
法人授权已审核通过；法人个人账号已创建；准备需要签署的合同

###### 步骤一
使用法人个人账号创建签署流程，设置autosign参数为true，指定法人印章

###### 步骤二
发起静默签署

###### ER-预期结果
1：系统验证法人静默签授权有效；2：静默签署自动执行完成；3：合同状态更新为已签署；4：文档显示法人印章签署效果

##### TL-企业授权和法人授权静默签区分验证

###### PD-前置条件
同时存在企业授权和法人授权；准备需要签署的合同

###### 步骤一
使用企业授权账号创建签署流程，设置autosign参数为true，发起静默签署

###### 步骤二
使用法人授权账号创建签署流程，设置autosign参数为true，发起静默签署

###### ER-预期结果
1：系统正确区分企业静默签授权和法人静默签授权；2：两种授权类型的静默签署都正常执行；3：授权类型判断准确无误

#### 异常测试

##### TL-authType为LEGAL_PERSON不传legalRepAccountId异常验证

###### PD-前置条件
准备法人授权书文件

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，不传入legalRepAccountId参数

###### 步骤二
尝试提交授权申请

###### ER-预期结果
1：接口返回400错误状态码；2：返回错误信息"legalRepAccountId参数为必填项"；3：授权申请提交失败

##### TL-authType为无效值异常验证

###### PD-前置条件
准备授权书文件

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"INVALID_TYPE"

###### 步骤二
尝试提交授权申请

###### ER-预期结果
1：接口返回400错误状态码；2：返回错误信息"authType参数值无效，支持ENTERPRISE或LEGAL_PERSON"；3：授权申请提交失败

##### TL-legalRepAccountId为不存在账号ID异常验证

###### PD-前置条件
准备法人授权书文件

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，设置legalRepAccountId参数为不存在的账号ID

###### 步骤二
尝试提交授权申请

###### ER-预期结果
1：接口返回400错误状态码；2：返回错误信息"legalRepAccountId对应的账号不存在"；3：授权申请提交失败

##### TL-AI审核法人授权书失败处理

###### PD-前置条件
准备模糊不清的法人授权书图片；准备有效的法人个人账号ID

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，设置legalRepAccountId参数为有效的法人个人账号ID

###### 步骤二
上传模糊不清的法人授权书图片，提交授权申请

###### ER-预期结果
1：AI审核识别授权书质量问题；2：审核结果返回失败状态；3：返回具体失败原因"授权书图片不清晰，请重新上传"；4：提供重新上传指引

##### TL-法人授权过期后静默签失败处理

###### PD-前置条件
法人授权已过期；准备需要签署的合同

###### 步骤一
使用过期法人授权的账号创建签署流程，设置autosign参数为true

###### 步骤二
尝试发起静默签署

###### ER-预期结果
1：系统检测到法人授权已过期；2：静默签署操作失败；3：返回错误信息"法人授权已过期，请重新进行授权"；4：签署流程状态保持未签署

#### 性能测试

##### TL-静默签批量处理性能验证

###### PD-前置条件
法人授权已审核通过；准备100个需要签署的合同

###### 步骤一
使用法人个人账号批量创建100个签署流程，为所有签署流程设置autosign参数为true

###### 步骤二
同时发起100个静默签署请求

###### ER-预期结果
1：100个静默签署请求的成功率大于95%；2：单个静默签署处理时间小于5秒；3：系统响应时间稳定；4：无性能瓶颈或超时异常

#### 安全测试

##### TL-法人授权信息篡改安全验证

###### PD-前置条件
准备法人授权书文件

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，设置legalRepAccountId参数为他人的法人个人账号ID

###### 步骤二
尝试提交授权申请

###### ER-预期结果
1：系统验证法人身份信息不匹配；2：授权申请被拒绝；3：返回错误信息"法人身份验证失败"；4：记录安全异常日志

##### TL-静默签权限越权验证

###### PD-前置条件
用户A具有企业授权；用户B具有法人授权

###### 步骤一
使用用户A的企业授权账号，尝试调用用户B的法人静默签功能，设置autosign参数为true

###### 步骤二
尝试发起静默签署

###### ER-预期结果
1：系统检测到权限不匹配；2：静默签署操作被拒绝；3：返回错误信息"无权限执行该操作"；4：记录越权尝试日志

## 需求3：法人章创建流程优化

### 法人章创建流程

#### 功能测试

##### TL-优化后法人章创建流程验证

###### PD-前置条件
准备法人基本信息和身份证明文件

###### 步骤一
进入法人章创建页面，填写法人姓名、身份证号、联系方式

###### 步骤二
上传法人身份证明文件，选择法人章样式

###### 步骤三
点击"提交创建申请"按钮

###### ER-预期结果
1：法人章创建申请提交成功；2：系统返回申请单号；3：申请状态为"待审核"；4：页面跳转到申请结果页面

##### TL-法人章创建状态转换验证

###### PD-前置条件
无

###### 步骤一
进入法人章创建页面（初始状态），开始填写法人基本信息（进入信息填写状态）

###### 步骤二
完成信息填写点击下一步（进入文件上传状态），完成文件上传点击下一步（进入样式选择状态）

###### 步骤三
完成样式选择点击提交（进入提交审核状态）

###### ER-预期结果
1：每个状态转换正常；2：状态转换时页面UI正确更新；3：无法跳过必要的状态直接提交；4：状态回退功能正常工作

### 授权书预览功能

#### 功能测试

##### TL-PC端授权书预览功能验证

###### PD-前置条件
系统中存在授权书文件

###### 步骤一
进入法人章创建页面，点击"查看授权书"链接

###### ER-预期结果
1：授权书预览窗口正常打开；2：授权书内容清晰显示；3：预览界面无下载按钮；4：预览界面无另存为功能

##### TL-PC端授权书下载限制验证

###### PD-前置条件
已打开授权书预览界面

###### 步骤一
在授权书预览界面右键点击，查看右键菜单选项

###### ER-预期结果
1：右键菜单中无"图片另存为"选项；2：右键菜单中无"复制图片地址"选项；3：无法通过右键菜单下载授权书

##### TL-H5端授权书预览功能验证

###### PD-前置条件
使用移动设备H5浏览器；系统中存在授权书文件

###### 步骤一
进入法人章创建页面，点击"查看授权书"链接

###### ER-预期结果
1：授权书预览界面在移动端正常显示；2：预览内容在小屏幕上清晰可读；3：预览界面适配移动端屏幕尺寸；4：无下载或保存功能

##### TL-H5端授权书保存限制验证

###### PD-前置条件
使用移动设备H5浏览器；已打开授权书预览界面

###### 步骤一
在授权书预览界面长按内容，查看长按后的操作选项

###### ER-预期结果
1：长按后无"保存图片"选项；2：长按后无"复制"选项；3：无法通过长按操作保存授权书到本地

#### 异常测试

##### TL-授权书文件损坏时预览异常处理

###### PD-前置条件
系统中存在损坏的授权书文件

###### 步骤一
进入法人章创建页面，点击"查看授权书"链接

###### ER-预期结果
1：系统检测到授权书文件损坏；2：显示"授权书文件异常，无法预览"提示；3：提供联系客服的引导信息；4：页面不会崩溃

#### 性能测试

##### TL-法人章创建页面加载性能验证

###### PD-前置条件
网络环境正常

###### 步骤一
清空浏览器缓存，访问法人章创建页面，记录页面完全加载时间

###### ER-预期结果
1：页面首次加载时间小于3秒；2：页面资源加载完整无缺失；3：页面交互响应时间小于1秒

#### 安全测试

##### TL-授权书URL直接访问安全验证

###### PD-前置条件
已获取授权书的预览URL地址

###### 步骤一
复制授权书预览界面的URL地址，在新的浏览器标签页中直接访问该URL

###### 步骤二
在未登录状态下访问该URL

###### ER-预期结果
1：直接访问URL返回403权限错误；2：未登录状态下访问返回401认证错误；3：无法绕过权限控制直接访问授权书

#### 兼容性测试

##### TL-跨浏览器兼容性验证

###### PD-前置条件
准备Chrome、Firefox、Safari浏览器

###### 步骤一
在Chrome浏览器中完成法人章创建流程

###### 步骤二
在Firefox浏览器中完成法人章创建流程

###### 步骤三
在Safari浏览器中完成法人章创建流程

###### ER-预期结果
1：所有浏览器中页面显示一致；2：功能操作无异常；3：无浏览器特有的兼容性问题

##### TL-移动设备兼容性验证

###### PD-前置条件
准备iOS和Android移动设备

###### 步骤一
在iOS设备Safari浏览器中测试法人章创建流程

###### 步骤二
在Android设备Chrome浏览器中测试法人章创建流程

###### 步骤三
在移动端测试授权书预览功能

###### ER-预期结果
1：移动端页面布局适配良好；2：授权书预览在移动端清晰显示；3：功能完整性与PC端一致

## 集成测试

### 三个需求联合功能验证

#### TL-印章前端裁剪与法人章创建集成验证

###### PD-前置条件
准备800KB的PNG格式印章图片文件；准备法人基本信息

###### 步骤一
进入法人章创建页面，填写法人基本信息，上传法人身份证明文件

###### 步骤二
上传800KB的法人印章图片，勾选"使用前端裁剪"选项，选择法人章样式

###### 步骤三
提交法人章创建申请

###### ER-预期结果
1：法人章创建流程正常执行；2：印章图片前端裁剪功能正常工作；3：前端裁剪后的印章正确保存；4：法人章创建成功

#### TL-法人授权与静默签完整流程集成验证

###### PD-前置条件
准备法人授权书文件；准备有效的法人个人账号ID；准备需要签署的合同

###### 步骤一
调用非标API企业线下授权接口，设置authType参数为"LEGAL_PERSON"，设置legalRepAccountId参数为有效的法人个人账号ID

###### 步骤二
上传法人线下授权书文件，等待AI审核通过

###### 步骤三
使用法人个人账号创建签署流程，设置autosign参数为true，发起静默签署

###### ER-预期结果
1：法人授权申请成功提交；2：AI审核正常通过；3：法人授权状态正确更新；4：静默签署功能正常执行；5：合同签署成功完成

#### TL-三个需求端到端业务流程集成验证

###### PD-前置条件
准备完整的测试数据

###### 步骤一
使用优化后的法人章创建流程创建法人章，在创建过程中上传印章图片并使用前端裁剪功能

###### 步骤二
预览授权书（验证只能查看不能下载），完成法人章创建

###### 步骤三
通过非标API配置法人授权静默签，创建需要法人签署的合同，执行静默签署流程

###### ER-预期结果
1：整个业务流程端到端执行成功；2：各功能模块协调工作无冲突；3：法人章创建、印章裁剪、授权配置、静默签署各环节正常；4：最终合同签署完成

#### TL-数据一致性集成验证

###### PD-前置条件
完成法人章创建和授权配置

###### 步骤一
查询法人章创建记录中的印章信息，查询法人授权记录中的账号信息

###### 步骤二
创建签署流程并执行静默签，查询签署记录中的印章和授权信息

###### 步骤三
对比各模块中的数据一致性

###### ER-预期结果
1：法人章信息在各模块中保持一致；2：印章数据在创建和签署中一致；3：法人授权信息准确传递；4：数据完整性在整个流程中得到保障

## 回归测试

### 现有功能兼容性验证

#### TL-现有印章功能回归验证

###### PD-前置条件
准备800KB的PNG格式印章图片文件

###### 步骤一
进入印章管理页面，点击"创建印章"按钮

###### 步骤二
选择800KB的PNG格式印章图片文件，不勾选"使用前端裁剪"选项（使用原有逻辑），点击"确认上传"按钮

###### ER-预期结果
1：原有印章创建功能完全正常；2：图片按原有逻辑正确处理；3：印章创建成功；4：功能表现与改动前一致

#### TL-现有企业授权功能回归验证

###### PD-前置条件
准备企业授权书文件

###### 步骤一
调用非标API企业线下授权接口，使用原有参数组合（不传入新增参数），上传企业授权书文件

###### 步骤二
提交授权申请，使用企业授权进行签署操作

###### ER-预期结果
1：原有企业授权功能完全正常；2：授权流程与改动前一致；3：签署功能正常工作；4：现有客户使用无影响

#### TL-现有签署流程回归验证

###### PD-前置条件
准备需要签署的合同

###### 步骤一
创建普通签署流程（非静默签），添加签署人信息，发起签署流程

###### 步骤二
签署人完成在线签署，验证签署结果

###### ER-预期结果
1：原有签署流程功能完全正常；2：签署人操作体验无变化；3：签署结果正确生成；4：功能稳定性保持

#### TL-系统整体稳定性回归验证

###### PD-前置条件
系统部署完成；准备完整的回归测试数据

###### 步骤一
执行核心业务流程测试，执行高频使用功能测试，执行边界条件测试

###### 步骤二
监控系统性能指标，检查系统日志异常

###### ER-预期结果
1：系统整体功能稳定；2：核心业务流程正常；3：性能指标在正常范围内；4：无新增异常日志；5：系统可用性保持

## 冒烟测试

### 核心功能基本验证

#### MYTL-印章前端裁剪基本功能

###### PD-前置条件
准备小于1M的印章图片

###### 步骤一
上传小于1M的印章图片，勾选前端裁剪选项，确认创建

###### ER-预期结果
1：前端裁剪功能正常；2：印章创建成功

#### MYTL-现有接口兼容性基本验证

###### PD-前置条件
准备企业授权书

###### 步骤一
调用接口不传入新增参数，上传企业授权书，完成授权流程

###### ER-预期结果
1：接口调用成功；2：现有功能正常；3：兼容性良好

#### MYTL-法人授权新参数基本功能

###### PD-前置条件
法人个人账号已创建；准备法人授权书

###### 步骤一
设置authType="LEGAL_PERSON"，设置legalRepAccountId为法人账号ID，上传法人授权书

###### ER-预期结果
1：新参数正确识别；2：法人授权创建成功；3：授权类型正确

#### MYTL-法人授权静默签基本功能

###### PD-前置条件
法人已通过新接口完成授权

###### 步骤一
使用法人账号调用静默签API，设置autosign=true，验证法人静默签授权有效性，执行签署

###### ER-预期结果
1：法人授权有效性判断正确；2：静默签署自动完成；3：无需人工干预；4：区分企业和法人授权

#### MYTL-法人章创建流程优化基本验证

###### PD-前置条件
无

###### 步骤一
进入优化后的创建页面，完成基本信息填写，预览授权书（不下载）

###### ER-预期结果
1：页面加载时间小于3秒；2：流程步骤减少；3：预览功能正常且无法下载

## 线上验证

### 生产环境功能验证

#### PATL-印章前端裁剪线上验证

###### PD-前置条件
线上环境已部署

###### 步骤一
在生产环境创建印章，使用前端裁剪功能，验证裁剪效果

###### ER-预期结果
1：生产环境功能正常；2：裁剪效果符合预期

#### PATL-现有客户接口兼容性线上验证

###### PD-前置条件
真实的现有客户

###### 步骤一
现有客户使用原有参数调用接口，完成企业授权流程，验证授权功能正常

###### ER-预期结果
1：现有客户无感知升级；2：原有功能完全正常；3：接口兼容性100%

#### PATL-法人授权静默签线上验证

###### PD-前置条件
真实法人授权

###### 步骤一
使用新参数创建法人授权，执行真实的法人静默签署，验证授权类型区分，验证签署结果

###### ER-预期结果
1：法人授权创建成功；2：授权类型正确区分；3：线上静默签正常；4：签署结果有效

#### PATL-法人章创建流程线上验证

###### PD-前置条件
真实用户

###### 步骤一
真实用户使用优化后流程，验证授权书预览限制

###### ER-预期结果
1：流程优化效果明显；2：下载限制有效
