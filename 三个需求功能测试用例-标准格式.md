# 【您负责的三个需求】功能测试用例

## 需求1：SaaS印章上传前端裁剪改成原图裁剪

### 功能测试

#### TL-001 小于1M图片勾选前端裁剪触发原图裁剪

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统触发前端原图裁剪逻辑；原图完整保存到服务器；生成对应的截图版本；页面显示"前端裁剪处理成功"提示

#### TL-002 小于1M图片未勾选前端裁剪使用原有逻辑

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→不勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

#### TL-003 大于1M图片勾选前端裁剪不触发原图裁剪

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备1.2MB的PNG格式印章图片文件

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择1.2MB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

#### TL-004 1MB边界值图片勾选前端裁剪验证

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备正好1MB的PNG格式印章图片文件

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择正好1MB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统不触发前端原图裁剪逻辑；按原有后端处理逻辑执行；图片正常上传并处理完成

#### TL-005 非图片格式文件勾选前端裁剪验证

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PDF格式文件

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PDF格式文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统提示"文件格式不支持，请上传图片格式文件"；不触发前端裁剪逻辑；上传操作失败

### 异常测试

#### TL-006 前端裁剪过程异常处理验证

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟前端JavaScript异常环境

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统捕获前端裁剪异常；显示"前端裁剪失败，已自动切换到后端处理"提示；图片按原有逻辑正常处理完成

#### TL-007 网络中断时前端裁剪异常处理

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟网络中断环境

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮（此时网络中断）

##### 预期结果
系统检测到网络异常；显示"网络连接异常，请检查网络后重试"提示；提供重试按钮；不会丢失已填写的信息

#### TL-008 服务器存储空间不足异常处理

##### 前置条件
用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件；模拟服务器存储空间不足

##### 操作步骤
进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→勾选"使用前端裁剪"选项→点击"确认上传"按钮

##### 预期结果
系统检测到存储空间不足；显示"服务器存储空间不足，请稍后重试"提示；上传操作失败；系统记录异常日志

### 性能测试

#### TL-009 前端裁剪并发处理性能验证
	前置条件：用户已登录SaaS系统；用户具有印章管理权限；准备50个800KB的PNG格式印章图片文件
	操作步骤：同时打开50个浏览器标签页→在每个标签页中进入印章管理页面→在每个标签页中上传800KB图片文件→在每个标签页中勾选"使用前端裁剪"选项→同时点击所有标签页的"确认上传"按钮
	预期结果：50个并发请求的成功率大于95%；单个前端裁剪处理时间小于10秒；系统CPU和内存使用率在合理范围内；无系统崩溃或异常

### 兼容性测试

#### TL-010 浏览器兼容性测试
	前置条件：准备Chrome、Firefox、Safari、Edge浏览器；相同的测试图片
	操作步骤：在Chrome浏览器中测试前端裁剪功能→上传800KB图片，勾选前端裁剪，验证处理结果→在Firefox浏览器中重复相同测试→在Safari浏览器中重复相同测试→在Edge浏览器中重复相同测试→对比各浏览器的处理结果和性能
	预期结果：所有主流浏览器都支持前端裁剪功能；处理结果一致性良好；性能差异在可接受范围内；用户体验保持一致；无浏览器特有的兼容性问题

## 需求2：非标API支持法人授权静默签

### 功能测试

#### TL-011 原有参数调用接口兼容性验证
	前置条件：已配置非标API接口；准备企业授权相关材料；准备原有参数组合
	操作步骤：调用非标API企业线下授权接口→传入原有参数组合（不包含authType和legalRepAccountId）→上传企业授权书文件→提交授权申请
	预期结果：接口调用成功返回200状态码；系统按企业授权逻辑处理；授权书文件上传成功；授权申请提交成功

#### TL-012 authType为ENTERPRISE不传legalRepAccountId验证
	前置条件：已配置非标API接口；准备企业授权相关材料
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"ENTERPRISE"→不传入legalRepAccountId参数→上传企业授权书文件→提交授权申请
	预期结果：接口调用成功返回200状态码；系统识别为企业授权类型；授权书文件上传成功；按企业授权流程处理

#### TL-013 authType为LEGAL_PERSON传入有效legalRepAccountId验证
	前置条件：已配置非标API接口；准备法人授权相关材料；准备有效的法人个人账号ID
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→设置legalRepAccountId参数为有效的法人个人账号ID→上传法人线下授权书文件→提交授权申请
	预期结果：接口调用成功返回200状态码；系统识别为法人授权类型；法人账号ID正确关联；授权书文件上传成功；进入法人授权审核流程

#### TL-014 法人授权审核通过后静默签验证
	前置条件：法人授权已审核通过；法人个人账号已创建；准备需要签署的合同
	操作步骤：使用法人个人账号创建签署流程→设置autosign参数为true→指定法人印章→发起静默签署
	预期结果：系统验证法人静默签授权有效；静默签署自动执行完成；合同状态更新为已签署；文档显示法人印章签署效果

#### TL-015 企业授权和法人授权静默签区分验证
	前置条件：同时存在企业授权和法人授权；准备需要签署的合同
	操作步骤：使用企业授权账号创建签署流程→设置autosign参数为true→发起静默签署→使用法人授权账号创建签署流程→设置autosign参数为true→发起静默签署
	预期结果：系统正确区分企业静默签授权和法人静默签授权；两种授权类型的静默签署都正常执行；授权类型判断准确无误

### 异常测试

#### TL-016 authType为LEGAL_PERSON不传legalRepAccountId异常验证
	前置条件：已配置非标API接口；准备法人授权相关材料
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→不传入legalRepAccountId参数→尝试提交授权申请
	预期结果：接口返回400错误状态码；返回错误信息"legalRepAccountId参数为必填项"；授权申请提交失败

#### TL-017 authType为无效值异常验证
	前置条件：已配置非标API接口；准备授权相关材料
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"INVALID_TYPE"→尝试提交授权申请
	预期结果：接口返回400错误状态码；返回错误信息"authType参数值无效，支持ENTERPRISE或LEGAL_PERSON"；授权申请提交失败

#### TL-018 legalRepAccountId为不存在账号ID异常验证
	前置条件：已配置非标API接口；准备法人授权相关材料
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→设置legalRepAccountId参数为不存在的账号ID→尝试提交授权申请
	预期结果：接口返回400错误状态码；返回错误信息"legalRepAccountId对应的账号不存在"；授权申请提交失败

#### TL-019 AI审核法人授权书失败处理
	前置条件：已配置非标API接口；准备模糊不清的法人授权书图片
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→设置legalRepAccountId参数为有效的法人个人账号ID→上传模糊不清的法人授权书图片→提交授权申请
	预期结果：AI审核识别授权书质量问题；审核结果返回失败状态；返回具体失败原因"授权书图片不清晰，请重新上传"；提供重新上传指引

#### TL-020 法人授权过期后静默签失败处理
	前置条件：法人授权已过期；准备需要签署的合同
	操作步骤：使用过期法人授权的账号创建签署流程→设置autosign参数为true→尝试发起静默签署
	预期结果：系统检测到法人授权已过期；静默签署操作失败；返回错误信息"法人授权已过期，请重新进行授权"；签署流程状态保持未签署

### 性能测试

#### TL-021 静默签批量处理性能验证
	前置条件：法人授权已审核通过；准备100个需要签署的合同
	操作步骤：使用法人个人账号批量创建100个签署流程→为所有签署流程设置autosign参数为true→同时发起100个静默签署请求
	预期结果：100个静默签署请求的成功率大于95%；单个静默签署处理时间小于5秒；系统响应时间稳定；无性能瓶颈或超时异常

### 安全测试

#### TL-022 法人授权信息篡改安全验证
	前置条件：已配置非标API接口；准备法人授权相关材料
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→设置legalRepAccountId参数为他人的法人个人账号ID→尝试提交授权申请
	预期结果：系统验证法人身份信息不匹配；授权申请被拒绝；返回错误信息"法人身份验证失败"；记录安全异常日志

#### TL-023 静默签权限越权验证
	前置条件：用户A具有企业授权；用户B具有法人授权
	操作步骤：使用用户A的企业授权账号→尝试调用用户B的法人静默签功能→设置autosign参数为true→尝试发起静默签署
	预期结果：系统检测到权限不匹配；静默签署操作被拒绝；返回错误信息"无权限执行该操作"；记录越权尝试日志

## 需求3：法人章创建流程优化

### 功能测试

#### TL-024 优化后法人章创建流程验证
	前置条件：用户已登录SaaS系统；用户具有法人章创建权限；准备法人基本信息和身份证明文件
	操作步骤：进入法人章创建页面→填写法人姓名→填写法人身份证号→填写法人联系方式→上传法人身份证明文件→选择法人章样式→点击"提交创建申请"按钮
	预期结果：页面布局美观合理；信息填写界面友好直观；文件上传交互流畅；章样式选择直观；申请提交流程简化；整体用户体验提升

#### TL-025 PC端授权书预览功能验证
	前置条件：用户在PC端Chrome浏览器中访问法人章创建页面；系统中存在授权书文件
	操作步骤：进入法人章创建页面→点击"查看授权书"链接
	预期结果：授权书预览窗口正常打开；授权书内容清晰显示；预览界面无下载按钮；预览界面无另存为功能

#### TL-026 PC端授权书下载限制验证
	前置条件：用户在PC端Chrome浏览器中打开授权书预览界面
	操作步骤：在授权书预览界面右键点击→查看右键菜单选项
	预期结果：右键菜单中无"图片另存为"选项；右键菜单中无"复制图片地址"选项；无法通过右键菜单下载授权书

#### TL-027 H5端授权书预览功能验证
	前置条件：用户在移动设备H5浏览器中访问法人章创建页面；系统中存在授权书文件
	操作步骤：进入法人章创建页面→点击"查看授权书"链接
	预期结果：授权书预览界面在移动端正常显示；预览内容在小屏幕上清晰可读；预览界面适配移动端屏幕尺寸；无下载或保存功能

#### TL-028 H5端授权书保存限制验证
	前置条件：用户在移动设备H5浏览器中打开授权书预览界面
	操作步骤：在授权书预览界面长按内容→查看长按后的操作选项
	预期结果：长按后无"保存图片"选项；长按后无"复制"选项；无法通过长按操作保存授权书到本地

#### TL-029 法人章创建状态转换验证
	前置条件：用户已登录SaaS系统；用户具有法人章创建权限
	操作步骤：进入法人章创建页面（初始状态）→开始填写法人基本信息（进入信息填写状态）→完成信息填写点击下一步（进入文件上传状态）→完成文件上传点击下一步（进入样式选择状态）→完成样式选择点击提交（进入提交审核状态）
	预期结果：每个状态转换顺畅无阻；状态转换时页面UI正确更新；无法跳过必要的状态直接提交；状态回退功能正常工作

### 异常测试

#### TL-030 授权书文件损坏时预览异常处理
	前置条件：用户在PC端访问法人章创建页面；系统中存在损坏的授权书文件
	操作步骤：进入法人章创建页面→点击"查看授权书"链接
	预期结果：系统检测到授权书文件损坏；显示"授权书文件异常，无法预览"提示；提供联系客服的引导信息；不会导致页面崩溃

### 性能测试

#### TL-031 法人章创建页面加载性能验证
	前置条件：用户已登录SaaS系统；用户具有法人章创建权限；网络环境正常
	操作步骤：清空浏览器缓存→访问法人章创建页面→记录页面完全加载时间
	预期结果：页面首次加载时间小于3秒；页面资源加载完整无缺失；页面交互响应时间小于1秒；用户体验流畅

### 安全测试

#### TL-032 授权书URL直接访问安全验证
	前置条件：用户已获取授权书的预览URL地址
	操作步骤：复制授权书预览界面的URL地址→在新的浏览器标签页中直接访问该URL→尝试在未登录状态下访问该URL
	预期结果：直接访问URL返回403权限错误；未登录状态下访问返回401认证错误；无法绕过权限控制直接访问授权书；安全控制机制有效

### 兼容性测试

#### TL-033 跨浏览器兼容性验证
	前置条件：准备Chrome、Firefox、Safari浏览器；相同的测试数据
	操作步骤：在Chrome浏览器中完成法人章创建流程→在Firefox浏览器中完成法人章创建流程→在Safari浏览器中完成法人章创建流程
	预期结果：所有浏览器中页面显示一致；功能操作无异常；用户体验保持一致；无浏览器特有的兼容性问题

#### TL-034 移动设备兼容性验证
	前置条件：准备iOS和Android移动设备；相同的测试数据
	操作步骤：在iOS设备Safari浏览器中测试法人章创建流程→在Android设备Chrome浏览器中测试法人章创建流程→验证授权书预览功能在移动端的表现
	预期结果：移动端页面布局适配良好；触摸操作响应正常；授权书预览在移动端清晰显示；功能完整性与PC端一致

## 集成测试

### TL-035 印章前端裁剪与法人章创建集成验证
	前置条件：用户已登录SaaS系统；用户具有法人章创建权限；准备800KB的PNG格式印章图片文件
	操作步骤：进入法人章创建页面→填写法人基本信息→上传法人身份证明文件→上传800KB的法人印章图片→勾选"使用前端裁剪"选项→选择法人章样式→提交法人章创建申请
	预期结果：法人章创建流程正常执行；印章图片前端裁剪功能正常工作；前端裁剪后的印章正确保存；法人章创建成功

### TL-036 法人授权与静默签完整流程集成验证
	前置条件：已配置非标API接口；准备法人授权相关材料；准备需要签署的合同
	操作步骤：调用非标API企业线下授权接口→设置authType参数为"LEGAL_PERSON"→设置legalRepAccountId参数为有效的法人个人账号ID→上传法人线下授权书文件→等待AI审核通过→使用法人个人账号创建签署流程→设置autosign参数为true→发起静默签署
	预期结果：法人授权申请成功提交；AI审核正常通过；法人授权状态正确更新；静默签署功能正常执行；合同签署成功完成

### TL-037 三个需求端到端业务流程集成验证
	前置条件：用户已登录SaaS系统；具有完整业务权限；准备完整的测试数据
	操作步骤：使用优化后的法人章创建流程创建法人章→在创建过程中上传印章图片并使用前端裁剪功能→预览授权书（验证只能查看不能下载）→完成法人章创建→通过非标API配置法人授权静默签→创建需要法人签署的合同→执行静默签署流程
	预期结果：整个业务流程端到端执行成功；各功能模块协调工作无冲突；法人章创建、印章裁剪、授权配置、静默签署各环节正常；最终合同签署完成

### TL-038 数据一致性集成验证
	前置条件：完成法人章创建和授权配置；准备签署测试
	操作步骤：查询法人章创建记录中的印章信息→查询法人授权记录中的账号信息→创建签署流程并执行静默签→查询签署记录中的印章和授权信息→对比各模块中的数据一致性
	预期结果：法人章信息在各模块中保持一致；印章数据在创建和签署中一致；法人授权信息准确传递；数据完整性在整个流程中得到保障

## 回归测试

### TL-039 现有印章功能回归验证
	前置条件：用户已登录SaaS系统；用户具有印章管理权限；准备800KB的PNG格式印章图片文件
	操作步骤：进入印章管理页面→点击"创建印章"按钮→点击"上传印章图片"按钮→选择800KB的PNG格式印章图片文件→不勾选"使用前端裁剪"选项（使用原有逻辑）→点击"确认上传"按钮
	预期结果：原有印章创建功能完全正常；图片按原有逻辑正确处理；印章创建成功；功能表现与改动前一致

### TL-040 现有企业授权功能回归验证
	前置条件：已配置非标API接口；准备企业授权相关材料
	操作步骤：调用非标API企业线下授权接口→使用原有参数组合（不传入新增参数）→上传企业授权书文件→提交授权申请→使用企业授权进行签署操作
	预期结果：原有企业授权功能完全正常；授权流程与改动前一致；签署功能正常工作；现有客户使用无影响

### TL-041 现有签署流程回归验证
	前置条件：已配置签署环境；准备需要签署的合同；使用原有签署方式
	操作步骤：创建普通签署流程（非静默签）→添加签署人信息→发起签署流程→签署人完成在线签署→验证签署结果
	预期结果：原有签署流程功能完全正常；签署人操作体验无变化；签署结果正确生成；功能稳定性保持

### TL-042 系统整体稳定性回归验证
	前置条件：系统部署完成；准备完整的回归测试数据
	操作步骤：执行核心业务流程测试→执行高频使用功能测试→执行边界条件测试→监控系统性能指标→检查系统日志异常
	预期结果：系统整体功能稳定；核心业务流程正常；性能指标在正常范围内；无新增异常日志；系统可用性保持

## 冒烟测试用例

### MYTL-001 印章前端裁剪基本功能
	前置条件：用户已登录；具有印章管理权限
	操作步骤：上传小于1M的印章图片→勾选前端裁剪选项→确认创建
	预期结果：前端裁剪功能正常；印章创建成功

### MYTL-002 现有接口兼容性基本验证
	前置条件：非标API接口正常；使用原有参数
	操作步骤：调用接口不传入新增参数→上传企业授权书→完成授权流程
	预期结果：接口调用成功；现有功能正常；兼容性良好

### MYTL-003 法人授权新参数基本功能
	前置条件：非标API接口正常；法人个人账号已创建
	操作步骤：设置authType="LEGAL_PERSON"→设置legalRepAccountId为法人账号ID→上传法人授权书
	预期结果：新参数正确识别；法人授权创建成功；授权类型正确

### MYTL-004 法人授权静默签基本功能
	前置条件：法人已通过新接口完成授权；配置了非标API
	操作步骤：使用法人账号调用静默签API→设置autosign=true→验证法人静默签授权有效性→执行签署
	预期结果：法人授权有效性判断正确；静默签署自动完成；无需人工干预；区分企业和法人授权

### MYTL-005 法人章创建流程优化基本验证
	前置条件：用户具有法人章创建权限
	操作步骤：进入优化后的创建页面→完成基本信息填写→预览授权书（不下载）
	预期结果：页面加载快速；流程更加简化；预览功能正常且无法下载

## 线上验证用例

### PATL-001 印章前端裁剪线上验证
	前置条件：线上环境已部署；真实用户使用
	操作步骤：在生产环境创建印章→使用前端裁剪功能→验证裁剪效果
	预期结果：生产环境功能正常；裁剪效果符合预期

### PATL-002 现有客户接口兼容性线上验证
	前置条件：线上环境；真实的现有客户；原有调用方式
	操作步骤：现有客户使用原有参数调用接口→完成企业授权流程→验证授权功能正常
	预期结果：现有客户无感知升级；原有功能完全正常；接口兼容性100%

### PATL-003 法人授权静默签线上验证
	前置条件：线上环境；真实法人授权；通过新接口创建
	操作步骤：使用新参数创建法人授权→执行真实的法人静默签署→验证授权类型区分→验证签署结果
	预期结果：法人授权创建成功；授权类型正确区分；线上静默签正常；签署结果有效

### PATL-004 法人章创建流程线上验证
	前置条件：线上环境；真实用户操作
	操作步骤：真实用户使用优化后流程→验证授权书预览限制
	预期结果：流程优化效果明显；下载限制有效
