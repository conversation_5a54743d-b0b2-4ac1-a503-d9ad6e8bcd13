# 批量签接口支持未传入姓名的签署流程-测试用例

## 需求概述

**需求名称**：批量签接口支持未传入姓名的签署流程

**需求背景**：平台方在收集信息时不一定能够收集到经办人的姓名，或者该经办人的手机号属于值班手机号或公用手机号，此时要指定姓名会造成流程堵塞。

**核心改造**：
1. 放开获取批量签链接时对未指定姓名的校验拦截
2. 在批量签页面增加实名认证入口
3. 支持企业签署方仅使用企业章且未传经办人姓名的场景
4. 支持个人签署方未传姓名的场景

**影响接口**：
- `{flowId}/organ/auth/{orgId}/identifyUrl` - 企业签署方批量签链接
- `{flowId}/individual/realNameWill/{accountId}/identifyUrl` - 个人签署方批量签链接

## 基础功能测试

### 企业签署方未传姓名场景

#### TL-企业签署方仅使用企业章未传姓名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章，未传经办人姓名；企业未实名；

##### 步骤一：调用POST接口{flowId}/organ/auth/{orgId}/identifyUrl获取批量签链接

##### 步骤二：检查接口返回结果，验证返回字段完整性

##### 步骤三：访问返回的批量签页面链接

##### 步骤四：检查页面展示内容和功能入口

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：返回realNameFlowId、url、shortLink字段完整；3：页面正常加载，展示企业实名认证入口；4：页面显示待签署流程信息；5：页面交互功能正常；

#### TL-企业签署方使用个人章未传姓名校验拦截验证

##### PD-前置条件：创建1个签署流程；企业签署方使用个人章（经办人签），未传经办人姓名；

##### 步骤一：调用POST接口{flowId}/organ/auth/{orgId}/identifyUrl获取批量签链接

##### 步骤二：检查接口返回结果和错误信息

##### ER-预期结果：1：接口调用失败，被校验拦截；2：返回明确的错误信息，提示经办人签必须指定姓名；3：错误码和错误信息准确；

#### TL-企业签署方已实名仅使用企业章未传姓名验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章，未传经办人姓名；企业已实名；

##### 步骤一：调用POST接口{flowId}/organ/auth/{orgId}/identifyUrl获取批量签链接

##### 步骤二：访问返回的批量签页面链接

##### 步骤三：检查页面展示内容和印章选择功能

##### ER-预期结果：1：接口调用成功；2：页面直接展示企业印章选择，无需实名认证；3：可以正常选择企业印章进行批量签署；4：页面功能完整正常；

### 个人签署方未传姓名场景

#### TL-个人签署方未传姓名未实名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名（无gid）；

##### 步骤一：调用POST接口{flowId}/individual/realNameWill/{accountId}/identifyUrl获取批量签链接

##### 步骤二：检查接口返回结果，验证type字段和url字段

##### 步骤三：访问返回的批量签页面链接

##### 步骤四：检查页面展示内容和实名认证入口

##### ER-预期结果：1：接口调用成功，返回type=0（实名类型）；2：url指向实名认证页面；3：页面展示个人实名认证入口；4：页面显示待签署流程信息；5：实名认证入口功能正常；

#### TL-个人签署方未传姓名已实名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名（有gid，状态为已实名）；

##### 步骤一：调用POST接口{flowId}/individual/realNameWill/{accountId}/identifyUrl获取批量签链接

##### 步骤二：检查接口返回结果，验证type字段和url字段

##### 步骤三：访问返回的批量签页面链接

##### 步骤四：检查页面展示内容和印章选择功能

##### ER-预期结果：1：接口调用成功，返回type=1（意愿类型）；2：url指向意愿确认页面；3：页面直接展示个人印章选择；4：可以正常选择个人印章进行批量签署；5：印章选择功能完整；

#### TL-个人签署方未传姓名实名中获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户实名中（有gid，状态为实名中）；

##### 步骤一：调用POST接口{flowId}/individual/realNameWill/{accountId}/identifyUrl获取批量签链接

##### 步骤二：检查接口返回结果，验证type字段和url字段

##### 步骤三：访问返回的批量签页面链接

##### 步骤四：检查页面展示的继续实名认证功能

##### ER-预期结果：1：接口调用成功，返回type=0（实名类型）；2：url指向继续实名页面；3：页面展示继续完成实名认证的入口；4：继续实名功能正常；

## 完整流程测试

### 企业签署方完整流程

#### TL-企业未实名仅使用企业章未传姓名完整批量签流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章，未传经办人姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，点击企业实名认证入口

##### 步骤三：完成企业实名认证流程（填写企业信息、上传证件等）

##### 步骤四：实名成功后返回批量签页面

##### 步骤五：页面刷新展示企业印章选择

##### 步骤六：选择企业印章，提交批量签署

##### 步骤七：检查签署结果和流程状态

##### ER-预期结果：1：完整流程执行成功；2：企业实名认证正常；3：实名后页面正确刷新；4：企业印章正确生成和展示；5：批量签署执行成功；6：签署流程状态正确更新为已签署；

#### TL-企业已实名仅使用企业章未传姓名完整批量签流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章，未传经办人姓名；企业已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：直接选择企业印章，提交批量签署

##### 步骤四：检查签署结果和流程状态

##### ER-预期结果：1：跳过实名认证环节；2：直接展示企业印章选择；3：批量签署正常执行；4：签署流程状态正确更新为已签署；

### 个人签署方完整流程

#### TL-个人未实名未传姓名完整批量签流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，点击个人实名认证入口

##### 步骤三：完成个人实名认证流程（填写个人信息、人脸识别等）

##### 步骤四：实名成功后返回批量签页面

##### 步骤五：页面刷新展示个人印章选择

##### 步骤六：选择个人印章，提交批量签署

##### 步骤七：检查签署结果和流程状态

##### ER-预期结果：1：完整流程执行成功；2：个人实名认证正常；3：实名后页面正确刷新；4：个人印章正确生成和展示；5：批量签署执行成功；6：签署流程状态正确更新为已签署；

#### TL-个人已实名未传姓名完整批量签流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：直接选择个人印章，提交批量签署

##### 步骤四：检查签署结果和流程状态

##### ER-预期结果：1：跳过实名认证环节；2：直接展示个人印章选择；3：批量签署正常执行；4：签署流程状态正确更新为已签署；

## 多流程混合场景测试

### 未传姓名流程组合场景

#### TL-多个未传姓名流程批量签验证

##### PD-前置条件：创建3个签署流程；所有流程个人签署方均未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，传入所有3个flowId

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：实名成功后选择个人印章，批量签署所有流程

##### 步骤四：检查签署结果和所有流程状态

##### ER-预期结果：1：接口调用成功；2：页面正确展示3个待签署流程；3：实名认证正常；4：可以一次性签署所有流程；5：所有流程状态正确更新为已签署；

#### TL-未传姓名与已传姓名流程混合批量签验证

##### PD-前置条件：创建3个签署流程；2个流程个人签署方未传姓名；1个流程个人签署方已传姓名（张三）；用户未实名；

##### 步骤一：调用获取批量签链接接口，传入所有3个flowId

##### 步骤二：访问批量签页面，完成个人实名认证（必须实名为张三）

##### 步骤三：实名成功后选择个人印章，批量签署所有流程

##### 步骤四：检查签署结果和流程状态

##### ER-预期结果：1：接口调用成功；2：实名人强制为张三；3：实名成功后可以签署所有流程；4：未传姓名的流程也能正常签署；5：所有流程状态正确更新；

#### TL-已传不同姓名流程批量签校验拦截验证

##### PD-前置条件：创建2个签署流程；1个流程个人签署方传姓名张三；1个流程个人签署方传姓名李四；

##### 步骤一：调用获取批量签链接接口，传入2个flowId

##### 步骤二：检查接口返回结果和错误信息

##### ER-预期结果：1：接口调用失败；2：返回错误信息："流程中各签署人指定身份信息不一致，请确认后重试"；3：错误码准确；

### 企业个人混合场景

#### TL-企业个人混合未传姓名批量签验证

##### PD-前置条件：创建2个签署流程；1个企业签署方（仅使用企业章，未传姓名）；1个个人签署方（未传姓名）；企业和个人均未实名；

##### 步骤一：调用获取批量签链接接口，传入2个flowId

##### 步骤二：访问批量签页面

##### 步骤三：分别完成企业实名认证和个人实名认证

##### 步骤四：选择企业印章和个人印章，提交批量签署

##### 步骤五：检查签署结果和流程状态

##### ER-预期结果：1：接口调用成功；2：页面同时展示企业和个人实名认证入口；3：两种实名认证都能正常完成；4：印章选择功能正常；5：批量签署执行成功；6：所有流程状态正确更新；

#### TL-已实名用户与未传姓名流程混合验证

##### PD-前置条件：创建2个签署流程；个人签署方均未传姓名；用户已实名（姓名为张三）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：直接选择个人印章，批量签署所有流程

##### 步骤四：检查签署结果

##### ER-预期结果：1：接口调用成功；2：跳过实名认证环节；3：直接展示印章选择；4：可以正常签署所有未传姓名的流程；5：流程状态正确更新；

#### TL-已实名用户与指定不同姓名流程校验拦截验证

##### PD-前置条件：创建1个签署流程；个人签署方指定姓名为李四；用户已实名（姓名为张三）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### ER-预期结果：1：接口调用失败；2：返回错误信息："流程中各签署人指定身份信息不一致，请确认后重试"；

## 场景分析图覆盖测试

### 签署人未实名场景

#### TL-流程1未指定姓名流程2未指定姓名场景验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方未指定姓名；流程2个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：选择个人印章，批量签署两个流程

##### ER-预期结果：1：改造后流程可以正常走下去；2：页面上可以进行实名认证；3：实名后选章签署成功；4：两个流程状态都正确更新；

#### TL-流程1未指定姓名流程2指定姓名张三场景验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方未指定姓名；流程2个人签署方指定姓名张三；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证（强制实名为张三）

##### 步骤三：选择个人印章，批量签署两个流程

##### ER-预期结果：1：改造后流程可以正常走下去；2：实名人强制是张三；3：使用预览章，提交批量签署时去实名，实名代替意愿，完成签署；

### 签署人已实名场景

#### TL-已实名用户流程1未指定姓名流程2未指定姓名场景验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方未指定姓名；流程2个人签署方未指定姓名；用户已实名（姓名张三）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，直接选择印章批量签署

##### ER-预期结果：1：改造后流程能正常走；2：跳过实名认证环节；3：直接进行批量签署；

#### TL-已实名用户流程1未指定姓名流程2指定张三场景验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方未指定姓名；流程2个人签署方指定姓名张三；用户已实名（姓名张三）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，直接选择印章批量签署

##### ER-预期结果：1：改造后流程能正常走；2：两种类型流程都能正常签署；

#### TL-已实名用户流程1未指定姓名流程2指定姓名李四校验拦截验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方未指定姓名；流程2个人签署方指定姓名李四；用户已实名（姓名张三）；

##### 步骤一：调用获取批量签链接接口

##### ER-预期结果：1：获取批量签署链接失败；2：报错："流程中各签署人指定身份信息不一致，请确认后重试"；

#### TL-已实名用户流程1指定姓名李四流程2指定姓名李四身份不一致验证

##### PD-前置条件：创建2个签署流程；流程1个人签署方指定姓名李四；流程2个人签署方指定姓名李四；用户已实名（姓名张三）；

##### 步骤一：调用获取批量签链接接口

##### ER-预期结果：1：触发身份不一致；2：目前流程无法走下去，后续需要考虑优化；

## 边界场景和异常测试

### 实名认证异常场景

#### TL-个人实名认证失败重试完整流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：点击个人实名认证入口，故意输入错误信息导致实名失败

##### 步骤三：页面显示实名失败原因，点击重新实名入口

##### 步骤四：使用正确信息重新完成实名认证

##### 步骤五：实名成功后返回批量签页面，选择印章完成签署

##### ER-预期结果：1：实名失败后页面显示明确的失败原因；2：提供重新实名的入口；3：重试流程完整可用；4：最终实名成功后批量签功能正常；5：整个流程可以完整执行；

#### TL-企业实名认证失败重试完整流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：点击企业实名认证入口，故意输入错误信息导致实名失败

##### 步骤三：页面显示实名失败原因，点击重新实名入口

##### 步骤四：使用正确信息重新完成企业实名认证

##### 步骤五：实名成功后返回批量签页面，选择企业印章完成签署

##### ER-预期结果：1：企业实名失败后页面显示明确的失败原因；2：提供重新实名的入口；3：重试流程完整可用；4：最终实名成功后批量签功能正常；5：整个流程可以完整执行；

### 网络和服务异常场景

#### TL-实名认证过程中网络异常恢复处理验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：开始个人实名认证流程，在认证过程中模拟网络中断

##### 步骤三：页面显示网络异常提示，提供重试选项

##### 步骤四：网络恢复后点击重试，继续完成实名认证

##### 步骤五：实名成功后返回批量签页面，完成签署流程

##### ER-预期结果：1：网络异常时显示友好的错误提示；2：提供明确的重试机制；3：网络恢复后可继续认证流程；4：不会出现数据不一致问题；5：最终可完整执行批量签署；

#### TL-批量签页面加载异常处理验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：在页面加载过程中模拟网络异常

##### 步骤三：页面显示加载异常提示

##### 步骤四：网络恢复后重新访问批量签链接

##### 步骤五：正常完成批量签署流程

##### ER-预期结果：1：页面加载异常时显示友好提示；2：提供重新加载的选项；3：网络恢复后可正常访问；4：批量签功能完全正常；

### 数据状态异常场景

#### TL-多oid对应同一gid批量签处理验证

##### PD-前置条件：同一用户注册了3个账号（oid1、oid2、oid3）；已用oid1完成实名认证（生成gid）；创建签署流程时个人签署方未传姓名；

##### 步骤一：使用oid1账号调用获取批量签链接接口，完成批量签署

##### 步骤二：使用oid2账号调用获取同一类型流程的批量签链接接口

##### 步骤三：访问批量签页面，检查印章选择情况

##### 步骤四：使用oid3账号重复相同操作

##### ER-预期结果：1：三个oid账号都能正常获取批量签链接；2：三个账号看到相同的个人印章（基于同一gid）；3：任一账号都可正常使用印章签署；4：签署记录正确关联到对应的oid；5：gid共享机制正常工作；

#### TL-签署流程状态变更时批量签处理验证

##### PD-前置条件：创建5个签署流程；个人签署方未传姓名；用户正在实名认证过程中；

##### 步骤一：调用获取批量签链接接口，访问批量签页面，开始实名认证

##### 步骤二：在实名认证过程中，其他用户取消了其中2个签署流程

##### 步骤三：完成实名认证，返回批量签页面

##### 步骤四：页面检测到流程状态变更，及时更新流程列表

##### 步骤五：对剩余有效的3个流程执行批量签署

##### ER-预期结果：1：实名认证流程不受签署流程状态变更影响；2：页面能及时检测并更新流程状态；3：已取消的流程从列表中移除；4：剩余有效流程可正常签署；5：整体流程处理正确；

### batchSerialId参数测试

#### TL-batchSerialId参数对个人签署方影响验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用个人批量签链接接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：传入无效batchSerialId调用接口

##### 步骤四：分别访问返回的批量签页面，检查签署操作人信息显示

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId时，页面正确显示签署操作人信息；3：传入无效batchSerialId时，返回相应错误或使用默认处理；4：batchSerialId正确影响签署操作人信息获取；

#### TL-batchSerialId参数对企业签署方影响验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业已实名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用企业批量签链接接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：分别访问返回的批量签页面，检查签署操作人信息显示

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId时，页面正确显示企业签署操作人信息；3：batchSerialId参数对企业签署方也有效；

## 兼容性和回归测试

### 原有功能完整兼容性验证

#### TL-传统个人签署流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；个人签署方已指定姓名和手机号；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示和功能

##### 步骤三：选择个人印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### 步骤五：检查签署结果和数据格式

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本完全一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：数据格式和字段完全兼容；6：新版本对原有功能零影响；

#### TL-传统企业签署流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；企业签署方已指定经办人姓名；企业已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示和功能

##### 步骤三：选择企业印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本完全一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：新版本对原有企业签署功能零影响；

#### TL-混合新旧功能场景完整验证

##### PD-前置条件：创建10个签署流程；5个按原有方式创建（已传姓名）；5个按新方式创建（未传姓名）；包含个人和企业签署；用户和企业均已实名；

##### 步骤一：调用获取批量签链接接口，传入所有10个flowId

##### 步骤二：访问批量签页面，检查所有流程的展示

##### 步骤三：验证新旧两种类型流程在页面上的一致性

##### 步骤四：分别选择个人印章和企业印章，批量签署所有10个流程

##### 步骤五：检查签署结果和状态更新

##### ER-预期结果：1：新旧两种类型流程在页面上展示完全一致；2：用户无法区分哪些是新功能哪些是旧功能；3：批量签署逻辑对两种类型处理完全相同；4：所有流程签署状态正确更新；5：签署数据格式完全一致；

## 接口详细测试

### 核心接口全面验证

#### TL-individual-realNameWill-identifyUrl接口全场景验证

##### PD-前置条件：准备不同状态的用户和签署流程；

##### 步骤一：未实名用户（无gid）调用接口，验证返回type=0，url指向实名页面

##### 步骤二：实名中用户（有gid，状态为实名中）调用接口，验证返回type=0，url指向继续实名页面

##### 步骤三：已实名用户（有gid，状态为已实名）调用接口，验证返回type=1，url指向意愿确认页面

##### 步骤四：gid异常用户调用接口，验证错误处理

##### 步骤五：验证batchSerialId参数对各种场景的影响

##### ER-预期结果：1：不同用户状态返回正确的type值；2：url指向正确的页面类型；3：所有必要字段正确返回；4：batchSerialId正确影响操作人信息；5：异常状态正确处理；

#### TL-organ-auth-identifyUrl接口全场景验证

##### PD-前置条件：准备不同状态的企业和签署流程；

##### 步骤一：企业未实名且仅使用企业章未传姓名时调用接口{flowId}/organ/auth/{orgId}/identifyUrl

##### 步骤二：企业已实名且仅使用企业章未传姓名时调用接口

##### 步骤三：企业使用个人章未传姓名时调用接口（应被拦截）

##### 步骤四：验证batchSerialId参数对各种场景的影响

##### 步骤五：验证接口返回字段的正确性

##### ER-预期结果：1：仅使用企业章未传姓名时接口调用成功；2：使用个人章未传姓名时被正确拦截；3：返回realNameFlowId、url、shortLink字段正确；4：batchSerialId正确影响操作人信息；5：接口响应格式符合文档规范；

#### TL-接口参数边界和异常全面验证

##### PD-前置条件：准备各种边界和异常参数；

##### 步骤一：flowId为空、null、不存在等情况的处理验证

##### 步骤二：accountId/orgId为空、null、不存在等情况的处理验证

##### 步骤三：batchSerialId为空、null、无效等情况的处理验证

##### 步骤四：请求格式错误（非POST、缺少Content-Type等）的处理验证

##### 步骤五：并发大量请求的处理验证

##### ER-预期结果：1：所有异常参数都有明确的错误响应；2：错误信息准确且用户友好；3：不会出现系统异常或崩溃；4：并发请求处理正常；5：接口响应时间在合理范围内；

## 冒烟测试用例

### 核心流程快速验证

#### MYTL-企业签署方仅使用企业章未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成企业实名认证

##### 步骤三：选择生成的企业印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：企业实名认证正常；3：企业印章生成正确；4：签署执行成功；

#### MYTL-个人签署方未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：选择生成的个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：个人实名认证正常；3：个人印章生成正确；4：签署执行成功；

#### MYTL-混合签署主体未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；包含企业签署方（仅使用企业章，未传姓名）和个人签署方（未传姓名）；均未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，分别完成企业和个人实名认证

##### 步骤三：选择企业印章和个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：企业和个人实名认证都正常；3：印章生成正确；4：批量签署执行成功；

#### MYTL-企业使用个人章未传姓名校验拦截验证

##### PD-前置条件：创建1个签署流程；企业签署方使用个人章（经办人签）未传姓名；

##### 步骤一：调用获取批量签链接接口

##### ER-预期结果：1：接口调用失败，被校验拦截；2：返回明确的错误信息；

#### MYTL-新旧功能兼容性基本验证

##### PD-前置条件：创建2个签署流程；1个已传姓名，1个未传姓名；用户已实名；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：两种类型流程都能正常签署；2：功能完全兼容；

## 线上验证用例

### 关键业务流程线上验证

#### PATL-企业签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；企业签署方仅使用企业章未传姓名；真实企业未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的企业实名认证

##### 步骤三：使用生成的企业印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据完整性

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：企业实名认证数据正确保存；3：企业印章生成符合规范；4：签署结果准确有效；5：业务数据完整一致；

#### PATL-个人签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；个人签署方未传姓名；真实用户未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的个人实名认证

##### 步骤三：使用生成的个人印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据完整性

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：个人实名认证数据正确保存；3：个人印章生成符合规范；4：签署结果准确有效；5：业务数据完整一致；

#### PATL-复杂混合场景线上验证

##### PD-前置条件：线上环境；创建包含企业签署、个人签署、已传姓名、未传姓名等多种类型的10个真实签署流程；

##### 步骤一：执行完整的批量签流程，包含实名认证和签署

##### 步骤二：验证各种场景的处理正确性

##### 步骤三：检查业务数据和签署记录

##### ER-预期结果：1：复杂混合场景在线上环境正常处理；2：各种状态组合都能正确执行；3：业务逻辑完全正确；4：数据一致性良好；

#### PATL-原有功能兼容性线上验证

##### PD-前置条件：线上环境；原有传姓名的真实签署流程；

##### 步骤一：执行原有的批量签流程

##### 步骤二：对比新旧版本的功能表现

##### 步骤三：验证业务数据格式和内容

##### ER-预期结果：1：原有功能完全正常；2：新版本对原有功能零影响；3：用户体验保持一致；4：业务数据格式完全兼容；
