# 批量签接口支持未传入姓名的签署流程-测试用例

## 企业签署方未传姓名场景测试

### 企业签署方仅使用企业章-未传姓名场景

#### TL-企业签署方仅使用企业章未传姓名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章；未指定经办人姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口{flowId}/organ/auth/{orgId}/identifyUrl

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### 步骤四：页面展示企业实名认证入口

##### 步骤五：完成企业实名认证

##### 步骤六：实名成功后页面刷新，展示企业印章选择

##### 步骤七：选择企业印章，提交批量签署

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：返回realNameFlowId、url、shortLink字段；3：成功进入批量签页面；4：企业实名认证流程正常；5：企业印章自动生成并可选择；6：批量签署执行成功；

#### TL-企业签署方已实名仅使用企业章未传姓名批量签验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章；未指定经办人姓名；企业已实名；

##### 步骤一：调用获取批量签链接接口{flowId}/organ/auth/{orgId}/identifyUrl

##### 步骤二：访问返回的批量签链接，直接进入批量签页面

##### 步骤三：页面直接展示企业印章选择区域

##### 步骤四：选择企业印章，提交批量签署

##### ER-预期结果：1：接口调用成功；2：直接进入批量签页面，跳过实名认证；3：企业印章选择功能正常；4：批量签署执行成功；

#### TL-企业签署方使用个人章未传姓名校验拦截验证

##### PD-前置条件：创建1个签署流程；企业签署方使用个人章（经办人签）；未指定经办人姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### ER-预期结果：1：接口调用失败，被校验拦截；2：返回错误信息，提示企业签署方使用个人章时必须指定经办人姓名；3：不允许获取批量签链接；

### 混合签署主体场景

#### TL-企业签署方和个人签署方混合未传姓名场景验证

##### PD-前置条件：创建1个签署流程；包含企业签署方（仅使用企业章，未传姓名）和个人签署方（未传姓名）；企业和个人均未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：页面同时展示企业实名认证和个人实名认证入口

##### 步骤四：先完成企业实名认证，页面刷新展示企业印章选择

##### 步骤五：再完成个人实名认证，页面刷新展示个人印章选择

##### 步骤六：分别选择企业印章和个人印章，提交批量签署

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：页面正确展示多种主体的实名认证入口；3：企业和个人实名认证流程都正常；4：印章选择功能正常；5：批量签署执行成功；

#### TL-企业签署方未传姓名个人签署方已传姓名混合场景验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；个人签署方已传姓名；企业和个人均已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：页面直接展示企业印章选择和个人印章选择

##### 步骤四：分别选择企业印章和个人印章，提交批量签署

##### ER-预期结果：1：接口调用成功；2：页面正确展示两种主体的印章选择；3：未传姓名和已传姓名的签署方处理一致；4：批量签署执行成功；

## 多个签署流程组合场景测试

### 多流程-企业签署方未传姓名场景

#### TL-多个流程企业签署方均未传姓名批量签验证

##### PD-前置条件：创建5个签署流程；所有流程企业签署方均仅使用企业章且未传姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口，传入所有flowId

##### 步骤二：访问批量签页面

##### 步骤三：页面展示企业实名认证入口和5个待签署流程

##### 步骤四：完成企业实名认证

##### 步骤五：实名成功后页面刷新，展示企业印章选择和5个可签署流程

##### 步骤六：选择企业印章，批量签署所有5个流程

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：页面正确展示所有5个流程；3：企业实名认证一次完成后，所有流程都可签署；4：可以一次性批量签署所有流程；5：所有5个流程状态都正确更新为已签署；

#### TL-多流程混合企业签署方传姓名状态批量签验证

##### PD-前置条件：创建5个签署流程；3个流程企业签署方未传姓名（仅使用企业章）；2个流程企业签署方已传姓名；企业已实名；

##### 步骤一：调用获取批量签链接接口，传入所有flowId

##### 步骤二：访问批量签页面

##### 步骤三：页面展示企业印章选择和5个可签署流程

##### 步骤四：选择企业印章，批量签署所有5个流程

##### ER-预期结果：1：接口调用成功；2：未传姓名和已传姓名的流程在页面上展示一致；3：可统一选择印章进行批量签署；4：批量签署逻辑对两种类型流程处理一致；5：所有流程签署状态正确更新；

## 个人签署方未传姓名场景测试

### 个人签署方未传姓名-不同实名状态场景

#### TL-个人未实名未传姓名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未指定姓名；用户有oid但未实名（无gid）；

##### 步骤一：调用获取批量签链接接口{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### 步骤四：页面展示个人实名认证入口

##### 步骤五：完成个人实名认证（填写姓名、身份证号、人脸识别）

##### 步骤六：实名成功后页面刷新，展示个人印章选择

##### 步骤七：选择个人印章，提交批量签署

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：返回type=0（实名类型）、bizId、realNameFlowId、url、shortUrl字段；3：成功进入批量签页面；4：个人实名认证流程正常；5：个人印章自动生成并可选择；6：批量签署执行成功；

#### TL-个人已实名未传姓名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未指定姓名；用户已完成实名认证（有gid）；

##### 步骤一：调用获取批量签链接接口{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### 步骤四：页面直接展示个人印章选择区域

##### 步骤五：选择个人印章，提交批量签署

##### ER-预期结果：1：接口调用成功；2：返回type=1（意愿类型）、bizId、realNameFlowId、url、shortUrl字段；3：直接进入批量签页面，跳过实名认证；4：个人印章选择功能正常；5：批量签署执行成功；

#### TL-个人实名中状态未传姓名获取批量签链接验证

##### PD-前置条件：创建1个签署流程；个人签署方未指定姓名；用户有gid但实名状态为"实名中"；

##### 步骤一：调用获取批量签链接接口{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### 步骤四：页面展示继续实名认证入口

##### 步骤五：完成剩余的实名认证步骤

##### 步骤六：实名成功后页面刷新，展示个人印章选择

##### 步骤七：选择个人印章，提交批量签署

##### ER-预期结果：1：接口调用成功；2：返回type=0（实名类型）相关字段；3：页面正确识别实名中状态；4：可继续完成实名认证流程；5：实名完成后功能正常；6：批量签署执行成功；

### 多个个人签署流程场景

#### TL-多个流程个人均未实名未传姓名批量签验证

##### PD-前置条件：创建5个签署流程；所有流程个人签署方均未指定姓名；用户未实名（无gid）；

##### 步骤一：调用获取批量签链接接口，传入所有flowId和accountId

##### 步骤二：访问批量签页面

##### 步骤三：页面展示个人实名认证入口和5个待签署流程

##### 步骤四：完成个人实名认证流程

##### 步骤五：实名成功后页面刷新，展示个人印章选择和5个可签署流程

##### 步骤六：选择个人印章，批量签署所有5个流程

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：页面正确展示所有5个流程；3：个人实名认证一次完成后，所有流程都可签署；4：可以一次性批量签署所有流程；5：所有5个流程状态都正确更新为已签署；

#### TL-多流程混合个人传姓名状态批量签验证

##### PD-前置条件：创建5个签署流程；3个流程个人签署方未传姓名；2个流程个人签署方已传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口，传入所有flowId和accountId

##### 步骤二：访问批量签页面

##### 步骤三：页面展示个人印章选择和5个可签署流程

##### 步骤四：选择个人印章，批量签署所有5个流程

##### ER-预期结果：1：接口调用成功；2：未传姓名和已传姓名的流程在页面上展示一致；3：可统一选择印章进行批量签署；4：批量签署逻辑对两种类型流程处理一致；5：所有流程签署状态正确更新；

## 复杂混合场景测试

### 多种签署主体混合场景

#### TL-企业个人混合未传姓名完整批量签流程验证

##### PD-前置条件：创建5个签署流程；2个企业签署方（仅使用企业章，未传姓名）；2个个人签署方（未传姓名）；1个企业经办人签署（已传姓名）；企业和个人均未实名；

##### 步骤一：调用获取批量签链接接口，传入所有flowId和相关ID

##### 步骤二：访问批量签页面

##### 步骤三：页面同时展示企业实名认证入口、个人实名认证入口，显示5个待签署流程

##### 步骤四：先完成企业实名认证，页面刷新展示企业印章选择

##### 步骤五：再完成个人实名认证，页面刷新展示个人印章选择

##### 步骤六：分别选择企业印章和个人印章，批量签署所有流程

##### ER-预期结果：1：接口调用成功，不被校验拦截；2：页面正确展示多种主体的实名认证入口；3：企业和个人实名认证流程都正常；4：印章选择功能正常；5：可同时处理多种签署主体；6：所有流程签署状态正确更新；

#### TL-部分流程未传姓名选择性签署验证

##### PD-前置条件：创建10个签署流程；5个企业签署方未传姓名；3个个人签署方未传姓名；2个个人签署方已传姓名；企业和个人均已实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面展示企业印章选择、个人印章选择和10个可签署流程

##### 步骤三：只选择企业印章，不选择个人印章

##### 步骤四：提交批量签署请求

##### 步骤五：检查签署结果

##### ER-预期结果：1：页面正确展示所有流程和印章选择；2：可以选择性使用部分主体的印章；3：只有企业签署相关的5个流程执行签署；4：个人签署相关的5个流程状态不变；5：系统正确区分不同签署主体和传姓名状态；

### batchSerialId参数影响场景

#### TL-batchSerialId参数对签署操作人信息获取影响验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用个人批量签链接接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：传入无效batchSerialId调用接口

##### 步骤四：分别访问返回的批量签页面，检查签署操作人信息显示

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId时，页面正确显示签署操作人信息；3：传入无效batchSerialId时，返回相应错误或使用默认处理；4：batchSerialId正确影响签署操作人信息获取；

#### TL-企业签署方batchSerialId参数影响验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业已实名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用企业批量签链接接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：分别访问返回的批量签页面，检查签署操作人信息显示

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId时，页面正确显示企业签署操作人信息；3：batchSerialId参数对企业签署方也有效；

## 异常处理和边界场景测试

### 实名认证异常场景

#### TL-个人实名认证失败重试完整流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：点击个人实名认证入口，故意输入错误信息导致实名失败

##### 步骤三：页面显示实名失败原因，点击重新实名入口

##### 步骤四：使用正确信息重新完成实名认证

##### 步骤五：实名成功后返回批量签页面，选择印章完成签署

##### ER-预期结果：1：实名失败后页面显示明确的失败原因；2：提供重新实名的入口；3：重试流程完整可用；4：最终实名成功后批量签功能正常；5：整个流程可以完整执行；

#### TL-企业实名认证失败重试完整流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：点击企业实名认证入口，故意输入错误信息导致实名失败

##### 步骤三：页面显示实名失败原因，点击重新实名入口

##### 步骤四：使用正确信息重新完成企业实名认证

##### 步骤五：实名成功后返回批量签页面，选择企业印章完成签署

##### ER-预期结果：1：企业实名失败后页面显示明确的失败原因；2：提供重新实名的入口；3：重试流程完整可用；4：最终实名成功后批量签功能正常；5：整个流程可以完整执行；

### 网络和服务异常场景

#### TL-实名认证过程中网络异常恢复处理验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：开始个人实名认证流程，在认证过程中模拟网络中断

##### 步骤三：页面显示网络异常提示，提供重试选项

##### 步骤四：网络恢复后点击重试，继续完成实名认证

##### 步骤五：实名成功后返回批量签页面，完成签署流程

##### ER-预期结果：1：网络异常时显示友好的错误提示；2：提供明确的重试机制；3：网络恢复后可继续认证流程；4：不会出现数据不一致问题；5：最终可完整执行批量签署；

#### TL-批量签页面加载异常处理验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：在页面加载过程中模拟网络异常

##### 步骤三：页面显示加载异常提示

##### 步骤四：网络恢复后重新访问批量签链接

##### 步骤五：正常完成批量签署流程

##### ER-预期结果：1：页面加载异常时显示友好提示；2：提供重新加载的选项；3：网络恢复后可正常访问；4：批量签功能完全正常；

### 数据状态异常场景

#### TL-多oid对应同一gid批量签处理验证

##### PD-前置条件：同一用户注册了3个账号（oid1、oid2、oid3）；已用oid1完成实名认证（生成gid）；创建签署流程时个人签署方未传姓名；

##### 步骤一：使用oid1账号调用获取批量签链接接口，完成批量签署

##### 步骤二：使用oid2账号调用获取同一类型流程的批量签链接接口

##### 步骤三：访问批量签页面，检查印章选择情况

##### 步骤四：使用oid3账号重复相同操作

##### ER-预期结果：1：三个oid账号都能正常获取批量签链接；2：三个账号看到相同的个人印章（基于同一gid）；3：任一账号都可正常使用印章签署；4：签署记录正确关联到对应的oid；5：gid共享机制正常工作；

#### TL-签署流程状态变更时批量签处理验证

##### PD-前置条件：创建5个签署流程；个人签署方未传姓名；用户正在实名认证过程中；

##### 步骤一：调用获取批量签链接接口，访问批量签页面，开始实名认证

##### 步骤二：在实名认证过程中，其他用户取消了其中2个签署流程

##### 步骤三：完成实名认证，返回批量签页面

##### 步骤四：页面检测到流程状态变更，及时更新流程列表

##### 步骤五：对剩余有效的3个流程执行批量签署

##### ER-预期结果：1：实名认证流程不受签署流程状态变更影响；2：页面能及时检测并更新流程状态；3：已取消的流程从列表中移除；4：剩余有效流程可正常签署；5：整体流程处理正确；

## 接口详细测试

### 企业签署方接口测试

#### TL-POST接口organ-auth-identifyUrl全场景验证

##### PD-前置条件：准备不同状态的企业和签署流程；

##### 步骤一：企业未实名且仅使用企业章未传姓名时调用接口{flowId}/organ/auth/{orgId}/identifyUrl

##### 步骤二：企业已实名且仅使用企业章未传姓名时调用接口

##### 步骤三：企业使用个人章未传姓名时调用接口（应被拦截）

##### 步骤四：验证batchSerialId参数对各种场景的影响

##### 步骤五：验证接口返回字段的正确性

##### ER-预期结果：1：仅使用企业章未传姓名时接口调用成功；2：使用个人章未传姓名时被正确拦截；3：返回realNameFlowId、url、shortLink字段正确；4：batchSerialId正确影响操作人信息；5：接口响应格式符合文档规范；

#### TL-企业接口参数异常处理验证

##### PD-前置条件：准备各种异常参数；

##### 步骤一：使用不存在的flowId调用企业接口，检查错误响应

##### 步骤二：使用不存在的orgId调用接口，检查错误响应

##### 步骤三：使用无效的batchSerialId调用接口，检查处理结果

##### 步骤四：使用正确参数调用接口，验证正常流程

##### ER-预期结果：1：无效flowId返回明确的流程不存在错误；2：无效orgId返回明确的企业不存在错误；3：无效batchSerialId不影响基本功能；4：错误信息准确且用户友好；5：正常参数调用完全正常；

### 个人签署方接口测试

#### TL-POST接口individual-realNameWill-identifyUrl全场景验证

##### PD-前置条件：准备不同状态的用户和签署流程；

##### 步骤一：未实名用户（无gid）未传姓名时调用接口{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：实名中用户（有gid，状态为实名中）未传姓名时调用接口

##### 步骤三：已实名用户（有gid，状态为已实名）未传姓名时调用接口

##### 步骤四：gid异常用户未传姓名时调用接口，验证错误处理

##### 步骤五：验证batchSerialId参数对各种场景的影响

##### ER-预期结果：1：不同用户状态返回正确的type值（0实名/1意愿）；2：url指向正确的页面类型；3：所有必要字段正确返回；4：batchSerialId正确影响操作人信息；5：异常状态正确处理；

#### TL-个人接口参数边界和异常验证

##### PD-前置条件：准备各种边界和异常参数；

##### 步骤一：flowId为空、null、不存在等情况的处理验证

##### 步骤二：accountId为空、null、不存在等情况的处理验证

##### 步骤三：batchSerialId为空、null、无效等情况的处理验证

##### 步骤四：请求格式错误（非POST、缺少Content-Type等）的处理验证

##### ER-预期结果：1：所有异常参数都有明确的错误响应；2：错误信息准确且用户友好；3：不会出现系统异常或崩溃；4：接口响应时间在合理范围内；

##### 步骤二：在页面加载过程中模拟网络异常

##### 步骤三：页面显示加载异常提示

##### 步骤四：网络恢复后重新访问批量签链接

##### 步骤五：正常完成批量签署流程

##### ER-预期结果：1：页面加载异常时显示友好提示；2：提供重新加载的选项；3：网络恢复后可正常访问；4：批量签功能完全正常；

### 数据状态异常场景

#### TL-多oid对应同一gid批量签处理验证

##### PD-前置条件：同一用户注册了3个账号（oid1、oid2、oid3）；已用oid1完成实名认证（生成gid）；创建签署流程时个人签署方未传姓名；

##### 步骤一：使用oid1账号调用获取批量签链接接口，完成批量签署

##### 步骤二：使用oid2账号调用获取同一类型流程的批量签链接接口

##### 步骤三：访问批量签页面，检查印章选择情况

##### 步骤四：使用oid3账号重复相同操作

##### ER-预期结果：1：三个oid账号都能正常获取批量签链接；2：三个账号看到相同的个人印章（基于同一gid）；3：任一账号都可正常使用印章签署；4：签署记录正确关联到对应的oid；5：gid共享机制正常工作；

#### TL-签署流程状态变更时批量签处理验证

##### PD-前置条件：创建5个签署流程；个人签署方未传姓名；用户正在实名认证过程中；

##### 步骤一：调用获取批量签链接接口，访问批量签页面，开始实名认证

##### 步骤二：在实名认证过程中，其他用户取消了其中2个签署流程

##### 步骤三：完成实名认证，返回批量签页面

##### 步骤四：页面检测到流程状态变更，及时更新流程列表

##### 步骤五：对剩余有效的3个流程执行批量签署

##### ER-预期结果：1：实名认证流程不受签署流程状态变更影响；2：页面能及时检测并更新流程状态；3：已取消的流程从列表中移除；4：剩余有效流程可正常签署；5：整体流程处理正确；

## 数据状态和边界场景测试

### 用户数据状态复杂场景

#### TL-多oid对应同一gid批量签验证

##### PD-前置条件：同一用户注册了3个账号（oid1、oid2、oid3）；已用oid1完成实名认证（生成gid）；创建签署流程时个人签署方未传姓名；

##### 步骤一：使用oid1账号调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面直接展示个人印章选择（因为已实名），完成批量签署

##### 步骤三：使用oid2账号调用获取同一流程的批量签链接接口

##### 步骤四：访问批量签页面，检查是否需要实名认证

##### 步骤五：使用oid3账号重复相同操作

##### ER-预期结果：1：oid1账号可直接进行批量签署；2：oid2和oid3账号也能直接进入印章选择（共享gid的实名状态）；3：三个账号看到相同的个人印章；4：任一账号都可正常使用印章签署；5：签署记录正确关联到对应的oid；

#### TL-gid状态变更时批量签处理验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户已实名（有gid）；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：在用户准备签署时，gid状态被系统冻结

##### 步骤三：用户选择印章并提交批量签署

##### 步骤四：系统检测到gid状态异常

##### ER-预期结果：1：系统能实时检测gid状态变更；2：阻止异常状态下的签署操作；3：显示明确的状态异常提示；4：提供状态恢复的指引；

### 极限数据场景

#### TL-大量流程批量签处理验证

##### PD-前置条件：创建50个签署流程；所有流程个人签署方均未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，传入所有50个flowId

##### 步骤二：访问批量签页面，检查页面加载性能

##### 步骤三：完成个人实名认证

##### 步骤四：页面刷新后展示50个可签署流程

##### 步骤五：选择个人印章，批量签署所有流程

##### ER-预期结果：1：页面能正常加载大量流程；2：实名认证流程不受影响；3：批量签署功能正常执行；4：所有50个流程状态正确更新；5：系统性能表现良好；

#### TL-并发实名认证处理验证

##### PD-前置条件：同一用户在多个设备/浏览器上同时访问批量签页面；个人签署方未传姓名；用户未实名；

##### 步骤一：设备A访问批量签页面，开始实名认证

##### 步骤二：设备B同时访问批量签页面，也开始实名认证

##### 步骤三：两个设备同时提交实名信息

##### 步骤四：检查最终实名状态和gid生成情况

##### 步骤五：两个设备都尝试进行批量签署

##### ER-预期结果：1：系统正确处理并发实名请求；2：避免重复实名或gid冲突；3：最终实名状态一致；4：两个设备都能正常使用批量签功能；5：不会出现数据不一致问题；

## 兼容性和回归测试

### 原有功能完整兼容性验证

#### TL-传统个人签署流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；个人签署方已指定姓名和手机号；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示和功能

##### 步骤三：选择个人印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### 步骤五：检查签署结果和数据格式

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本完全一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：数据格式和字段完全兼容；6：新版本对原有功能零影响；

#### TL-传统企业签署流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；企业签署方已指定经办人姓名；企业已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示和功能

##### 步骤三：选择企业印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本完全一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：新版本对原有企业签署功能零影响；

#### TL-混合新旧功能场景完整验证

##### PD-前置条件：创建10个签署流程；5个按原有方式创建（已传姓名）；5个按新方式创建（未传姓名）；包含个人和企业签署；用户和企业均已实名；

##### 步骤一：调用获取批量签链接接口，传入所有10个flowId

##### 步骤二：访问批量签页面，检查所有流程的展示

##### 步骤三：验证新旧两种类型流程在页面上的一致性

##### 步骤四：分别选择个人印章和企业印章，批量签署所有10个流程

##### 步骤五：检查签署结果和状态更新

##### ER-预期结果：1：新旧两种类型流程在页面上展示完全一致；2：用户无法区分哪些是新功能哪些是旧功能；3：批量签署逻辑对两种类型处理完全相同；4：所有流程签署状态正确更新；5：签署数据格式完全一致；

## 冒烟测试用例

### 核心流程快速验证

#### MYTL-企业签署方仅使用企业章未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；企业签署方仅使用企业章未传姓名；企业未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成企业实名认证

##### 步骤三：选择生成的企业印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：企业实名认证正常；3：企业印章生成正确；4：签署执行成功；

#### MYTL-个人签署方未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：选择生成的个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：个人实名认证正常；3：个人印章生成正确；4：签署执行成功；

#### MYTL-混合签署主体未传姓名基本流程验证

##### PD-前置条件：创建1个签署流程；包含企业签署方（仅使用企业章，未传姓名）和个人签署方（未传姓名）；均未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，分别完成企业和个人实名认证

##### 步骤三：选择企业印章和个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：企业和个人实名认证都正常；3：印章生成正确；4：批量签署执行成功；

#### MYTL-企业使用个人章未传姓名校验拦截验证

##### PD-前置条件：创建1个签署流程；企业签署方使用个人章（经办人签）未传姓名；

##### 步骤一：调用获取批量签链接接口

##### ER-预期结果：1：接口调用失败，被校验拦截；2：返回明确的错误信息；

#### MYTL-新旧功能兼容性基本验证

##### PD-前置条件：创建2个签署流程；1个已传姓名，1个未传姓名；用户已实名；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：两种类型流程都能正常签署；2：功能完全兼容；

## 线上验证用例

### 关键业务流程线上验证

#### PATL-企业签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；企业签署方仅使用企业章未传姓名；真实企业未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的企业实名认证

##### 步骤三：使用生成的企业印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据完整性

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：企业实名认证数据正确保存；3：企业印章生成符合规范；4：签署结果准确有效；5：业务数据完整一致；

#### PATL-个人签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；个人签署方未传姓名；真实用户未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的个人实名认证

##### 步骤三：使用生成的个人印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据完整性

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：个人实名认证数据正确保存；3：个人印章生成符合规范；4：签署结果准确有效；5：业务数据完整一致；

#### PATL-复杂混合场景线上验证

##### PD-前置条件：线上环境；创建包含企业签署、个人签署、已传姓名、未传姓名等多种类型的10个真实签署流程；

##### 步骤一：执行完整的批量签流程，包含实名认证和签署

##### 步骤二：验证各种场景的处理正确性

##### 步骤三：检查业务数据和签署记录

##### ER-预期结果：1：复杂混合场景在线上环境正常处理；2：各种状态组合都能正确执行；3：业务逻辑完全正确；4：数据一致性良好；

#### PATL-原有功能兼容性线上验证

##### PD-前置条件：线上环境；原有传姓名的真实签署流程；

##### 步骤一：执行原有的批量签流程

##### 步骤二：对比新旧版本的功能表现

##### 步骤三：验证业务数据格式和内容

##### ER-预期结果：1：原有功能完全正常；2：新版本对原有功能零影响；3：用户体验保持一致；4：业务数据格式完全兼容；

##### 步骤三：两个设备同时提交实名信息

##### 步骤四：检查最终实名状态和gid生成情况

##### 步骤五：两个设备都尝试进行批量签署

##### ER-预期结果：1：系统正确处理并发实名请求；2：避免重复实名或gid冲突；3：最终实名状态一致；4：两个设备都能正常使用批量签功能；5：不会出现数据不一致问题；

## 时序和状态转换场景测试

### 实名认证过程中的状态变化

#### TL-实名认证过程中签署流程过期处理验证

##### PD-前置条件：创建1个签署流程，设置较短的过期时间；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：开始实名认证流程，故意延长认证时间

##### 步骤三：在实名认证过程中，签署流程到期过期

##### 步骤四：完成实名认证，返回批量签页面

##### 步骤五：检查页面状态和流程列表

##### ER-预期结果：1：实名认证正常完成；2：系统检测到流程已过期；3：页面显示流程过期提示；4：过期流程从可签署列表中移除；5：不影响其他有效流程的签署；

#### TL-实名认证完成后印章生成时序验证

##### PD-前置条件：创建1个签署流程；个人签署方未传姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：完成个人实名认证

##### 步骤三：立即检查页面状态（实名完成瞬间）

##### 步骤四：等待页面自动刷新

##### 步骤五：检查个人印章生成情况

##### ER-预期结果：1：实名完成后立即生成gid；2：个人印章自动生成；3：页面及时刷新展示印章选择；4：印章内容包含实名时的姓名；5：整个过程用户体验流畅；

### 多用户并发操作场景

#### TL-多用户同时对同一流程批量签验证

##### PD-前置条件：创建1个签署流程，包含多个签署方；个人签署方A和B均未传姓名；用户A和B均未实名；

##### 步骤一：用户A调用获取批量签链接接口，开始实名认证

##### 步骤二：用户B同时调用获取批量签链接接口，也开始实名认证

##### 步骤三：用户A先完成实名认证和签署

##### 步骤四：用户B完成实名认证后尝试签署

##### 步骤五：检查签署结果和流程状态

##### ER-预期结果：1：两个用户可以同时进行实名认证；2：用户A签署成功后，流程状态正确更新；3：用户B能正确看到流程的最新状态；4：不会出现重复签署或状态冲突；5：最终流程状态正确；

## 兼容性和回归测试

### 原有功能完整兼容性验证

#### TL-传统个人签署流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；个人签署方已指定姓名和手机号；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示和功能

##### 步骤三：选择个人印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### 步骤五：检查签署结果和数据格式

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本完全一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：数据格式和字段完全兼容；6：新版本对原有功能零影响；

#### TL-混合新旧功能场景完整验证

##### PD-前置条件：创建10个签署流程；5个按原有方式创建（已传姓名）；5个按新方式创建（未传姓名）；用户已实名；

##### 步骤一：调用获取批量签链接接口，传入所有10个flowId

##### 步骤二：访问批量签页面，检查所有流程的展示

##### 步骤三：验证新旧两种类型流程在页面上的一致性

##### 步骤四：选择个人印章，批量签署所有10个流程

##### 步骤五：检查签署结果和状态更新

##### ER-预期结果：1：新旧两种类型流程在页面上展示完全一致；2：用户无法区分哪些是新功能哪些是旧功能；3：批量签署逻辑对两种类型处理完全相同；4：所有流程签署状态正确更新；5：签署数据格式完全一致；

## 接口详细测试

### 核心接口全面验证

#### TL-individual-realNameWill-identifyUrl接口全场景验证

##### PD-前置条件：准备不同状态的用户和签署流程；

##### 步骤一：未实名用户（无gid）调用接口，验证返回type=0，url指向实名页面

##### 步骤二：实名中用户（有gid，状态为实名中）调用接口，验证返回type=0，url指向继续实名页面

##### 步骤三：已实名用户（有gid，状态为已实名）调用接口，验证返回type=1，url指向意愿确认页面

##### 步骤四：gid异常用户调用接口，验证错误处理

##### 步骤五：验证batchSerialId参数对各种场景的影响

##### ER-预期结果：1：不同用户状态返回正确的type值；2：url指向正确的页面类型；3：所有必要字段正确返回；4：batchSerialId正确影响操作人信息；5：异常状态正确处理；

#### TL-接口参数边界和异常全面验证

##### PD-前置条件：准备各种边界和异常参数；

##### 步骤一：flowId为空、null、不存在等情况的处理验证

##### 步骤二：accountId为空、null、不存在等情况的处理验证

##### 步骤三：batchSerialId为空、null、无效等情况的处理验证

##### 步骤四：请求格式错误（非POST、缺少Content-Type等）的处理验证

##### 步骤五：并发大量请求的处理验证

##### ER-预期结果：1：所有异常参数都有明确的错误响应；2：错误信息准确且用户友好；3：不会出现系统异常或崩溃；4：并发请求处理正常；5：接口响应时间在合理范围内；

## 冒烟测试用例

### 核心流程快速验证

#### MYTL-个人未实名未传姓名单流程批量签基本验证

##### PD-前置条件：创建1个签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：选择生成的个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：实名认证正常；3：印章生成正确；4：签署执行成功；

#### MYTL-个人已实名未传姓名单流程批量签基本验证

##### PD-前置条件：创建1个签署流程；个人签署方未指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，直接选择印章签署

##### ER-预期结果：1：跳过实名认证环节；2：直接进入印章选择；3：批量签署正常执行；

#### MYTL-多流程批量签基本验证

##### PD-前置条件：创建3个签署流程；个人签署方均未传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，选择印章批量签署所有流程

##### ER-预期结果：1：页面正确展示3个流程；2：可以一次性签署所有流程；3：所有流程状态正确更新；

#### MYTL-实名认证失败重试基本验证

##### PD-前置条件：个人签署方未传姓名；用户未实名；

##### 步骤一：访问批量签页面，故意实名认证失败

##### 步骤二：重新进行实名认证成功

##### 步骤三：完成批量签署

##### ER-预期结果：1：失败后提供重试机制；2：重试流程正常；3：最终签署成功；

#### MYTL-新旧功能兼容性基本验证

##### PD-前置条件：创建2个签署流程；1个已传姓名，1个未传姓名；用户已实名；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：两种类型流程都能正常签署；2：功能完全兼容；

## 线上验证用例

### 关键业务流程线上验证

#### PATL-个人未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；个人签署方未指定姓名；真实用户未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的个人实名认证

##### 步骤三：使用生成的印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据完整性

##### 步骤五：检查实名认证数据在系统中的存储

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：实名认证数据正确保存到数据库；3：印章生成符合业务规范；4：签署结果准确有效；5：业务数据完整一致；6：符合法律法规要求；

#### PATL-多流程复杂场景线上验证

##### PD-前置条件：线上环境；创建包含个人签署、企业签署、已传姓名、未传姓名等多种类型的10个真实签署流程；

##### 步骤一：执行完整的批量签流程，包含实名认证和签署

##### 步骤二：验证各种场景的处理正确性

##### 步骤三：检查业务数据和签署记录

##### ER-预期结果：1：复杂混合场景在线上环境正常处理；2：各种状态组合都能正确执行；3：业务逻辑完全正确；4：数据一致性良好；

#### PATL-原有功能兼容性线上验证

##### PD-前置条件：线上环境；原有传姓名的真实签署流程；

##### 步骤一：执行原有的批量签流程

##### 步骤二：对比新旧版本的功能表现

##### 步骤三：验证业务数据格式和内容

##### ER-预期结果：1：原有功能完全正常；2：新版本对原有功能零影响；3：用户体验保持一致；4：业务数据格式完全兼容；

## 接口测试

### 获取个人批量签链接接口

#### TL-POST接口flowId-individual-realNameWill-accountId-identifyUrl未传姓名场景验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；用户有oid；

##### 步骤一：构造POST请求，URL为{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：设置请求头Content-Type为application/json

##### 步骤三：传入batchSerialId参数（可选）

##### 步骤四：发送请求并获取响应

##### ER-预期结果：1：HTTP状态码为200；2：响应包含type字段（0表示实名）；3：响应包含bizId、realNameFlowId字段；4：响应包含url和shortUrl字段；5：所有字段值非空且格式正确；

#### TL-不同实名状态用户接口返回type字段验证

##### PD-前置条件：准备不同实名状态的用户（未实名、实名中、已实名）；

##### 步骤一：分别使用不同状态用户调用接口

##### 步骤二：检查返回的type字段值

##### 步骤三：验证type字段与实名状态的对应关系

##### ER-预期结果：1：未实名用户返回type=0（实名）；2：实名中用户返回type=0（实名）；3：已实名用户返回type=1（意愿）；4：type字段准确反映用户状态；

#### TL-batchSerialId参数对接口行为影响验证

##### PD-前置条件：个人签署方未传姓名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：传入无效batchSerialId调用接口

##### 步骤四：对比三种情况的接口响应

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId功能正常；3：传入无效batchSerialId返回相应错误；4：batchSerialId用于获取签署操作人信息；

### 接口异常处理

#### TL-accountId不存在时接口异常处理验证

##### PD-前置条件：准备不存在的accountId；

##### 步骤一：使用不存在的accountId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回明确的错误信息；3：错误信息指出accountId不存在；

#### TL-flowId无效时接口异常处理验证

##### PD-前置条件：准备无效的flowId；

##### 步骤一：使用无效flowId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回签署流程不存在的错误信息；

## 兼容性测试

### 原有功能兼容性

#### TL-个人签署方已传姓名流程兼容性验证

##### PD-前置条件：已创建签署流程；个人签署方已指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：执行完整的批量签署流程

##### ER-预期结果：1：接口调用成功；2：批量签页面正常展示；3：签署流程正常执行；4：功能与原有流程完全一致；

#### TL-混合场景兼容性验证

##### PD-前置条件：批量签包含多种情况：个人已传姓名、个人未传姓名、企业签署等；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：分别处理不同类型的签署方

##### 步骤四：执行批量签署

##### ER-预期结果：1：接口正常返回；2：页面正确展示各类型签署方；3：未传姓名的个人签署方可正常实名；4：已传姓名的签署方功能不受影响；5：批量签署正常执行；

## 性能测试

### 实名认证性能

#### TL-大量用户同时实名认证性能验证

##### PD-前置条件：准备100个未实名用户；所有用户个人签署方均未传姓名；

##### 步骤一：100个用户同时获取批量签链接

##### 步骤二：同时访问批量签页面

##### 步骤三：同时进行实名认证

##### 步骤四：记录性能指标

##### ER-预期结果：1：接口响应时间小于3秒；2：页面加载时间小于5秒；3：实名认证成功率达到99%以上；4：系统资源使用正常；

#### TL-批量签页面加载性能验证

##### PD-前置条件：用户有大量未传姓名的签署流程；用户已实名；

##### 步骤一：访问包含大量流程的批量签页面

##### 步骤二：记录页面加载时间

##### 步骤三：测试页面交互响应时间

##### ER-预期结果：1：页面初始加载时间小于5秒；2：印章选择响应时间小于1秒；3：批量签署提交响应时间小于3秒；4：页面操作流畅无卡顿；

## 安全测试

### 实名认证安全

#### TL-实名认证数据传输安全验证

##### PD-前置条件：个人签署方未传姓名；用户进行实名认证；

##### 步骤一：监控实名认证过程中的数据传输

##### 步骤二：检查敏感信息加密情况

##### 步骤三：验证数据传输协议

##### ER-预期结果：1：身份证号等敏感信息加密传输；2：使用HTTPS协议；3：实名认证token具有时效性；4：不在URL中暴露敏感信息；

#### TL-跨用户实名信息隔离验证

##### PD-前置条件：多个用户同时进行实名认证；

##### 步骤一：用户A开始实名认证

##### 步骤二：用户B同时开始实名认证

##### 步骤三：检查两用户的实名信息是否隔离

##### ER-预期结果：1：用户A无法看到用户B的实名信息；2：实名认证流程完全隔离；3：gid生成正确对应各自用户；4：印章生成基于正确的实名信息；

### 权限安全

#### TL-未授权用户访问批量签链接安全验证

##### PD-前置条件：个人签署方未传姓名的签署流程；无权限用户；

##### 步骤一：无权限用户尝试获取批量签链接

##### 步骤二：尝试直接访问批量签页面URL

##### ER-预期结果：1：接口返回401或403错误；2：页面访问被拦截；3：返回权限不足提示；4：记录安全审计日志；

## 冒烟测试用例

### 核心功能验证

#### MYTL-个人签署方未传姓名批量签基本流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证

##### 步骤四：选择个人印章并提交签署

##### ER-预期结果：1：成功获取批量签链接；2：正常进入批量签页面；3：实名认证流程完整；4：签署执行成功；

#### MYTL-个人已实名未传姓名批量签验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：选择个人印章并签署

##### ER-预期结果：1：接口调用成功；2：直接进入印章选择页面；3：批量签署正常执行；

#### MYTL-实名认证失败重试基本验证

##### PD-前置条件：个人签署方未传姓名；用户未实名；

##### 步骤一：访问批量签页面

##### 步骤二：实名认证失败

##### 步骤三：重新进行实名认证成功

##### ER-预期结果：1：失败后提供重试入口；2：重试流程正常；3：最终实名成功可正常签署；

#### MYTL-POST接口基本调用验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；

##### 步骤一：调用个人批量签链接接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：HTTP状态码为200；2：返回必要字段；3：字段值格式正确；

#### MYTL-原有传姓名流程兼容性验证

##### PD-前置条件：个人签署方已指定姓名的签署流程；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：功能完全正常；2：与原有流程一致；

## 线上验证用例

### 核心业务流程验证

#### PATL-个人签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；已创建真实签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用线上获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证流程

##### 步骤四：选择个人印章并提交批量签署

##### 步骤五：验证签署结果和流程状态

##### ER-预期结果：1：接口调用成功返回有效链接；2：批量签页面正常展示；3：个人实名认证流程完整可用；4：印章选择功能正常；5：签署执行成功且状态正确更新；

#### PATL-多oid对应同一gid场景线上验证

##### PD-前置条件：线上环境；同一用户的多个oid账号；已完成实名认证；

##### 步骤一：使用不同oid账号获取批量签链接

##### 步骤二：验证印章共享机制

##### 步骤三：执行批量签署

##### ER-预期结果：1：不同oid账号都能正常获取链接；2：印章基于gid正确共享；3：签署功能完全正常；

#### PATL-实名认证服务异常恢复线上验证

##### PD-前置条件：线上环境；个人签署方未传姓名；

##### 步骤一：在实名服务异常时访问批量签页面

##### 步骤二：等待服务恢复

##### 步骤三：完成实名认证和签署

##### ER-预期结果：1：异常时显示友好提示；2：服务恢复后功能自动可用；3：整体流程完整可用；

#### PATL-接口兼容性线上验证

##### PD-前置条件：线上环境；包含已传姓名和未传姓名的混合场景；

##### 步骤一：执行完整的批量签流程

##### 步骤二：验证各种场景的兼容性

##### ER-预期结果：1：新功能不影响原有流程；2：混合场景处理正确；3：接口响应格式一致；4：业务逻辑兼容性良好；
