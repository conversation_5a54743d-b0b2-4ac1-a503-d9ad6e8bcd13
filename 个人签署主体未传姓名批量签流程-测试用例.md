# 个人签署主体未传姓名批量签流程-测试用例

## 端到端完整流程测试

### 主流程场景

#### TL-个人未实名未传姓名完整批量签流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户有oid但未实名（无gid）；

##### 步骤一：调用获取批量签链接接口，传入flowId和accountId

##### 步骤二：访问返回的批量签链接，进入批量签页面

##### 步骤三：点击个人实名认证入口，填写真实姓名和身份证号

##### 步骤四：完成人脸识别等实名认证步骤

##### 步骤五：实名成功后自动返回批量签页面，页面刷新展示个人印章选择

##### 步骤六：选择生成的个人印章，确认批量签署流程列表

##### 步骤七：提交批量签署请求，完成签署

##### ER-预期结果：1：接口成功返回批量签链接；2：页面正常展示实名认证入口；3：实名认证流程完整可用；4：实名成功后自动生成个人印章并刷新页面；5：印章选择功能正常；6：批量签署执行成功；7：签署流程状态正确更新为已签署；

#### TL-个人已实名未传姓名完整批量签流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已完成实名认证（有gid）；

##### 步骤一：调用获取批量签链接接口，传入flowId和accountId

##### 步骤二：访问返回的批量签链接，直接进入批量签页面

##### 步骤三：页面直接展示个人印章选择区域

##### 步骤四：选择个人印章，确认批量签署流程列表

##### 步骤五：提交批量签署请求，完成签署

##### ER-预期结果：1：接口成功返回批量签链接；2：直接进入批量签页面，跳过实名认证；3：页面正常展示个人印章选择；4：印章选择和批量签署功能正常；5：签署流程状态正确更新；

#### TL-个人实名中状态未传姓名完整批量签流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户有gid但实名状态为"实名中"；

##### 步骤一：调用获取批量签链接接口，传入flowId和accountId

##### 步骤二：访问返回的批量签链接，进入批量签页面

##### 步骤三：页面展示继续实名认证的入口

##### 步骤四：继续完成剩余的实名认证步骤

##### 步骤五：实名成功后页面刷新，展示个人印章选择

##### 步骤六：选择个人印章，提交批量签署

##### ER-预期结果：1：接口成功返回批量签链接；2：页面正确识别实名中状态；3：可继续完成实名认证流程；4：实名完成后功能正常；5：批量签署执行成功；

### 分支流程场景

#### TL-实名认证失败重试完整流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：点击实名认证入口，故意输入错误信息导致实名失败

##### 步骤三：页面显示实名失败原因，点击重新实名入口

##### 步骤四：使用正确信息重新完成实名认证

##### 步骤五：实名成功后返回批量签页面，选择印章完成签署

##### ER-预期结果：1：实名失败后页面显示明确的失败原因；2：提供重新实名的入口；3：重试流程完整可用；4：最终实名成功后批量签功能正常；5：整个流程可以完整执行；

#### TL-部分流程签署完整场景验证

##### PD-前置条件：批量签包含5个流程；其中3个流程个人签署方未传姓名；2个流程已传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面展示5个可批量签署的流程，检查流程列表

##### 步骤三：选择个人印章，但只勾选其中3个流程进行签署

##### 步骤四：提交批量签署请求

##### 步骤五：检查签署结果，验证已签署和未签署流程的状态

##### ER-预期结果：1：页面正确展示所有可签署流程；2：未传姓名和已传姓名的流程都正常显示；3：可选择性签署部分流程；4：已签署流程状态更新为已签署；5：未签署流程状态保持不变；

#### TL-混合签署主体完整流程验证

##### PD-前置条件：批量签包含个人签署、企业签署等多种主体；个人签署方未传姓名；企业未实名；用户个人未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面同时展示个人实名认证和企业实名认证入口

##### 步骤三：先完成个人实名认证，页面刷新展示个人印章选择

##### 步骤四：再完成企业实名认证，页面刷新展示企业印章选择

##### 步骤五：分别选择个人印章和企业印章，提交批量签署

##### ER-预期结果：1：页面正确展示多种主体的实名认证入口；2：个人实名完成后个人印章选择正常；3：企业实名完成后企业印章选择正常；4：可同时选择多种主体的印章；5：批量签署执行成功；

### 异常处理场景

#### TL-实名认证过程中网络异常恢复完整流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：开始个人实名认证流程，在认证过程中模拟网络中断

##### 步骤三：页面显示网络异常提示，提供重试选项

##### 步骤四：网络恢复后点击重试，继续完成实名认证

##### 步骤五：实名成功后返回批量签页面，完成签署流程

##### ER-预期结果：1：网络异常时显示友好的错误提示；2：提供明确的重试机制；3：网络恢复后可继续认证流程；4：不会出现数据不一致问题；5：最终可完整执行批量签署；

#### TL-签署流程状态变更时完整流程处理验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户正在实名认证过程中；

##### 步骤一：调用获取批量签链接接口，访问批量签页面，开始实名认证

##### 步骤二：在实名认证过程中，其他用户取消了相关签署流程

##### 步骤三：完成实名认证，返回批量签页面

##### 步骤四：页面检测到流程状态变更，及时更新流程列表

##### 步骤五：对剩余有效流程执行批量签署

##### ER-预期结果：1：实名认证流程不受签署流程状态变更影响；2：页面能及时检测并更新流程状态；3：已取消的流程从列表中移除；4：剩余有效流程可正常签署；5：整体流程处理正确；

### 数据状态组合场景

#### TL-多oid对应同一gid完整批量签流程验证

##### PD-前置条件：同一用户注册了多个账号（oid1、oid2）；已用oid1完成实名认证（生成gid）；创建签署流程时个人签署方未传姓名；

##### 步骤一：使用oid1账号调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面直接展示个人印章选择（因为已实名），完成批量签署

##### 步骤三：使用oid2账号调用获取同一流程的批量签链接接口

##### 步骤四：访问批量签页面，检查印章选择情况

##### 步骤五：使用oid2账号选择印章完成签署

##### ER-预期结果：1：oid1账号可直接进行批量签署；2：oid2账号也能正常获取批量签链接；3：两个账号看到相同的个人印章（基于同一gid）；4：任一账号都可正常使用印章签署；5：签署记录正确关联到对应的oid；

#### TL-用户gid异常状态完整流程处理验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；用户有gid但状态异常（被冻结）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：页面检测到gid状态异常，显示相应提示

##### 步骤四：按照提示进行gid状态恢复操作

##### 步骤五：状态恢复后重新访问批量签页面，完成签署

##### ER-预期结果：1：系统能正确检测gid状态异常；2：显示明确的异常原因和解决方案；3：不允许异常状态下进行签署；4：状态恢复后功能立即可用；5：整个处理流程用户体验良好；

## 兼容性测试

### 原有功能兼容性

#### TL-个人签署方已传姓名与未传姓名混合场景完整流程验证

##### PD-前置条件：批量签包含多个流程；部分流程个人签署方已传姓名；部分流程未传姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口，访问批量签页面

##### 步骤二：页面展示所有可批量签署的流程列表

##### 步骤三：检查已传姓名和未传姓名流程的展示是否一致

##### 步骤四：选择个人印章，对所有流程执行批量签署

##### 步骤五：验证签署结果和流程状态更新

##### ER-预期结果：1：已传姓名和未传姓名的流程在页面上展示一致；2：两种类型流程都能正常进行批量签署；3：签署逻辑完全相同；4：签署结果和状态更新正确；5：新功能不影响原有流程；

#### TL-原有传姓名流程完整兼容性验证

##### PD-前置条件：完全按照原有方式创建签署流程；个人签署方已指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，检查页面展示

##### 步骤三：选择个人印章，执行批量签署

##### 步骤四：对比与历史版本的功能表现

##### ER-预期结果：1：接口调用完全正常；2：页面展示与历史版本一致；3：批量签署功能完全正常；4：签署结果与历史版本一致；5：新版本对原有功能零影响；

## 接口测试

### 核心接口验证

#### TL-POST接口individual-realNameWill-identifyUrl完整调用验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；准备不同状态的用户账号；

##### 步骤一：使用未实名用户调用接口{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：检查返回的type字段为0（实名类型），访问返回的url完成实名认证

##### 步骤三：使用已实名用户调用相同接口

##### 步骤四：检查返回的type字段为1（意愿类型），访问返回的url直接进行签署

##### 步骤五：验证batchSerialId参数的影响

##### ER-预期结果：1：未实名用户返回type=0，url指向实名认证页面；2：已实名用户返回type=1，url指向意愿确认页面；3：batchSerialId参数正确影响签署操作人信息获取；4：所有必要字段（bizId、realNameFlowId、url、shortUrl）正常返回；5：接口响应格式符合文档规范；

### 接口异常处理

#### TL-接口参数异常完整处理验证

##### PD-前置条件：准备各种异常参数；

##### 步骤一：使用不存在的flowId调用接口，检查错误响应

##### 步骤二：使用不存在的accountId调用接口，检查错误响应

##### 步骤三：使用无效的batchSerialId调用接口，检查处理结果

##### 步骤四：使用正确参数调用接口，验证正常流程

##### ER-预期结果：1：无效flowId返回明确的流程不存在错误；2：无效accountId返回明确的用户不存在错误；3：无效batchSerialId不影响基本功能；4：错误信息准确且用户友好；5：正常参数调用完全正常；

## 冒烟测试用例

### 核心流程验证

#### MYTL-个人未实名未传姓名完整批量签基本流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，完成个人实名认证

##### 步骤三：选择生成的个人印章，提交批量签署

##### ER-预期结果：1：完整流程执行成功；2：实名认证正常；3：印章生成正确；4：签署执行成功；

#### MYTL-个人已实名未传姓名批量签基本流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面，直接选择印章签署

##### ER-预期结果：1：跳过实名认证环节；2：直接进入印章选择；3：批量签署正常执行；

#### MYTL-实名认证失败重试基本流程验证

##### PD-前置条件：个人签署方未传姓名；用户未实名；

##### 步骤一：访问批量签页面，实名认证失败

##### 步骤二：重新进行实名认证成功

##### 步骤三：完成批量签署

##### ER-预期结果：1：失败后提供重试机制；2：重试流程正常；3：最终签署成功；

#### MYTL-混合场景兼容性基本验证

##### PD-前置条件：批量签包含已传姓名和未传姓名的流程；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：两种类型流程都能正常签署；2：功能完全兼容；

## 线上验证用例

### 关键业务流程验证

#### PATL-个人未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；真实签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用线上接口获取批量签链接

##### 步骤二：访问批量签页面，完成真实的个人实名认证

##### 步骤三：使用生成的印章完成真实的批量签署

##### 步骤四：验证签署结果和业务数据

##### ER-预期结果：1：完整业务流程在线上环境正常执行；2：实名认证数据正确保存；3：印章生成符合规范；4：签署结果准确有效；5：业务数据完整一致；

#### PATL-原有功能兼容性线上验证

##### PD-前置条件：线上环境；原有传姓名的签署流程；

##### 步骤一：执行原有的批量签流程

##### 步骤二：对比新旧版本的功能表现

##### ER-预期结果：1：原有功能完全正常；2：新版本对原有功能零影响；3：用户体验保持一致；

#### PATL-混合场景线上完整验证

##### PD-前置条件：线上环境；包含多种签署主体和状态的复杂场景；

##### 步骤一：执行包含个人签署、企业签署、已传姓名、未传姓名等混合场景的批量签流程

##### 步骤二：验证各种场景的处理正确性

##### ER-预期结果：1：复杂混合场景在线上环境正常处理；2：各种状态组合都能正确执行；3：业务逻辑完全正确；

## 接口测试

### 获取个人批量签链接接口

#### TL-POST接口flowId-individual-realNameWill-accountId-identifyUrl未传姓名场景验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；用户有oid；

##### 步骤一：构造POST请求，URL为{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：设置请求头Content-Type为application/json

##### 步骤三：传入batchSerialId参数（可选）

##### 步骤四：发送请求并获取响应

##### ER-预期结果：1：HTTP状态码为200；2：响应包含type字段（0表示实名）；3：响应包含bizId、realNameFlowId字段；4：响应包含url和shortUrl字段；5：所有字段值非空且格式正确；

#### TL-不同实名状态用户接口返回type字段验证

##### PD-前置条件：准备不同实名状态的用户（未实名、实名中、已实名）；

##### 步骤一：分别使用不同状态用户调用接口

##### 步骤二：检查返回的type字段值

##### 步骤三：验证type字段与实名状态的对应关系

##### ER-预期结果：1：未实名用户返回type=0（实名）；2：实名中用户返回type=0（实名）；3：已实名用户返回type=1（意愿）；4：type字段准确反映用户状态；

#### TL-batchSerialId参数对接口行为影响验证

##### PD-前置条件：个人签署方未传姓名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：传入无效batchSerialId调用接口

##### 步骤四：对比三种情况的接口响应

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId功能正常；3：传入无效batchSerialId返回相应错误；4：batchSerialId用于获取签署操作人信息；

### 接口异常处理

#### TL-accountId不存在时接口异常处理验证

##### PD-前置条件：准备不存在的accountId；

##### 步骤一：使用不存在的accountId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回明确的错误信息；3：错误信息指出accountId不存在；

#### TL-flowId无效时接口异常处理验证

##### PD-前置条件：准备无效的flowId；

##### 步骤一：使用无效flowId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回签署流程不存在的错误信息；

## 兼容性测试

### 原有功能兼容性

#### TL-个人签署方已传姓名流程兼容性验证

##### PD-前置条件：已创建签署流程；个人签署方已指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：执行完整的批量签署流程

##### ER-预期结果：1：接口调用成功；2：批量签页面正常展示；3：签署流程正常执行；4：功能与原有流程完全一致；

#### TL-混合场景兼容性验证

##### PD-前置条件：批量签包含多种情况：个人已传姓名、个人未传姓名、企业签署等；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：分别处理不同类型的签署方

##### 步骤四：执行批量签署

##### ER-预期结果：1：接口正常返回；2：页面正确展示各类型签署方；3：未传姓名的个人签署方可正常实名；4：已传姓名的签署方功能不受影响；5：批量签署正常执行；

## 性能测试

### 实名认证性能

#### TL-大量用户同时实名认证性能验证

##### PD-前置条件：准备100个未实名用户；所有用户个人签署方均未传姓名；

##### 步骤一：100个用户同时获取批量签链接

##### 步骤二：同时访问批量签页面

##### 步骤三：同时进行实名认证

##### 步骤四：记录性能指标

##### ER-预期结果：1：接口响应时间小于3秒；2：页面加载时间小于5秒；3：实名认证成功率达到99%以上；4：系统资源使用正常；

#### TL-批量签页面加载性能验证

##### PD-前置条件：用户有大量未传姓名的签署流程；用户已实名；

##### 步骤一：访问包含大量流程的批量签页面

##### 步骤二：记录页面加载时间

##### 步骤三：测试页面交互响应时间

##### ER-预期结果：1：页面初始加载时间小于5秒；2：印章选择响应时间小于1秒；3：批量签署提交响应时间小于3秒；4：页面操作流畅无卡顿；

## 安全测试

### 实名认证安全

#### TL-实名认证数据传输安全验证

##### PD-前置条件：个人签署方未传姓名；用户进行实名认证；

##### 步骤一：监控实名认证过程中的数据传输

##### 步骤二：检查敏感信息加密情况

##### 步骤三：验证数据传输协议

##### ER-预期结果：1：身份证号等敏感信息加密传输；2：使用HTTPS协议；3：实名认证token具有时效性；4：不在URL中暴露敏感信息；

#### TL-跨用户实名信息隔离验证

##### PD-前置条件：多个用户同时进行实名认证；

##### 步骤一：用户A开始实名认证

##### 步骤二：用户B同时开始实名认证

##### 步骤三：检查两用户的实名信息是否隔离

##### ER-预期结果：1：用户A无法看到用户B的实名信息；2：实名认证流程完全隔离；3：gid生成正确对应各自用户；4：印章生成基于正确的实名信息；

### 权限安全

#### TL-未授权用户访问批量签链接安全验证

##### PD-前置条件：个人签署方未传姓名的签署流程；无权限用户；

##### 步骤一：无权限用户尝试获取批量签链接

##### 步骤二：尝试直接访问批量签页面URL

##### ER-预期结果：1：接口返回401或403错误；2：页面访问被拦截；3：返回权限不足提示；4：记录安全审计日志；

## 冒烟测试用例

### 核心功能验证

#### MYTL-个人签署方未传姓名批量签基本流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证

##### 步骤四：选择个人印章并提交签署

##### ER-预期结果：1：成功获取批量签链接；2：正常进入批量签页面；3：实名认证流程完整；4：签署执行成功；

#### MYTL-个人已实名未传姓名批量签验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：选择个人印章并签署

##### ER-预期结果：1：接口调用成功；2：直接进入印章选择页面；3：批量签署正常执行；

#### MYTL-实名认证失败重试基本验证

##### PD-前置条件：个人签署方未传姓名；用户未实名；

##### 步骤一：访问批量签页面

##### 步骤二：实名认证失败

##### 步骤三：重新进行实名认证成功

##### ER-预期结果：1：失败后提供重试入口；2：重试流程正常；3：最终实名成功可正常签署；

#### MYTL-POST接口基本调用验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；

##### 步骤一：调用个人批量签链接接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：HTTP状态码为200；2：返回必要字段；3：字段值格式正确；

#### MYTL-原有传姓名流程兼容性验证

##### PD-前置条件：个人签署方已指定姓名的签署流程；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：功能完全正常；2：与原有流程一致；

## 线上验证用例

### 核心业务流程验证

#### PATL-个人签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；已创建真实签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用线上获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证流程

##### 步骤四：选择个人印章并提交批量签署

##### 步骤五：验证签署结果和流程状态

##### ER-预期结果：1：接口调用成功返回有效链接；2：批量签页面正常展示；3：个人实名认证流程完整可用；4：印章选择功能正常；5：签署执行成功且状态正确更新；

#### PATL-多oid对应同一gid场景线上验证

##### PD-前置条件：线上环境；同一用户的多个oid账号；已完成实名认证；

##### 步骤一：使用不同oid账号获取批量签链接

##### 步骤二：验证印章共享机制

##### 步骤三：执行批量签署

##### ER-预期结果：1：不同oid账号都能正常获取链接；2：印章基于gid正确共享；3：签署功能完全正常；

#### PATL-实名认证服务异常恢复线上验证

##### PD-前置条件：线上环境；个人签署方未传姓名；

##### 步骤一：在实名服务异常时访问批量签页面

##### 步骤二：等待服务恢复

##### 步骤三：完成实名认证和签署

##### ER-预期结果：1：异常时显示友好提示；2：服务恢复后功能自动可用；3：整体流程完整可用；

#### PATL-接口兼容性线上验证

##### PD-前置条件：线上环境；包含已传姓名和未传姓名的混合场景；

##### 步骤一：执行完整的批量签流程

##### 步骤二：验证各种场景的兼容性

##### ER-预期结果：1：新功能不影响原有流程；2：混合场景处理正确；3：接口响应格式一致；4：业务逻辑兼容性良好；
