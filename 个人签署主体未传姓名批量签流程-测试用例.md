# 个人签署主体未传姓名批量签流程-测试用例

## 功能测试

### 批量签链接获取

#### TL-个人签署方未传姓名获取批量签链接成功验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；个人签署方有oid但无gid；

##### 步骤一：调用获取批量签链接接口，传入flowId和accountId

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### ER-预期结果：1：接口调用成功，返回200状态码；2：返回type、bizId、realNameFlowId、url、shortUrl字段；3：成功进入批量签页面；4：页面展示个人实名认证入口；

#### TL-个人签署方有oid无gid状态获取批量签链接验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户有oid但从未实名无gid；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回的type字段

##### 步骤三：访问返回的链接

##### ER-预期结果：1：接口调用成功；2：type字段返回0（实名类型）；3：进入实名认证页面；4：页面提示需要完成实名认证；

#### TL-个人签署方有gid未实名状态获取批量签链接验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户有gid但实名状态为"实名中"；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的链接

##### ER-预期结果：1：接口调用成功；2：返回实名流程相关信息；3：进入实名认证页面；4：可继续完成实名流程；

#### TL-个人签署方已实名但未传姓名获取批量签链接验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已完成实名认证；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：检查接口返回结果

##### 步骤三：访问返回的批量签链接

##### ER-预期结果：1：接口调用成功；2：直接进入批量签页面；3：页面展示个人印章选择；4：可正常进行批量签署；

### 实名认证流程

#### TL-个人未实名用户批量签页面实名认证完整流程

##### PD-前置条件：个人签署方未传姓名；用户未完成实名认证；已获取批量签链接；

##### 步骤一：访问批量签页面链接

##### 步骤二：点击个人实名认证入口

##### 步骤三：填写真实姓名和身份证号

##### 步骤四：完成人脸识别或其他实名认证步骤

##### 步骤五：实名认证成功后返回批量签页面

##### ER-预期结果：1：页面正常展示实名认证入口；2：实名认证流程完整可用；3：实名成功后自动生成gid；4：页面自动刷新展示个人印章选择；5：个人印章正常生成并可选择；

#### TL-实名认证过程中页面状态同步验证

##### PD-前置条件：个人签署方未传姓名；用户正在进行实名认证；

##### 步骤一：在实名认证过程中刷新批量签页面

##### 步骤二：检查页面状态显示

##### 步骤三：完成实名认证

##### 步骤四：再次检查页面状态

##### ER-预期结果：1：实名进行中页面显示正确状态；2：实名完成后页面状态及时更新；3：个人印章选择区域正常展示；4：不出现页面状态不一致问题；

#### TL-实名认证失败后重试流程验证

##### PD-前置条件：个人签署方未传姓名；用户实名认证失败；

##### 步骤一：访问批量签页面

##### 步骤二：点击实名认证入口

##### 步骤三：故意输入错误信息导致实名失败

##### 步骤四：重新进行实名认证

##### 步骤五：使用正确信息完成实名

##### ER-预期结果：1：实名失败后页面显示失败原因；2：提供重新实名的入口；3：重试流程正常可用；4：最终实名成功后功能正常；

### 印章生成和选择

#### TL-实名成功后个人印章自动生成验证

##### PD-前置条件：个人签署方未传姓名；用户刚完成实名认证；

##### 步骤一：完成实名认证流程

##### 步骤二：返回批量签页面

##### 步骤三：检查个人印章选择区域

##### 步骤四：查看生成的默认印章

##### ER-预期结果：1：实名成功后自动生成个人默认印章；2：印章内容包含实名时填写的姓名；3：印章样式符合系统规范；4：印章可正常选择使用；

#### TL-多个oid对应同一gid场景印章共享验证

##### PD-前置条件：同一用户注册了多个账号（多个oid）；已完成实名认证（同一gid）；

##### 步骤一：使用第一个oid账号创建签署流程并获取批量签链接

##### 步骤二：使用第二个oid账号获取同一流程的批量签链接

##### 步骤三：分别访问两个批量签页面

##### 步骤四：检查印章选择情况

##### ER-预期结果：1：两个oid账号都能正常获取批量签链接；2：两个账号看到的个人印章相同；3：印章基于gid生成，不受oid影响；4：任一账号都可正常使用印章签署；

### 批量签署执行

#### TL-个人实名完成后批量签署执行验证

##### PD-前置条件：个人签署方未传姓名；用户已完成实名认证；已进入批量签页面；

##### 步骤一：在批量签页面选择个人印章

##### 步骤二：确认批量签署的流程列表

##### 步骤三：提交批量签署请求

##### 步骤四：检查签署执行结果

##### ER-预期结果：1：个人印章选择功能正常；2：批量签署流程列表正确展示；3：签署提交成功；4：所有相关签署流程状态更新为已签署；5：签署时间和签署人信息正确记录；

#### TL-部分流程个人未传姓名批量签署验证

##### PD-前置条件：批量签包含多个流程；部分流程个人签署方未传姓名；部分流程已传姓名；用户已实名；

##### 步骤一：访问批量签页面

##### 步骤二：检查可批量签署的流程列表

##### 步骤三：选择个人印章

##### 步骤四：提交批量签署

##### ER-预期结果：1：未传姓名的流程正常显示在可签署列表中；2：已传姓名的流程也正常显示；3：可统一选择印章进行批量签署；4：所有流程签署状态正确更新；

## 异常测试

### 实名认证异常

#### TL-实名认证过程中网络异常处理验证

##### PD-前置条件：个人签署方未传姓名；用户正在进行实名认证；

##### 步骤一：开始实名认证流程

##### 步骤二：在认证过程中模拟网络中断

##### 步骤三：网络恢复后检查认证状态

##### 步骤四：重新完成实名认证

##### ER-预期结果：1：网络异常时显示友好错误提示；2：提供重试机制；3：网络恢复后可继续认证流程；4：不会出现数据不一致问题；

#### TL-实名认证服务异常时批量签流程处理

##### PD-前置条件：个人签署方未传姓名；实名认证服务不可用；

##### 步骤一：访问批量签页面

##### 步骤二：点击实名认证入口

##### 步骤三：检查系统响应

##### ER-预期结果：1：显示实名服务暂不可用的提示；2：提供稍后重试的选项；3：不影响其他已实名用户的批量签功能；4：服务恢复后功能自动可用；

### 数据状态异常

#### TL-用户gid状态异常时批量签处理验证

##### PD-前置条件：个人签署方未传姓名；用户gid状态异常（如被冻结）；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的链接

##### 步骤三：尝试进行实名认证或签署

##### ER-预期结果：1：系统检测到gid状态异常；2：显示相应的错误提示；3：提供解决方案指引；4：不允许继续签署操作；

#### TL-签署流程状态变更时批量签处理验证

##### PD-前置条件：个人签署方未传姓名；用户正在批量签页面；签署流程被其他操作取消；

##### 步骤一：在批量签页面停留

##### 步骤二：其他用户取消相关签署流程

##### 步骤三：尝试提交批量签署

##### ER-预期结果：1：系统检测到流程状态变更；2：及时更新批量签页面状态；3：移除已取消的流程；4：显示流程状态变更提示；

## 边界测试

### 极限场景

#### TL-大量未传姓名流程批量签处理验证

##### PD-前置条件：创建100个签署流程；所有流程个人签署方均未传姓名；用户未实名；

##### 步骤一：获取批量签链接

##### 步骤二：访问批量签页面

##### 步骤三：完成实名认证

##### 步骤四：尝试批量签署所有流程

##### ER-预期结果：1：页面能正常加载大量流程；2：实名认证流程不受影响；3：批量签署功能正常执行；4：系统性能表现良好；

#### TL-实名认证并发场景处理验证

##### PD-前置条件：同一用户在多个设备上同时进行实名认证；

##### 步骤一：设备A开始实名认证流程

##### 步骤二：设备B同时开始实名认证流程

##### 步骤三：两个设备同时提交实名信息

##### 步骤四：检查最终实名状态

##### ER-预期结果：1：系统正确处理并发实名请求；2：避免重复实名或数据冲突；3：最终实名状态一致；4：两个设备都能正常使用批量签功能；

### 时序场景

#### TL-实名认证过程中签署流程过期处理

##### PD-前置条件：个人签署方未传姓名；签署流程即将过期；用户正在实名认证；

##### 步骤一：开始实名认证流程

##### 步骤二：在认证过程中签署流程过期

##### 步骤三：完成实名认证

##### 步骤四：尝试进行批量签署

##### ER-预期结果：1：实名认证正常完成；2：系统检测到流程已过期；3：在批量签页面显示流程过期提示；4：移除过期流程，不影响其他有效流程；

## 接口测试

### 获取个人批量签链接接口

#### TL-POST接口flowId-individual-realNameWill-accountId-identifyUrl未传姓名场景验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；用户有oid；

##### 步骤一：构造POST请求，URL为{flowId}/individual/realNameWill/{accountId}/identifyUrl

##### 步骤二：设置请求头Content-Type为application/json

##### 步骤三：传入batchSerialId参数（可选）

##### 步骤四：发送请求并获取响应

##### ER-预期结果：1：HTTP状态码为200；2：响应包含type字段（0表示实名）；3：响应包含bizId、realNameFlowId字段；4：响应包含url和shortUrl字段；5：所有字段值非空且格式正确；

#### TL-不同实名状态用户接口返回type字段验证

##### PD-前置条件：准备不同实名状态的用户（未实名、实名中、已实名）；

##### 步骤一：分别使用不同状态用户调用接口

##### 步骤二：检查返回的type字段值

##### 步骤三：验证type字段与实名状态的对应关系

##### ER-预期结果：1：未实名用户返回type=0（实名）；2：实名中用户返回type=0（实名）；3：已实名用户返回type=1（意愿）；4：type字段准确反映用户状态；

#### TL-batchSerialId参数对接口行为影响验证

##### PD-前置条件：个人签署方未传姓名；准备有效的batchSerialId；

##### 步骤一：不传batchSerialId调用接口

##### 步骤二：传入有效batchSerialId调用接口

##### 步骤三：传入无效batchSerialId调用接口

##### 步骤四：对比三种情况的接口响应

##### ER-预期结果：1：不传batchSerialId接口正常返回；2：传入有效batchSerialId功能正常；3：传入无效batchSerialId返回相应错误；4：batchSerialId用于获取签署操作人信息；

### 接口异常处理

#### TL-accountId不存在时接口异常处理验证

##### PD-前置条件：准备不存在的accountId；

##### 步骤一：使用不存在的accountId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回明确的错误信息；3：错误信息指出accountId不存在；

#### TL-flowId无效时接口异常处理验证

##### PD-前置条件：准备无效的flowId；

##### 步骤一：使用无效flowId调用接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：接口返回4xx错误状态码；2：返回签署流程不存在的错误信息；

## 兼容性测试

### 原有功能兼容性

#### TL-个人签署方已传姓名流程兼容性验证

##### PD-前置条件：已创建签署流程；个人签署方已指定姓名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：执行完整的批量签署流程

##### ER-预期结果：1：接口调用成功；2：批量签页面正常展示；3：签署流程正常执行；4：功能与原有流程完全一致；

#### TL-混合场景兼容性验证

##### PD-前置条件：批量签包含多种情况：个人已传姓名、个人未传姓名、企业签署等；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：分别处理不同类型的签署方

##### 步骤四：执行批量签署

##### ER-预期结果：1：接口正常返回；2：页面正确展示各类型签署方；3：未传姓名的个人签署方可正常实名；4：已传姓名的签署方功能不受影响；5：批量签署正常执行；

## 性能测试

### 实名认证性能

#### TL-大量用户同时实名认证性能验证

##### PD-前置条件：准备100个未实名用户；所有用户个人签署方均未传姓名；

##### 步骤一：100个用户同时获取批量签链接

##### 步骤二：同时访问批量签页面

##### 步骤三：同时进行实名认证

##### 步骤四：记录性能指标

##### ER-预期结果：1：接口响应时间小于3秒；2：页面加载时间小于5秒；3：实名认证成功率达到99%以上；4：系统资源使用正常；

#### TL-批量签页面加载性能验证

##### PD-前置条件：用户有大量未传姓名的签署流程；用户已实名；

##### 步骤一：访问包含大量流程的批量签页面

##### 步骤二：记录页面加载时间

##### 步骤三：测试页面交互响应时间

##### ER-预期结果：1：页面初始加载时间小于5秒；2：印章选择响应时间小于1秒；3：批量签署提交响应时间小于3秒；4：页面操作流畅无卡顿；

## 安全测试

### 实名认证安全

#### TL-实名认证数据传输安全验证

##### PD-前置条件：个人签署方未传姓名；用户进行实名认证；

##### 步骤一：监控实名认证过程中的数据传输

##### 步骤二：检查敏感信息加密情况

##### 步骤三：验证数据传输协议

##### ER-预期结果：1：身份证号等敏感信息加密传输；2：使用HTTPS协议；3：实名认证token具有时效性；4：不在URL中暴露敏感信息；

#### TL-跨用户实名信息隔离验证

##### PD-前置条件：多个用户同时进行实名认证；

##### 步骤一：用户A开始实名认证

##### 步骤二：用户B同时开始实名认证

##### 步骤三：检查两用户的实名信息是否隔离

##### ER-预期结果：1：用户A无法看到用户B的实名信息；2：实名认证流程完全隔离；3：gid生成正确对应各自用户；4：印章生成基于正确的实名信息；

### 权限安全

#### TL-未授权用户访问批量签链接安全验证

##### PD-前置条件：个人签署方未传姓名的签署流程；无权限用户；

##### 步骤一：无权限用户尝试获取批量签链接

##### 步骤二：尝试直接访问批量签页面URL

##### ER-预期结果：1：接口返回401或403错误；2：页面访问被拦截；3：返回权限不足提示；4：记录安全审计日志；

## 冒烟测试用例

### 核心功能验证

#### MYTL-个人签署方未传姓名批量签基本流程验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证

##### 步骤四：选择个人印章并提交签署

##### ER-预期结果：1：成功获取批量签链接；2：正常进入批量签页面；3：实名认证流程完整；4：签署执行成功；

#### MYTL-个人已实名未传姓名批量签验证

##### PD-前置条件：已创建签署流程；个人签署方未指定姓名；用户已实名；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：访问批量签页面

##### 步骤三：选择个人印章并签署

##### ER-预期结果：1：接口调用成功；2：直接进入印章选择页面；3：批量签署正常执行；

#### MYTL-实名认证失败重试基本验证

##### PD-前置条件：个人签署方未传姓名；用户未实名；

##### 步骤一：访问批量签页面

##### 步骤二：实名认证失败

##### 步骤三：重新进行实名认证成功

##### ER-预期结果：1：失败后提供重试入口；2：重试流程正常；3：最终实名成功可正常签署；

#### MYTL-POST接口基本调用验证

##### PD-前置条件：已创建签署流程；个人签署方未传姓名；

##### 步骤一：调用个人批量签链接接口

##### 步骤二：检查接口响应

##### ER-预期结果：1：HTTP状态码为200；2：返回必要字段；3：字段值格式正确；

#### MYTL-原有传姓名流程兼容性验证

##### PD-前置条件：个人签署方已指定姓名的签署流程；

##### 步骤一：执行完整批量签流程

##### ER-预期结果：1：功能完全正常；2：与原有流程一致；

## 线上验证用例

### 核心业务流程验证

#### PATL-个人签署方未传姓名完整业务流程线上验证

##### PD-前置条件：线上环境；已创建真实签署流程；个人签署方未指定姓名；用户未实名；

##### 步骤一：调用线上获取批量签链接接口

##### 步骤二：访问返回的批量签链接

##### 步骤三：完成个人实名认证流程

##### 步骤四：选择个人印章并提交批量签署

##### 步骤五：验证签署结果和流程状态

##### ER-预期结果：1：接口调用成功返回有效链接；2：批量签页面正常展示；3：个人实名认证流程完整可用；4：印章选择功能正常；5：签署执行成功且状态正确更新；

#### PATL-多oid对应同一gid场景线上验证

##### PD-前置条件：线上环境；同一用户的多个oid账号；已完成实名认证；

##### 步骤一：使用不同oid账号获取批量签链接

##### 步骤二：验证印章共享机制

##### 步骤三：执行批量签署

##### ER-预期结果：1：不同oid账号都能正常获取链接；2：印章基于gid正确共享；3：签署功能完全正常；

#### PATL-实名认证服务异常恢复线上验证

##### PD-前置条件：线上环境；个人签署方未传姓名；

##### 步骤一：在实名服务异常时访问批量签页面

##### 步骤二：等待服务恢复

##### 步骤三：完成实名认证和签署

##### ER-预期结果：1：异常时显示友好提示；2：服务恢复后功能自动可用；3：整体流程完整可用；

#### PATL-接口兼容性线上验证

##### PD-前置条件：线上环境；包含已传姓名和未传姓名的混合场景；

##### 步骤一：执行完整的批量签流程

##### 步骤二：验证各种场景的兼容性

##### ER-预期结果：1：新功能不影响原有流程；2：混合场景处理正确；3：接口响应格式一致；4：业务逻辑兼容性良好；
